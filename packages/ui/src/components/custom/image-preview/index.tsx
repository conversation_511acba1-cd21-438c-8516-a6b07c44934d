import { ImageToolbar, type ImageToolbarProps } from '@repo/ui/components/custom/image-toolbar';
import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { useIsSmallScreen } from '@repo/ui/hooks/useIsSmallScreen';
import { cn } from '@repo/ui/lib/utils';
import { Image as AntdImage } from 'antd';
import { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { Blurhash } from '../blurhash';

const { PreviewGroup } = AntdImage;

type ImgRenderProps = React.ComponentPropsWithoutRef<'img'> & {
  ref?: React.Ref<HTMLImageElement>;
  isLoading?: boolean;
};

export interface ImagePreviewProps extends ImageToolbarProps {
  src: string;
  showLoadingOnError?: boolean;
  alt?: string;
  width?: number | string;
  height?: number | string;
  quality?: 'low' | 'medium' | 'high' | 'auto';
  imageList?: string[];
  currentIndex?: number;
  rounded?: boolean;
  blurhash?: string;
  maxWidth?: string;
  classNames?: {
    image?: string;
    wrapper?: string;
  };
  isNewBoard?: boolean;
  onImageEdit?: (data: {
    image_url: string;
    original_image_url: string;
    blurhash: string;
    width: number;
    height: number;
    prompt: string;
  }) => Promise<void>;
  editRender?: (props: { src: string; alt: string }) => React.ReactNode;
  isSaving?: boolean;
  onInsert?: (e: React.MouseEvent) => void;
  onSave?: (e: React.MouseEvent) => void;
  insertToThoughtEnabled?: boolean;
  saveEnabled?: boolean;
  editEnabled?: boolean;
  // 编辑状态控制回调
  onEditStart?: () => void;
  onEditEnd?: () => void;
  isEditing?: boolean;
  isProcessing?: boolean;
  imgRender?: (props: ImgRenderProps) => React.ReactNode;
}

export const ImagePreview = ({
  src: outerSrc,
  alt = '',
  width,
  height,
  quality,
  imageList,
  currentIndex = 0,
  showLoadingOnError = false,
  rounded = true,
  blurhash,
  classNames,
  isNewBoard = false,
  onImageEdit,
  editRender,
  imgRender,
  isSaving = false,
  onInsert,
  onSave,
  insertToThoughtEnabled = false,
  saveEnabled = false,
  editEnabled = false,
  onEditStart,
  onEditEnd,
  isEditing = false,
  isProcessing = false,
  ...rest
}: ImagePreviewProps) => {
  // 基础状态
  const [isLoading, setIsLoading] = useState(true);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [internalCurrentIndex, setInternalCurrentIndex] = useState(currentIndex);
  const [src, setSrc] = useState(outerSrc);

  // DOM引用
  const imgRef = useRef<HTMLImageElement | null>(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);
  const doubleClickTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { isSmallScreen } = useIsSmallScreen();

  // 同步外部 currentIndex 变化到内部状态
  useEffect(() => {
    setInternalCurrentIndex(currentIndex);
  }, [currentIndex]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (doubleClickTimeoutRef.current) {
        clearTimeout(doubleClickTimeoutRef.current);
        doubleClickTimeoutRef.current = null;
      }
    };
  }, []);

  useLayoutEffect(() => {
    setSrc(outerSrc);
  }, [outerSrc]);

  // 最大化预览图片
  const onMaximizeImage = () => {
    setPreviewVisible(true);
  };

  // 处理点击事件，实现单击放大
  const handleClick = () => {
    // 编辑模式下不响应点击事件
    if (isEditing) return;
    // 直接触发放大功能，不需要检测双击
    onMaximizeImage();
  };

  const handleEdit = () => {
    onEditStart?.();
  };

  // 计算布局尺寸（用于容器与占位符）
  const aspectRatio =
    width && height && typeof width === 'number' && typeof height === 'number'
      ? width / height
      : undefined;
  const maxWidthStyle = typeof width === 'number' ? `min(${width}px, 100%)` : width || '100%';
  const minHeightStyle = typeof height === 'number' ? height : 120;

  const shouldImageAnonymous = useMemo(() => {
    // 微信图片
    if (src.includes('mmbiz.qpic.cn')) {
      return true;
    }
    return false;
  }, [src]);

  // 渲染图片组件
  return (
    <div className={cn('relative flex w-full justify-center', classNames?.wrapper)}>
      <div
        className={cn(
          'node-image-container group relative flex items-center justify-center',
          rounded && 'rounded-lg',
        )}
        style={{
          width: width || '100%',
          aspectRatio: aspectRatio,
          maxWidth: maxWidthStyle,
          // 当未知尺寸时，确保加载阶段有最小高度占位
          minHeight: isLoading ? minHeightStyle : undefined,
        }}
        onClick={handleClick}
        ref={imageContainerRef}
      >
        {/* 图片渲染（可通过 imgRender 自定义，默认使用标准 img 渲染） */}
        {(() => {
          const defaultImgProps: ImgRenderProps = {
            src,
            alt,
            ref: imgRef,
            className: cn('h-auto w-full', classNames?.image, !isEditing && 'cursor-zoom-in'),
            draggable: true,
            crossOrigin: shouldImageAnonymous ? 'anonymous' : undefined,
            onLoad: () => {
              setIsLoading(false);
            },
            onError: (_e) => {
              setIsLoading(false);
              if (showLoadingOnError) {
                setSrc('https://cdn.gooo.ai/assets/skeleton_breath.gif');
              }
            },
          };
          if (typeof imgRender === 'function') {
            return imgRender(defaultImgProps);
          }
          const { ref: _ignoredRef, ...restImgProps } = defaultImgProps;
          return <img ref={imgRef} {...restImgProps} />;
        })()}

        {/* 加载占位符覆盖在图片上方，保证样式与占位一致 */}
        {isLoading && (
          <div className={cn('absolute inset-0 pointer-events-none', rounded && 'rounded-lg')}>
            {blurhash ? (
              <Blurhash
                metadata={{
                  width: (width as number) || 0,
                  height: (height as number) || 0,
                  blurhash,
                }}
              />
            ) : (
              <div className="w-full h-full bg-gray-100">
                <div className="absolute inset-0 flex min-h-[120px] w-full flex-col items-center justify-center gap-3">
                  <div className="dot-pulse" />
                </div>
              </div>
            )}
          </div>
        )}

        {/* 图片预览组 */}
        {!isEditing && (
          <PreviewGroup
            items={imageList ?? [src]}
            preview={{
              onVisibleChange: (visible) => {
                setPreviewVisible(visible);
              },
              visible: previewVisible,
              current: internalCurrentIndex,
              onChange: (current) => {
                setInternalCurrentIndex(current);
              },
              rootClassName: 'preview-white-background',
            }}
          />
        )}

        {isEditing &&
          editRender &&
          editRender({
            src,
            alt,
          })}

        {isProcessing && (
          <div className="absolute z-[100] flex h-full w-full items-center justify-center bg-snip-card">
            <SimpleLoading className="w-10 h-10" />
          </div>
        )}

        {/* 图片工具栏 */}
        {isSmallScreen || isEditing ? null : (
          <ImageToolbar
            {...rest}
            src={src}
            type="png"
            alt={alt}
            isSaving={isSaving}
            isNewBoard={isNewBoard}
            insertToThoughtEnabled={insertToThoughtEnabled}
            saveEnabled={saveEnabled}
            editEnabled={editEnabled}
            onCopyLink={(e) => {
              e.stopPropagation();
            }}
            onCopyToClipboard={(e) => {
              e.stopPropagation();
            }}
            onDownload={(e) => {
              e.stopPropagation();
            }}
            onInsert={(e) => {
              e.stopPropagation();
              onInsert?.(e);
            }}
            onSave={(e) => {
              e.stopPropagation();
              onSave?.(e);
            }}
            onEdit={(e) => {
              e.stopPropagation();
              handleEdit();
            }}
          />
        )}
      </div>
    </div>
  );
};
