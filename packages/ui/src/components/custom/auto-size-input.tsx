import { cn } from '@repo/ui/lib/utils';
import { forwardRef, useImperativeHandle, useRef } from 'react';

export type AutoSizeInputProps = {
  placeholder: string;
  value?: string;
  onInput?: (val: string) => void;
  onChange?: (val: string) => void;
  className?: string;
};

export interface AutoSizeInputRef {
  focus: () => void;
  blur: () => void;
  getValue: () => string;
  setValue: (value: string) => void;
  getIsFocused: () => boolean;
}

export const AutoSizeInput = forwardRef<AutoSizeInputRef, AutoSizeInputProps>(
  ({ placeholder, value, onInput, onChange, className }, ref) => {
    const titleELRef = useRef<HTMLDivElement>(null);

    useImperativeHandle(
      ref,
      () => ({
        focus: () => {
          titleELRef.current?.focus();
        },
        blur: () => {
          titleELRef.current?.blur();
        },
        getValue: () => {
          return titleELRef.current?.textContent || '';
        },
        setValue: (newValue: string) => {
          if (titleELRef.current) {
            titleELRef.current.textContent = newValue;
          }
        },
        getIsFocused: () => {
          return titleELRef.current?.matches(':focus') || false;
        },
      }),
      [],
    );

    const handleOnInput = (event: React.FormEvent<HTMLDivElement>) => {
      const currentValue = event.currentTarget.textContent || '';
      onInput?.(currentValue);
      onChange?.(currentValue);
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
      if (event.key === 'Enter') {
        event.preventDefault();
      }
    };

    return (
      <div
        spellCheck={false}
        data-placeholder={placeholder}
        className={cn(
          'p-0 cursor-text w-full h-auto text-2xl bg-transparent border-none outline-none font-[600] min-h-10 text-[rgba(0,0,0,0.88)] placeholder:text-[rgba(0,0,0,0.24)] empty:before:text-[rgba(0,0,0,0.24)] empty:before:content-[attr(data-placeholder)]',
          className,
        )}
        ref={titleELRef}
        onKeyDown={handleKeyDown}
        onInput={handleOnInput}
        contentEditable
        suppressContentEditableWarning
        dangerouslySetInnerHTML={value ? { __html: value } : undefined}
      />
    );
  },
);

AutoSizeInput.displayName = 'AutoSizeInput';
