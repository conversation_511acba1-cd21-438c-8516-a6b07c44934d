'use client';

import clsx from 'clsx';
import React, { useEffect, useMemo, useRef } from 'react';

import './index.css';
import { useSnipContext } from '../../context';
import { deleteImgParentP } from '../../utils/html';

export interface ArticleContentProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  extractTimestamp?: boolean;
  source: string;
  type?: 'chat' | 'article';
  onHashLinkClick?: (href: string) => void;
}

const MemoizedContent = React.memo(
  React.forwardRef<HTMLDivElement, { source: string; type: 'chat' | 'article' }>(
    ({ source, type }, ref) => {
      const containerRef = useRef<HTMLDivElement>(null);
      const { components } = useSnipContext();
      const ImagePreview = components.ImagePreview;

      useEffect(() => {
        if (containerRef.current) {
          const links = containerRef.current.getElementsByTagName('a');
          Array.from(links).forEach((link) => link.setAttribute('target', '_blank'));
        }
      }, []);

      const cleanSource = useMemo(() => {
        return deleteImgParentP(source);
      }, [source]);

      // 解析HTML并替换img标签为ImagePreview组件
      const processedContent = useMemo(() => {
        // 如果环境不支持DOM，如服务器端渲染环境
        if (typeof document === 'undefined') {
          return (
            <div
              className={clsx('ym-reader-content text-foreground', {
                'chat-content': type === 'chat',
              })}
              dangerouslySetInnerHTML={{
                __html: cleanSource,
              }}
            />
          );
        }

        try {
          // 正则表达式匹配图片标签
          const imgRegex = /<img\b[^>]*>/gi;

          // 如果没有图片，直接返回原始HTML
          if (!imgRegex.test(cleanSource)) {
            return (
              <div
                className={clsx('ym-reader-content text-foreground', {
                  'chat-content': type === 'chat',
                })}
                dangerouslySetInnerHTML={{
                  __html: cleanSource,
                }}
              />
            );
          }

          // 使用更简单的方法：一次解析出图片与分隔的文本片段
          const tempHtml = cleanSource;
          const imageMatches: Array<{
            src: string;
            alt: string;
            width?: number | string;
            height?: number | string;
          }> = [];
          const htmlSegments: string[] = [];
          const allImageSources: string[] = [];

          // 提取属性的工具函数（支持双引号、单引号和无引号）
          const extractAttr = (tag: string, attr: string): string => {
            const re = new RegExp(`${attr}\\s*=\\s*(\"([^\"]*)\"|'([^']*)'|([^\\s\"'>]+))`, 'i');
            const m = tag.match(re);
            if (!m) return '';
            return (m[2] ?? m[3] ?? m[4] ?? '') as string;
          };

          let lastIndex = 0;

          // 预先收集所有图片 src
          imgRegex.lastIndex = 0;
          imgRegex.lastIndex = 0;
          let execMatch: RegExpExecArray | null = imgRegex.exec(tempHtml);
          while (execMatch !== null) {
            // 收集图片前的文本片段
            const beforeImg = tempHtml.substring(lastIndex, execMatch.index);
            htmlSegments.push(beforeImg);

            // 解析图片属性
            const imgTag = execMatch[0];
            const src = extractAttr(imgTag, 'src') || '';
            const alt = extractAttr(imgTag, 'alt') || '';
            const widthAttr = extractAttr(imgTag, 'width') || '';
            const heightAttr = extractAttr(imgTag, 'height') || '';

            const width = widthAttr
              ? widthAttr.includes('%')
                ? (widthAttr as unknown as string)
                : parseInt(widthAttr, 10)
              : undefined;
            const height = heightAttr
              ? heightAttr.includes('%')
                ? (heightAttr as unknown as string)
                : parseInt(heightAttr, 10)
              : undefined;

            allImageSources.push(src);
            imageMatches.push({ src, alt, width, height });

            lastIndex = execMatch.index + execMatch[0].length;
            execMatch = imgRegex.exec(tempHtml);
          }

          // 添加最后一段HTML
          const afterLastImg = tempHtml.substring(lastIndex);
          htmlSegments.push(afterLastImg);

          // 渲染：片段与图片交替输出
          const content: React.ReactNode[] = [];
          for (let i = 0; i < imageMatches.length; i++) {
            const before = htmlSegments[i];
            if (before?.trim()) {
              content.push(
                <div
                  key={`content-${content.length}`}
                  className={clsx('ym-reader-content text-foreground', {
                    'chat-content': type === 'chat',
                  })}
                  dangerouslySetInnerHTML={{ __html: before }}
                />,
              );
            }
            const img = imageMatches[i]!;
            content.push(
              <ImagePreview
                key={`img-${i}`}
                src={img.src}
                alt={img.alt}
                width={img.width}
                height={img.height}
                imageList={allImageSources}
                currentIndex={i}
                showLoadingOnError
                insertToThoughtEnabled={false}
              />,
            );
          }
          const tail = htmlSegments[htmlSegments.length - 1];
          if (tail?.trim()) {
            content.push(
              <div
                key={`content-${content.length}`}
                className={clsx('ym-reader-content text-foreground', {
                  'chat-content': type === 'chat',
                })}
                dangerouslySetInnerHTML={{ __html: tail }}
              />,
            );
          }

          return <>{content}</>;
        } catch (error) {
          console.error('Error processing HTML content:', error);
          // 出错时回退到原始渲染
          return (
            <div
              className={clsx('ym-reader-content text-foreground', {
                'chat-content': type === 'chat',
              })}
              dangerouslySetInnerHTML={{
                __html: cleanSource,
              }}
            />
          );
        }
      }, [cleanSource, type, ImagePreview]);

      return (
        <div ref={containerRef}>
          <div
            ref={ref}
            className={clsx('ym-reader-content-container', {
              'chat-content-container': type === 'chat',
            })}
          >
            {processedContent}
          </div>
        </div>
      );
    },
  ),
);
MemoizedContent.displayName = 'MemoizedContent';

export const ArticleContent = React.forwardRef<HTMLDivElement, ArticleContentProps>(
  ({ source, type = 'article', onHashLinkClick, ...props }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (containerRef?.current) {
        containerRef.current.addEventListener('click', (e) => {
          const targetLink = (e.target as HTMLElement)?.closest('a');
          if (!targetLink) return;
          if (targetLink.getAttribute('href')?.startsWith('#')) {
            e.preventDefault();
            onHashLinkClick?.(targetLink.getAttribute('href')!);
            return;
          }
          e.preventDefault();
          window.open(targetLink.href, '_blank');
        });
      }
    }, [onHashLinkClick]);

    const { title: _omitTitle, ...propsWithoutTitle } = props;

    return (
      <div className="ym-reader-container relative" ref={ref} {...propsWithoutTitle}>
        <MemoizedContent ref={containerRef} source={source} type={type} />
      </div>
    );
  },
);

ArticleContent.displayName = 'ArticleContent';
