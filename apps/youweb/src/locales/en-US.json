{"LocaleLayout": {"title": "YouMind - AI Creation Studio", "description": "YouMind is an innovative creation studio that harnesses the power of generative AI to transform diverse materials into inspired creations.", "keywords": "ChatGPT, chatgpt api , YouMind"}, "Navigation": {"library": "Library", "board": "Boards", "discover": "Discover"}, "Sidebar": {"newBoard": "New board", "search": "Search", "searchTooltip": "Search in YouMind", "boards": "Boards", "snips": "<PERSON><PERSON><PERSON>", "thoughts": "Thoughts", "newSnip": "New snip", "installExtension": "Install now", "getBrowserExtension": "Get Browser Extension", "askAndSave": "Ask and save anything while you browse", "favorite": "Favorite"}, "Thought": {"MagicCreation": {"title": "Magic Creation", "howHelp": "How can I helps you in creation?", "detailDescription": "Help me create", "untitled": "Untitled", "importItems": "Import items"}, "EditableThought": {"placeholder": "Add new thought"}, "Code": {"language": "Language", "placeholder": "Search for a language...", "notFound": "No language found.", "noDiagramType": "No diagram type detected matching given configuration for text."}}, "MindWorkspace": {"openMindStudio": "Open Mind Studio", "closeMindStudio": "Close Mind Studio", "assistants": "Assistants", "newAssistant": "New Assistant", "openBoardSidebar": "Open Catalog Sidebar", "closeBoardSidebar": "Close Catalog Sidebar", "create": "Create"}, "Library": {"Home": {"placeholder": "Ask AI anything...", "createNewThought": "Create new thought", "browseFiles": "Browse files here to upload", "snip": "<PERSON><PERSON><PERSON>", "askAi": "Ask AI", "fileExists": "File already exists.", "uploadFileFailed": "Upload file failed."}, "Search": {"dialogTitle": "Search Dialog", "placeholder": "Search", "noData": "No results found.", "chooseDate": "Date", "recent": "Recents", "clearAll": "Clear all", "filter": {"all": "All", "snips": "<PERSON><PERSON><PERSON>", "thoughts": "Thoughts"}, "lastUpdated": "Last updated", "thought": "Thought", "snippet": "Snippet", "pdf": "PDF", "image": "Image", "fromOverview": "From Overview", "fromExtractedText": "From Extracted Text", "in": "in: "}, "ThoughtList": {"unused": "Unsorted", "all": "All", "inProgress": "In progress", "inProgressEmptyTitle": "Let's start creating by adding thoughts to a board", "inProgressEmptyDescription": "Save hours on drafting your article using board, and stay in flow with your research materials at your fingertips.", "goToBoard": "Go to Boards", "newThought": "Thought"}, "SnipList": {"unused": "Unsorted", "all": "All", "webpages": "Webpages", "images": "Images", "files": "Files", "snippets": "Snippets", "videos": "Videos", "thoughts": "Thoughts", "audios": "Audios", "newSnip": "<PERSON><PERSON><PERSON>", "youtube": "YouTube", "medium": "Medium", "podcasts": "Podcasts", "wikipedia": "Wikipedia", "createSnipSuccess": "<PERSON><PERSON><PERSON> added.", "clickToUpload": "Click to upload", "dragNDrop": "Drag&Drop or ", "chooseFiles": "Choose files", "dropFiles": "Drop files here", "acceptedFileTypes": "Supported: text, images, audio, PDFs, and Office documents", "maxFileSize": "Max file size", "uploadFile": " to upload", "importFromURL": "Or add links", "newThought": "Thought", "createSnipThoughtSuccess": "Thought created.", "pasteLink": "Click to paste the link", "pasteLinkPlaceholder": "Paste Links here, one per line.", "upload": "Upload", "uploadFailed": "Upload failed.", "fileSizeTooLarge": "File size too large.", "fileSizeTooLargeMaxSize": "File is too large. Max size is ", "maxFilesExceeded": "You can select up to {{max}} files", "clipboardReadPermissionDenied": "Clipboard read permission denied.", "recently": "Recently", "boards": "Boards", "allSnips": "<PERSON><PERSON><PERSON>", "addToYouMind": "Add to YouMind", "addSnips": "Add snips", "createBoard": "Create Board", "snippet": "Snippet", "allUsed": "All clear.", "allUsedDescription": "Enjoy a little moment to yourself.", "maxUrlsExceeded": "Maximum URL limit exceeded ({{max}})", "invalidUrlFormat": "Invalid URL format detected. Please check your input.", "inProgress": "In progress", "inProgressEmptyTitle": "Let's start creating by adding snips to a board", "inProgressEmptyDescription": "Save hours on drafting your article using board, and stay in flow with your research materials at your fingertips.", "goToBoard": "Go to Boards", "delete": "Delete", "saving": "Saving...", "analyzing": "Analyzing...", "add": "Add", "fetchFailed": "Failed to fetch this URL."}, "ListEmpty": {"everyThingClear": "All clear.", "everyThingSortedDescription": "Let's start something new.", "noContent": "Nothing here.", "noContentDescription": "Newly saved {type}s will appear here."}, "ThoughtDetail": {"deleteSuccess": "You've deleted this from your library", "delete": "Delete", "moveTo": "Move to...", "choose": "<PERSON><PERSON>"}, "SnipDetail": {"overview": "Overview", "note": "Note", "transcript": "Transcript", "images": "Images", "createdBy": "Created by ", "delete": "Delete", "visitSite": "Open the original site", "moveTo": "Move to...", "shareLink": "Share link", "deleteSuccess": "You've deleted this from your library", "savedToLibrary": "Saved to library: ", "lastUpdated": "Last updated", "PDFSource": {"failedToLoad": "Failed to load PDF file.", "noData": "No page specified."}, "saveNoteFailed": "Failed to save note.", "collectedOn": "Saved on", "openSource": "Open source", "duplicate": "Duplicate", "minutes": " min", "update": "Update", "choose": "<PERSON><PERSON>", "autoChoose": "Auto choose", "allBoards": "All boards", "newBoard": "Create new board", "noBoard": "No board", "unsorted": "Unsorted", "updateRelativeBoardSuccess": "Relative board updated.", "aiChat": "AI chat", "relatedSnips": "Related snips", "relatedThoughts": "Related thoughts", "noRelatedSnips": "No related snips.", "noRelatedThoughts": "No related thoughts.", "noTranscriptOverview": "Overview is not supported yet. Please generate the transcript first.", "noOverview": "Overview is unavailable for this snip.", "edit": "Edit", "editSnip": "<PERSON>", "title": "Title", "save": "Save", "titlePlaceholder": "Title", "showMore": "...Show more", "more": "...more", "showLess": "Show less", "extractText": {"title": "Extract text", "failed": "Failed to extract text", "clickToExtract": "Click to Extract", "noTextFound": "No text found"}, "info": "Info", "properties": "Properties", "dimensions": "Dimensions", "size": "Size", "type": "Type", "dateCreated": "Date created", "cancel": "Cancel", "done": "Done", "saveFailed": "Failed to save", "imageTitlePlaceholder": "Title", "imageDescriptionPlaceholder": "Description", "titleRequired": "Title is required", "generateFailed": "Failed to generate", "share": "Share", "shareToTheWeb": "Share to the web", "anyoneWithLinkCanAccess": "Anyone with link can access.", "copyLink": "Copy link", "linkCopied": "Link copied!", "podcast": {"speaker": "Speaker", "multipleSpeakers_zero": "No speakers", "multipleSpeakers_one": "Detected 1 speaker", "multipleSpeakers_other": "Detected {{speakerCount}} speakers", "enhanceText": "Enhance the transcript for punctuation, paragraphs, and speaker grouping.", "enhance": "<PERSON><PERSON>ce"}}, "Board": {"title": "Boards", "recentlyReviewed": "Recently viewed", "allBoards": "All boards", "createBoardShort": "Board", "createBoard": "Create board", "editBoard": "Edit board", "back": "Back", "submit": "Create", "update": "Update", "cancel": "Cancel", "edit": "Edit", "pin": "<PERSON>n", "unpin": "Unpin", "pinned": "Pinned", "delete": "Delete", "name": "Icon & Name", "description": "Description", "previews": "Previews", "createdSuccess": "Board created", "updatedSuccess": "Board updated", "namePlaceholder": "Board name", "icon": "Icon", "deleteSuccess": "Board deleted", "pinSuccess": "Board pinned", "unpinSuccess": "Board unpinned", "snips": "<PERSON><PERSON><PERSON>", "thoughts": "Thoughts", "aiChats": "AI chats", "all": "All", "archive": "Archive", "unarchive": "Unarchive", "archived": "archived", "unarchived": "unarchived", "archivedLongDesc": "{{board<PERSON>ame}} has been archived. It can be found in the Board list.", "unarchivedLongDesc": "{{board<PERSON>ame}} has been unarchived. It can be found in the Board list.", "archivedSimpleDesc": "{{boardName}} has been archived", "unarchivedSimpleDesc": "{{boardName}} has been unarchived", "boardDescription": "YouMind helps you to build your own creative board", "rename": "<PERSON><PERSON>", "save": "Save", "add": "Add", "addToBoard": "Add to board", "addMaterialSuccess": "Successfully imported to board", "removeMaterialSuccess": "Successfully removed from board", "allDone": "All done", "allDoneTips": "Feels good to complete your creation.", "noBoard": "No boards here.", "noBoardTips": "When you finish a board, it will appear here.", "noContent": "Nothing here.", "noContentOfPanel": "Nothing here.", "addContentTips": "Activate the panel and add in or create snips", "image": "Image", "lastUpdated": "Last updated", "moveRight": "Move right", "moveLeft": "Move left", "openSource": "Open source", "duplicate": "Duplicate", "copyId": "Copy ID", "pinToDesk": "Pin to desk", "unpinFromDesk": "Unpin from desk", "focusMode": "Focus mode", "share": "Share", "sharing": "Sharing", "deleteLimitTips": "You must have at least one board", "createBoardGroup": "New group", "editBoardGroup": "Edit group"}, "Boards": {"boards": "Boards", "board": "Board", "boardsDescription": "YouMind helps you to build your own creative board.", "onDesk": "On desk"}, "BoardOverview": {"home": "Home", "list": "List", "openSplitView": "Open split view", "closeSplitView": "Close split view", "navigateUp": "Navigate up", "navigateDown": "Navigate down", "subtitle": "Welcome! Add your materials and let's create something amazing.", "snips": "<PERSON><PERSON><PERSON>", "snip": "<PERSON><PERSON><PERSON>", "thought": "Thought", "thoughts": "Thoughts", "aiChats": "AI chats", "aiChat": "AI chat", "groups": "Groups", "group": "Group", "board": "Board", "all": "All", "importAll": "Import", "upload": "Upload...", "snipsDescription": "Add relevant snips to this board.", "thoughtsDescription": "Dump any thoughts to this board.", "uploadDescription": "Supported file types: audio, image", "dragNDrop": "Drag&Drop or ", "chooseFiles": "Choose files", "toThisBoard": " to this board.", "uploadFile": " to upload", "newWebpage": "New snip", "newThought": "New thought", "newSnip": "New snip", "newBoardChat": "New AI chat", "import": "Import...", "importUnsortedItems": "Import unsorted items", "importSnipsToBoard": "Import unsorted snips", "importThoughtsToBoard": "Import unsorted thoughts", "importAIChatsToBoard": "Import AI chats", "nothingHere": "Nothing here.", "noContent": "No content.", "addToBoard": "Add to board", "New": "New", "AIchat": "AI chat", "importSnips": "Import snips", "importThoughts": "Import thoughts", "importAIChats": "Import AI chats", "newGroup": "New group", "updated": "Updated", "showInAGallery": "Show in a gallery", "showInColumns": "Show in columns", "openInFullView": "Open in full view", "openInSimpleView": "Open in simple view", "nothingIsHere": "Nothing is here."}, "BoardGroup": {"newMagicCreation": "New Magic Creation", "magicCreation": "Magic Creation", "upload": "Upload", "AIchat": "AI chat", "rename": "<PERSON><PERSON>", "delete": "Delete", "import": "Import...", "newSnip": "New snip", "newThought": "New thought", "ungroup": "Ungroup", "openInSplitView": "Open in split view", "openSource": "Open source", "duplicate": "Duplicate", "moveTo": "Move to", "otherBoard": "Other board", "moveOutOfGroup": "Move out of group", "newGroup": "New group", "noGroup": "No group", "untitled": "Untitled", "snips": "<PERSON><PERSON><PERSON>", "thoughts": "Thoughts", "aiChats": "AI chats", "addMaterialSuccess": "Successfully imported to board", "edit": "Edit", "download": "Download", "copyAsMarkdown": "<PERSON><PERSON> as <PERSON><PERSON>", "copyAsPlainText": "Copy as plain text", "exportAsPDF": "Export as PDF", "exportAsImage": "Export as image"}, "AskAI": {"title": "Ask AI", "newChat": "New chat", "placeholder": "Ask anything", "hi": "Hi, {{name}}", "greetings": "What do you want to do?", "suggestion": "Suggested", "thinking": "Thinking", "thoughtFor_zero": "Thought for 0 seconds", "thoughtFor_one": "Thought for 1 second", "thoughtFor_other": "Thought for {{count}} seconds", "seconds_zero": "{{count}} seconds", "seconds_one": "{{count}} second", "seconds_other": "{{count}} seconds", "searchFor": "Searching for", "generateSVG": "Drawing illustration", "toggleOpenText": "Expand for details", "toggleCloseText": "Collapse details", "resolveLibray": "Looking up library content", "noResultsFound": "No results found", "recentChats": "Recent chats", "viewAll": "View all", "fullPageChat": "Chat in full page", "minimizeChat": "Chat in small window", "deleteChat": "Delete chat", "selectedContent": "Your selected content:", "showAll": "Show all", "edit": "Edit", "delete": "Delete", "lastUpdated": "Last updated", "editAIChat": "Edit AI chat", "titlePlaceholder": "AI Chat title", "save": "Save", "saving": "Saving", "open": "Open", "copy": "Copy", "createNewChat": "Create new chat", "snip": "<PERSON><PERSON><PERSON>", "thought": "Thought", "sources": "Sources", "noSources": "No sources.", "essence": "Essence", "noEssence": "No essence.", "showMore": "...more", "showLess": "Show less", "saveAsThought": "Save as thought", "savedAsThought": "Saved as thought.", "savedAsUnsortedSnip": "Saved as unsorted snip.", "savedToThisBoard": "Saved to this board.", "copiedToThisBoard": "Copied to this board.", "copySnipSuccess": "Snip copied successfully.", "successCopy": "Copied to clipboard.", "chatModelGroup": "Cha<PERSON>", "reasoningModelGroup": "Reasoning", "useWebSearch": "Web search", "webSearchDisabled": "Web search is not yet supported on this model", "noResults": "No results.", "chatHistory": "Chat history", "currentBoard": "Current board", "currentItem": "Current page", "all": "All", "board": "board", "youmind": "youmind", "mentionBoardDescription": "Mention all materials in this board.", "currentGroup": "The currently opened group of the item", "currentPage": "Currently opened item"}, "NewSnipTemplate": {"title": "Nothing here, try adding a new one.", "description": "Paste a link or upload files to get started.", "files": "Files", "placeholder": "Paste a link here...", "fetchFailed": "Failed to fetch this URL."}, "NewThoughtPlaceholder": {"addNewThought": "Add a new thought", "clickToStart": "Click to start your creation....", "nothingHere": "Nothing here, try adding a new one."}, "SVGEditor": {"save": "Save", "reset": "Reset"}, "ImagePreview": {"viewImage": "View image", "copyLink": "Copy link", "copyToClipboard": "Copy to clipboard", "download": "Download", "edit": "Edit", "insert": "Insert", "save": "Save"}}, "Settings": {"title": "Settings", "Account": {"title": "Account", "username": "Name", "email": "Email", "submit": "Save", "personalInfoModified": "Profile modified", "profile": "Profile", "deletion": "Deletion", "cancel": "Cancel", "delete": "Delete", "deleteAccount": "Delete your account", "deletionDescription": "Please note that when you delete an account, all data will be deleted and this operation is irreversible.", "deletionConfirm1": "The deletion cannot be undone.", "deletionConfirm2": "All your snips and boards will be deleted FOREVER.", "deletionConfirm3": "Are you sure? ", "system": "System", "activeAccount": "Active account", "signOut": "Sign out", "signOutConfirm": "Are you sure you want to sign out?", "changeName": "Change name", "apply": "Apply", "manage": "Manage", "nextBillingDate": "Next billing date", "uploadFileFailed": "Upload file failed.", "refreshInDays_zero": "Refresh in 0 days", "refreshInDays_one": "Refresh in 1 day", "refreshInDays_other": "Refresh in {{count}} days"}, "Subscriptions": {"title": "Subscriptions"}, "Appearance": {"title": "Appearance", "theme": "Theme", "system": "System", "light": "Light", "dark": "Dark"}, "Language": {"AI response languages": "AI response languages", "SearchLanguage": "Search language", "Empty": "Empty", "System": "System", "Primary": "Primary", "Secondary": "Secondary", "Enable bilingual display": "Enable bilingual display", "Auto": "Auto", "Unset": "Unset", "followContent": "Follow content", "submit": "Save", "language": "Language", "display": "Display language", "aiResponse": "AI response languages", "savedSuccessfully": "Saved successfully", "savedFailed": "Saved failed"}, "About": {"title": "About", "termsOfUse": "Terms", "privacyNotice": "Privacy", "joinUs": "🤙Join us for upcoming features and feedback", "joinWechat": "Join us on WeChat", "joinDiscord": "Join us on Discord", "followX": "Follow us on X", "feedback": "<EMAIL>", "expectation": "Let us know your expectation:", "Contact": "Contact"}, "general": "General", "Billing": {"title": "Billing"}, "SignOut": {"title": "Sign out"}}, "AudioPlayer": {"skipBack": "Skip back 15 seconds", "skipForward": "Skip forward 15 seconds", "mute": "Mute", "unmute": "Unmute", "play": "Play", "pause": "Pause", "listen": "listen", "generating": "generating...", "loading": "loading...", "expand": "Expand", "collapse": "Collapse", "locate": "Locate the current subtitle", "previous": "Previous", "next": "Next", "mode": {"sequential": "Enable repeat", "repeatOne": "Repeat one", "repeatAll": "Repeat all", "enableShuffle": "Enable shuffle"}}, "Action": {"overview": "Overview", "note": "Note", "transcript": "Transcript", "media": "Media", "exportTranscript": "Export", "transcriptCopied": "Transcript copied.", "showTimestamp": "Show timestamp", "copy": "Copy", "share": "Share", "read": "Read aloud", "moveUp": "Move up", "moveDown": "Move down", "moveToTop": "Move to top", "moveToBottom": "Move to bottom", "delete": "Delete", "theaterMode": "Theater mode", "defaultView": "Default view", "Transcript": {"title": "Transcript", "copy": "Copy", "copied": "Transcript copied", "export": "Download", "exported": "Transcript exported", "displayLineByLine": "Line view", "displayByParagraph": "Chapter view", "generate": "Generate", "regenerate": "Regenerate", "empty": "No transcript is available.", "generating": "We're working hard on the transcription, which may take over 30 minutes.", "preview": "Preview", "previewWarning": "Only enabled in DEV or PREVIEW environment.", "previewWarningDescription": "Fetch lastest transcript from youget.ai.", "upload": "Upload", "uploadText": "Upload or paste transcript generated by other tools.", "uploadTranscript": "Upload Transcript", "dragDropSrt": "Drag and drop or click to upload .srt file", "uploadSrtDescription": "Only .srt files are supported", "clipboardSrtDetected": "SRT content detected from clipboard", "invalidSrtFormat": "Invalid SRT file format", "fileReadError": "Error reading file", "noTranscriptContent": "No transcript content", "transcriptUploaded": "Transcript uploaded successfully", "transcriptUploadFailed": "Failed to upload transcript", "cancel": "Cancel", "uploading": "Uploading..."}, "Overview": {"title": "Overview", "copy": "Copy", "copied": "Overview copied"}, "Description": {"title": "Description"}, "Shownotes": {"title": "Shownotes"}, "Media": {"title": "Media", "imageTitle": "Images", "jumpToAnchor": "Jump to anchor", "openImage": "Open image", "nothingHereYet": "Nothing here yet"}, "Note": {"title": "Note", "copy": "Copy", "copied": "Note copied.", "edit": "Edit", "saving": "Saving..."}, "Explain": {"title": "Explain"}, "ReadAloud": {"title": "Read aloud", "chooseVoice": "Choose a voice", "localSpeechSynthesizerService": "Local speech synthesizer service"}, "Listen": {"title": "Listen", "generate": "Generate", "delete": "Delete", "editTitle": "Edit title", "description": "Generate a 3-minute audio in seconds.", "generating": "It'll take a few minutes. No need to stick around.", "generateLimit": "This content type is not supported yet."}}, "Auth": {"Footnote": {"desc": "By continuing, you agree to the ", "terms": "Terms", "privacy": "Privacy", "cookie": "<PERSON><PERSON>"}, "Login": {"title": "Join <PERSON>", "description": "", "action": "Continue", "actionLoading": "Sending email", "actionByOAuth": "Continue with {{provider}}", "form": {"email": {"label": "Email", "invalid": "Please enter a valid email", "placeholder": "Enter your email"}}}, "OTP": {"title": "Enter the code", "description": "We've sent email to {{email}}", "goBack": "Back", "action": "Continue", "actionLoading": "Validating code", "resend": "Resend code", "resending": "Sending", "resendSuccess": "Code sent", "form": {"token": {"label": "Token", "invalid": "Please enter the valid code"}}}, "Onboard": {"title": "Almost!", "description": "What should we call you?", "action": "Continue", "actionLoading": "Remembering your name", "form": {"name": {"label": "Name", "placeholder": "Enter your name", "invalid": "Please enter a name"}}}, "Profile": {"title": "Hi, {{name}} 👋", "description": "Tell us more to move up the waitlist.", "action": "Move up waitlist 🚀", "skip": "<PERSON><PERSON>", "form": {"content": {"label": "What type of contents have you published?", "placeholder": "Such as Vlog or Videos", "selection": "Vlog or Videos (YouTube, TikTok)\nArticles (Medium, Substack)\nPodcasts (Apple Podcast, Spotify)\nX (Twitter), Facebook, Reddit\nOnline courses or live-streaming\nStill on the way"}, "tools": {"label": "What productivity tools do you use?", "placeholder": "Such as Notion", "selection": "Notion\nEvernote\nMicrosoft 365\nApple Notes/Notepad\nChatGPT\nPerplexity\nClaude\nOthers"}, "valuation": {"label": "How much would you pay for AI tool a year?", "placeholder": "Such as $200/year", "selection": "$200/year\n$300/year\n$500/year\nI will pay any price for usefulness.\nNone, I only use the free version."}, "purpose": {"label": "How do you use AI in your work?", "placeholder": "Such as generate ideas", "selection": "To search\nTo generate ideas\nTo assist writing\nTo edit video/audio\nAI doesn't help my work\nI haven't tried AI yet"}, "challenge": {"label": "Is there anything else you'd like to tell us?", "placeholder": "Please be as specific as possible"}}}, "Waiting": {"title": "Thank you for joining our waitlist.", "description": "Great to know you, {{name}}. Just hang tight!", "action": "Join us on Discord", "logout": "Sign out"}}, "Landing": {}, "NotFoundPage": {"title": "404", "description": "Please double-check the browser address bar or use the navigation to go to a known page.", "goToHomepage": "Go to homepage"}, "InternalServerError": {"title": "500", "description": "Internal Server Error", "tryAgain": "Try again"}, "Message": {"Success": {"confirmEmailSended": "Check your email for the confirmation link.", "passwordResetEmailSended": "Check your email for the password reset link.", "updatePassword": "The password has been updated"}}, "LLMTools": {"feedback": "<PERSON><PERSON><PERSON>", "feedbackPlaceholder": "What did you find unsatisfying?", "submit": "Submit", "submitting": "Submitting...", "feedbackToast": "Thanks for your feedback.", "regenerate": "Regenerate with {{model}}", "goodResponse": "Good response", "badResponse": "Bad response", "goodFeedbackTitle": "Glad you found that helpful", "goodFeedbackDescription": "Would you like to share more details?"}, "Boards": {"newBoard": {"title": "Organize and create", "subtitle": "with AI", "newTitle": "Organize and create with board", "name": "Board name", "placeholder": "Add description...", "hi": "Hi, {{name}}", "creatingBoard": "Creating board ", "create": "Create", "whatAreYouCuriousAbout": "What are you curious about?"}}, "Transcript": {"formattingFailed": "Failed to enhance transcript. Please try again.", "detectingSpeakers": "Identifying speakers..."}, "Share": {"save": "Save", "open": "Open"}}