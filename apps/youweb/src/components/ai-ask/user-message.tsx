import {
  EditCommand,
  EditCommandTypeEnum,
  getCommandTitle,
  MessageAtReferenceTypeEnum,
} from '@repo/common/types/chat/enum';
import React, { memo, ReactNode } from 'react';
import { MobileChatAt } from '@/hooks/ask-ai/types';
import { ChatAt } from '@/hooks/ask-ai/useChat';
import { useTranslation } from '@/hooks/useTranslation';
import { BoardItem } from '@/typings/board-item';
import { getImageAndTitleFromBoardItem } from '@/utils/board/getImageAndTitleFromBoardItem';
import { cn } from '@/utils/utils';
import { SelectionIcon } from '../icon/selection';
import { ShortcutIcon } from '../icon/shortcut';
import { ThoughtTranslateIcon } from '../icon/thought-translate-icon';
import { messageMentionAnalyzeRegexp, messageMentionRegexp } from './ask-ai-input';
import { MentionListInHistory } from './mention-in-history';

const _EditCommandMapping = {
  [EditCommandTypeEnum.ADJUST_LENGTH]: 'Adjust length',
  [EditCommandTypeEnum.TRANSLATE]: 'Translate',
  [EditCommandTypeEnum.SUGGEST_EDITS]: 'Suggest edits',
  [EditCommandTypeEnum.AUTO_ILLUSTRATION]: 'Auto illustration',
};

const UserMessage = memo(
  ({
    content,
    className,
    command,
    atReference,
    mobileAtReference,
    selection,
    shortcut,
  }: {
    content: string;
    className?: string;
    command?: EditCommand;
    atReference?: ChatAt[];
    mobileAtReference?: MobileChatAt[];
    selection?: string;
    shortcut?: {
      id: string;
      name: string;
    };
  }) => {
    const contentList = [];
    const { t } = useTranslation('Library.AskAI');

    if (shortcut) {
      contentList.push(
        <span
          key={shortcut.id}
          className="relative -top-[1px] mr-2 inline-flex max-w-[120px] items-center gap-1 align-middle"
        >
          <ShortcutIcon size={16} />
          <span className="truncate">{shortcut.name}</span>
        </span>,
      );
    }

    const splitContentWithMentions = content.split(messageMentionRegexp).filter(Boolean);

    for (const mention of splitContentWithMentions) {
      if (messageMentionAnalyzeRegexp.test(mention)) {
        const [, display, id, type] = messageMentionAnalyzeRegexp.exec(mention)!;
        const at = atReference?.find((item) => item.entity.id === id && item.entity_type === type);
        let domContent: ReactNode = `@${display}`;
        if (at) {
          let { title } = getImageAndTitleFromBoardItem(at as unknown as BoardItem);

          if (at.entity_type === 'board') {
            title = t('board');
          }

          if (at.entity_type === MessageAtReferenceTypeEnum.YOUMIND) {
            title = t('youmind');
          }

          domContent = (
            <span className="relative max-w-[200px] truncate leading-5 bg-select text-foreground rounded-md px-[3px] py-[2px]">
              @{title}
            </span>
          );
        }
        contentList.push(
          <span key={id} data-id={id} data-type={type} className="max-w-[200px] truncate">
            {domContent}
          </span>,
        );
      } else {
        contentList.push(mention);
      }
    }

    if (command) {
      contentList.push(
        <React.Fragment key={command.type}>
          <div className="flex items-center gap-2">
            <ThoughtTranslateIcon size={14} /> {getCommandTitle(command)}
          </div>
        </React.Fragment>,
      );
    }

    return (
      <>
        {atReference?.length ? (
          <MentionListInHistory chatAtList={atReference} mobileAtList={mobileAtReference} />
        ) : null}

        <div className={cn(className)}>
          {selection && (
            <>
              <div className="mb-2 flex items-center gap-1 text-[12px] leading-[18px] text-disabled">
                <SelectionIcon size={14} />
                {t('selectedContent')}
              </div>
              <div className="mb-2 line-clamp-3 w-full border-l-[2px] border-l-divider py-[2px] pl-3 text-[12px] leading-[18px] text-caption-fg">
                {selection}
              </div>
            </>
          )}
          <p className="leading-5 text-[14px] my-[2px] font-normal">{contentList}</p>
        </div>
      </>
    );
  },
);
UserMessage.displayName = 'UserMessage';

export default UserMessage;
