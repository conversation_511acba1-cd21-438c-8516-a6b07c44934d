'use client';
import { dismissToast, toast } from '@repo/ui/components/ui/sonner';
import canvasSize from 'canvas-size';
import * as htmlToImage from 'html-to-image';
import React, {
  forwardRef,
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { createPortal } from 'react-dom';
import { cn } from '@/utils/utils';
import { YouMindLogo, YouMindTextLogo } from '../ui/custom/logo';

// 水印位置枚举
export type WatermarkPosition = 'top-left' | 'top-center' | 'top-right';

// 水印类型枚举
export type WatermarkType = 'logo' | 'text' | 'logo-text';

// 水印配置
export interface WatermarkOptions {
  show: boolean;
  type: WatermarkType;
  position: WatermarkPosition;
  hasLogoOnly?: boolean;
  size?: number;
}

// 类名配置
export interface ClassNames {
  wrapper?: string;
  watermark?: string;
  headerDivider?: string;
  footerDivider?: string;
  footer?: string;
  title?: string;
  content?: string;
  header?: string;
}

// 导出配置
export interface ExportOptions {
  filename?: string;
  padding?: {
    x?: number;
    yTop?: number;
    yBottom?: number;
  };
  background?: string;
  borderRadius?: string;
  needDownload?: boolean;
}

// 暴露的方法接口
export interface ExportImagePreviewRef {
  exportImage: (options?: ExportOptions) => Promise<string | undefined>;
  refreshContent: () => void;
  isExporting: boolean;
}

// 组件Props
export interface ExportImagePreviewProps {
  classNames?: ClassNames;
  className?: string;
  watermarkOptions?: WatermarkOptions;
  hasHeaderDivider?: boolean;
  hasFooterDivider?: boolean;
  footer?: ReactNode;
  title?: ReactNode;
  selector?: string | (() => HTMLElement | null);
  children?: ReactNode;
  style?: React.CSSProperties;
  header?: ReactNode;
  onExportSuccess?: (dataUrl: string) => void;
  onExportError?: (error: Error) => void;
  offscreen?: boolean;
  rerenderContentBeforeExport?: {
    enable: boolean;
    // 等待时长
    wait?: number;
  };
  signal?: AbortSignal;
}

async function getDeviceMaxCanvasHeight(): Promise<number> {
  const maxHeight = localStorage.getItem('browser-max-canvas-height');
  if (maxHeight) {
    return parseInt(maxHeight) || 65536;
  }

  const result = await canvasSize.maxHeight({
    useWorker: true, // 使用 Web Worker（如果浏览器支持）
    usePromise: true,
  });

  localStorage.setItem('browser-max-canvas-height', result.height.toString());

  return result.height;
}

// 图片代理函数 - 将外部图片URL转换为代理URL
const proxyImageUrl = (originalUrl: string): string => {
  // 检查是否是外部URL（非同源）
  if (originalUrl.startsWith('http') && !originalUrl.includes(window.location.origin)) {
    // 使用/api/proxy-image代理
    return `/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;
  }
  return originalUrl;
};

const ExportImagePreview = forwardRef<ExportImagePreviewRef, ExportImagePreviewProps>(
  (
    {
      className,
      classNames = {},
      watermarkOptions = {
        show: true,
        type: 'logo-text',
        position: 'top-right',
        hasLogoOnly: false,
        size: 16,
      },
      hasHeaderDivider = false,
      hasFooterDivider = false,
      footer,
      title,
      header,
      selector,
      children,
      style,
      onExportSuccess,
      onExportError,
      offscreen = false,
      signal,
      rerenderContentBeforeExport = {
        enable: false,
        wait: 1000,
      },
    },
    ref,
  ) => {
    const contentRef = useRef<HTMLDivElement>(null);
    const wrapperRef = useRef<HTMLDivElement>(null);
    const [isExporting, setIsExporting] = useState(false);
    const [contentWidth, setContentWidth] = useState<number | undefined>();

    // DOM克隆逻辑
    const cloneContent = useCallback(() => {
      if (!selector || !contentRef.current) return;

      const getTargetElement = () => {
        if (typeof selector === 'string') {
          return document.querySelector(selector) as HTMLElement;
        } else if (typeof selector === 'function') {
          return selector();
        }
        return null;
      };

      const targetElement = getTargetElement();
      if (targetElement) {
        // 清空当前内容
        contentRef.current.innerHTML = '';

        // 克隆目标元素
        const clonedElement = targetElement.cloneNode(true) as HTMLElement;
        clonedElement.tabIndex = -1;
        const images = Array.from(clonedElement.getElementsByTagName('img'));
        images.forEach((img) => {
          img.crossOrigin = 'anonymous';
          img.loading = 'eager';
          // 处理需要代理的外部图片
          if (
            img.src?.startsWith('http') &&
            !(img.src.includes(window.location.origin) || img.src.includes('cdn.gooo.ai'))
          ) {
            const originalSrc = img.src;
            const proxySrc = proxyImageUrl(originalSrc);

            if (proxySrc !== originalSrc) {
              img.src = proxySrc; // 修改src会重新触发加载
            }
          }
        });

        const MOBILE_WIDTH = 380;
        const PC_WIDTH = targetElement.scrollWidth;

        // 设置克隆元素的宽度为原始节点的绝对宽度
        clonedElement.style.width = `${MOBILE_WIDTH}px`;
        clonedElement.style.left = '0px';
        clonedElement.style.paddingLeft = '0px';
        clonedElement.style.paddingRight = '0px';

        setContentWidth(MOBILE_WIDTH);

        // 添加到内容区域
        contentRef.current.appendChild(clonedElement);
      }
    }, []);

    // 刷新内容
    const refreshContent = useCallback(() => {
      cloneContent();
    }, []);

    // 导出前处理图片：设置crossOrigin，处理代理，并等待所有图片加载完成
    const handleImagesBeforeExport = async (element: HTMLElement): Promise<void> => {
      const images = Array.from(element.getElementsByTagName('img'));
      const imageLoadPromises: Promise<void>[] = [];

      images.forEach((img) => {
        // 为所有未加载完成的图片（包括刚替换src的）创建加载Promise
        if (!img.complete) {
          imageLoadPromises.push(
            Promise.race([
              new Promise<void>((resolve) => {
                const loadHandler = () => {
                  console.log('loaded', img.src);
                  img.removeEventListener('load', loadHandler);
                  img.removeEventListener('error', errorHandler);
                  resolve();
                };
                const errorHandler = () => {
                  console.log('fail load', img.src);
                  // 图片加载失败时，隐藏图片，避免在导出图片中显示损坏的图标
                  img.style.display = 'none';
                  img.removeEventListener('load', loadHandler);
                  img.removeEventListener('error', errorHandler);
                  resolve(); // 仍然 resolve，以避免阻塞整个导出流程
                };
                img.addEventListener('load', loadHandler);
                img.addEventListener('error', errorHandler);
              }),
              new Promise<void>((resolve) => {
                setTimeout(() => {
                  // console.log("img load timeout", img.src);
                  resolve();
                }, 10000);
              }),
            ]),
          );
        }
      });

      // 等待所有图片加载
      if (imageLoadPromises.length > 0) {
        const toastKey = toast('Loading images...', {
          duration: 600000,
        });
        await Promise.all(imageLoadPromises);
        dismissToast(toastKey);
      }
    };

    // 导出图片
    const exportImage = useCallback(
      async (options: ExportOptions = {}) => {
        if (!wrapperRef.current) {
          toast('Export failed: no content element found');
          return;
        }

        setIsExporting(true);
        if (rerenderContentBeforeExport?.enable) {
          cloneContent();
          await new Promise((resolve) =>
            setTimeout(resolve, rerenderContentBeforeExport.wait || 0),
          );
        }

        if (signal?.aborted) {
          setIsExporting(false);
          return;
        }

        const {
          filename = `youmind-export-${Date.now()}.png`,
          needDownload = true,
          // padding = { x: 32, yTop: 66, yBottom: 24 },
          // background = "linear-gradient(to bottom, #F2F1F3 90%, #ECF1F9)",
          // borderRadius = "16px",
        } = options;

        try {
          const element = wrapperRef.current;

          // 预处理所有图片：设置 crossOrigin，处理代理并等待加载
          await handleImagesBeforeExport(element);

          if (signal?.aborted) {
            setIsExporting(false);
            return;
          }

          // 临时改变元素样式以进行截图准备
          element.classList.add('exporting');

          // 获取元素尺寸
          const baseScrollHeight = element.scrollHeight;
          const baseScrollWidth = element.scrollWidth;

          console.log(`png size: ${baseScrollWidth}x${baseScrollHeight}`);

          const pixelRatio = 2;

          if (baseScrollHeight > 16384 / pixelRatio) {
            const maxCanvasHeight = await getDeviceMaxCanvasHeight();

            if (baseScrollHeight > maxCanvasHeight / pixelRatio) {
              toast('Export failed: content height exceeds the maximum limit');
              return;
            }
          }

          // html-to-image 配置
          const htmlToImageOptions = {
            width: baseScrollWidth,
            height: baseScrollHeight,
            pixelRatio,
            // 可能导致图片请求失败
            // cacheBust: true, // 避免缓存问题
            skipAutoScale: true, // 避免自动缩放
            style: {
              borderRadius: '8px',
              overflow: 'hidden',
              // background: "linear-gradient(to bottom, #F2F1F3 90%, #ECF1F9)",
              border: 'none',
            },
            filter: (node: HTMLElement) => {
              const hasScrollbar = node.classList?.contains('scrollbar');
              const isStyleOrScript = node.tagName === 'STYLE' || node.tagName === 'SCRIPT';
              const isVideo = node.tagName === 'VIDEO';
              const isAudio = node.tagName === 'AUDIO';
              return !hasScrollbar && !isStyleOrScript && !isVideo && !isAudio;
            },
          };

          // 导出图片
          const dataUrl = await htmlToImage.toPng(element, htmlToImageOptions);

          if (needDownload) {
            // 创建下载链接
            const downloadLink = document.createElement('a');
            downloadLink.href = dataUrl;
            downloadLink.download = filename;

            // 触发下载
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            toast('Image has been downloaded');
          }
          onExportSuccess?.(dataUrl);
          return dataUrl;
        } catch (error) {
          console.error('导出图片失败:', error);
          toast('Failed to export image');
          onExportError?.(error as Error);
        } finally {
          // 恢复元素样式
          if (wrapperRef.current) {
            wrapperRef.current.classList.remove('exporting');
          }
          setIsExporting(false);
        }
      },
      [onExportSuccess, onExportError, cloneContent, signal],
    );

    // 暴露方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        exportImage,
        refreshContent,
        isExporting,
      }),
      [exportImage, refreshContent, isExporting],
    );

    // 处理DOM克隆
    useEffect(() => {
      cloneContent();
    }, []);

    // 渲染水印
    const renderWatermark = () => {
      if (!watermarkOptions.show) return null;

      const { type, position, hasLogoOnly } = watermarkOptions;

      const watermarkClass = cn(
        'absolute text-sm flex items-center gap-2',
        {
          'top-8 left-8': position === 'top-left',
          'top-8 left-1/2 transform -translate-x-1/2': position === 'top-center',
          'top-8 right-8': position === 'top-right',
        },
        classNames.watermark,
      );

      return (
        <div className={watermarkClass}>
          {(type === 'logo' || type === 'logo-text') && (
            <div className="flex items-center">
              <YouMindLogo size={watermarkOptions.size || 16} />
              {!hasLogoOnly && type === 'logo-text' && (
                <YouMindTextLogo className="ml-[6px]" size={watermarkOptions.size || 16} />
              )}
            </div>
          )}
          {type === 'text' && <span className="text-xs">YouMind</span>}
        </div>
      );
    };

    const PreviewComponent = (
      <div
        ref={wrapperRef}
        className={cn(
          'pointer-events-none relative box-content overflow-hidden rounded-lg p-8',
          className,
          classNames.wrapper,
        )}
        style={{ ...style, width: contentWidth }}
      >
        {/* 水印 */}
        {renderWatermark()}

        {/* 头部 */}
        {header && <div className={cn('relative mb-8', classNames.header)}>{header}</div>}

        {/* 标题 */}
        {title && (
          <>
            <div className={cn('mt-8', classNames.title)}>{title}</div>
            {hasHeaderDivider && (
              <div className={cn('border-t border-gray-200', classNames.headerDivider)} />
            )}
          </>
        )}

        {/* 内容区域 */}
        <div ref={contentRef} className={cn('mt-6 w-fit', classNames.content)}>
          {children}
        </div>

        {/* 脚注分割线 */}
        {hasFooterDivider && footer && (
          <div className={cn('border-t border-gray-200', classNames.footerDivider)} />
        )}

        {/* 脚注 */}
        {footer && (
          <div className={cn('py-4 text-sm text-gray-500', classNames.footer)}>{footer}</div>
        )}
      </div>
    );

    if (offscreen) {
      return createPortal(
        <div
          className={cn(
            'fixed z-50 w-fit',
            'left-[-9999px] top-[-9999px]',
            // 测试用
            // "left-0 top-0 h-screen overflow-auto",
          )}
        >
          {PreviewComponent}
        </div>,
        document.body,
      );
    }

    return PreviewComponent;
  },
);

ExportImagePreview.displayName = 'ExportImagePreview';

export default ExportImagePreview;
