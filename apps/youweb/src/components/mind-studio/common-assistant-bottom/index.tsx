import { toast } from '@repo/ui/components/ui/sonner';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { Check, Copy, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { ExportImageIcon } from '../../icon/export-image-icon';
import { LLMTools } from '../../llm-tools';
import { useExportImage } from './useExportImage';

interface CommonAssistantBottomProps {
  sourceType: 'overview' | 'custom-assistant';
  sourceElementRef?: React.RefObject<HTMLDivElement>;
  canExportImage?: boolean;
  canCopyContent?: boolean;
  loading?: boolean;
  onRegenerate?: () => void;
  onCopyContent?: () => void;
  traceId?: string;
}

export const CommonAssistantBottom = ({
  sourceType,
  sourceElementRef,
  canExportImage = false,
  canCopyContent = false,
  loading = false,
  onRegenerate,
  onCopyContent,
  traceId,
}: CommonAssistantBottomProps) => {
  const { trackButtonClick } = useTrackActions();

  const [copySuccess, setCopySuccess] = useState(false);

  // 使用新的 useExportImage hook
  const { exportImage, isExporting: isExportingImage } = useExportImage({
    sourceElementRef,
  });

  const sourceContent = sourceElementRef?.current?.innerText;

  const handleCopyContent = () => {
    trackButtonClick(`${sourceType}_copy_content_click`);
    if (onCopyContent) {
      onCopyContent();
    } else {
      if (!sourceContent) {
        toast('The current content is empty.');
        return;
      }
      navigator.clipboard.writeText(sourceContent);
    }
    setCopySuccess(true);
    setTimeout(() => {
      setCopySuccess(false);
    }, 2000);
  };

  const handleExportImage = () => {
    trackButtonClick(`${sourceType}_export_image_click`);
    exportImage();
  };

  return (
    <div className="flex items-center w-full gap-4 pt-2 pb-2">
      {canCopyContent ? (
        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger
              disabled={loading}
              className={loading ? 'pointer-events-none opacity-50' : ''}
            >
              {copySuccess ? (
                <Check className="cursor-pointer text-caption-fg hover:text-foreground" size={16} />
              ) : (
                <Copy
                  className="cursor-pointer text-caption-fg hover:text-foreground"
                  size={16}
                  onClick={handleCopyContent}
                />
              )}
            </TooltipTrigger>
            <TooltipContent side="bottom">Copy</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : null}
      {canExportImage && (
        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger
              disabled={loading}
              className={loading ? 'pointer-events-none opacity-50' : ''}
            >
              {isExportingImage ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <ExportImageIcon
                  onClick={handleExportImage}
                  size={16}
                  className="cursor-pointer text-caption-fg hover:text-foreground"
                />
              )}
            </TooltipTrigger>
            <TooltipContent>
              <p>Export image</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      <LLMTools
        traceId={traceId}
        className="llm-tools"
        onRegenerate={onRegenerate}
        disabled={loading}
      />
    </div>
  );
};
