import type { SnipBlock } from '@repo/common/types/snip/app-types';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { useAtom } from 'jotai';
import { useEffect, useMemo, useRef, useState } from 'react';
import OverviewBlock, { type OverviewBlockHandle } from '@/components/block/overview';
import { snipDetailAtom } from '@/hooks/scoped/useSnips';
import { useTranslation } from '@/hooks/useTranslation';
import { hasNoTranscripts } from '@/pages/snips/utils';
import { cn } from '@/utils/utils';
import { Logo } from '../../sidebar/logo-button';
import { CommonAssistantBottom } from '../common-assistant-bottom';

export interface OverviewPanelProps extends React.HTMLAttributes<HTMLDivElement> {}

export default function OverviewPanel({ className }: OverviewPanelProps) {
  const { t } = useTranslation('Library.SnipDetail');
  const [snip, setSnip] = useAtom(snipDetailAtom);

  const overviewBlockRef = useRef<HTMLDivElement>(null);

  const [overviewTraceId, setOverviewTraceId] = useState<string>('');

  const [loading, setLoading] = useState(false);

  const { trackButtonClick } = useTrackActions();

  const overviewBlockHandle = useRef<OverviewBlockHandle>(null);

  // @ts-expect-error 预期内
  const overviewKey = `${snip?.id}-${snip?.overview?.contents?.[0]?.id}`;

  const snipRef = useRef(snip);

  const noTranscript = useMemo(() => hasNoTranscripts(snip!), [snip]);

  useEffect(() => {
    snipRef.current = snip;
  }, [snip]);

  const handleOverviewGenerateFinish = (overviewData: SnipBlock['contents'][0]) => {
    const currentSnip = snipRef.current;
    // 更新 snip detail
    if (currentSnip?.type && overviewData?.id) {
      setSnip({
        ...currentSnip,
        overview: {
          current_content_id: overviewData.id,
          // @ts-expect-error 预期内
          contents: [overviewData],
        },
      });
    }
  };

  return (
    <div className={cn('flex h-full flex-col overflow-y-auto', className)}>
      <div className="flex h-6 items-center text-sm text-caption-fg">{t('overview')}</div>
      <main
        // className="relative w-full flex-1 overflow-y-auto py-3"
        className="relative left-[-24px] flex w-[calc(100%+48px)] flex-col px-6 py-3"
        ref={overviewBlockRef}
      >
        <OverviewBlock
          key={overviewKey}
          snip={snip!}
          pointBackground="#F7F7F6"
          onLoadingChange={setLoading}
          onTraceIdChange={setOverviewTraceId}
          ref={overviewBlockHandle}
          onGenerateFinish={handleOverviewGenerateFinish}
        />
        <div className="youmind-logo-temporary mt-[80px] w-full justify-center opacity-25">
          <Logo />
        </div>
      </main>

      {noTranscript ? null : (
        <CommonAssistantBottom
          sourceType="overview"
          sourceElementRef={overviewBlockRef}
          canExportImage
          canCopyContent
          loading={loading}
          traceId={overviewTraceId}
          onRegenerate={() => {
            overviewBlockHandle.current?.generateNewOverview({
              regenerate: true,
            });
            // 上报埋点
            trackButtonClick('overview_regenerate_click');
            setOverviewTraceId('');
          }}
        />
      )}
    </div>
  );
}
