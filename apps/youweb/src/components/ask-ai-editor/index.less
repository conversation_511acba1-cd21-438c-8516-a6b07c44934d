.ai-ask-editor {
  p {
    font-size: 14px;
    line-height: 20px;
    margin: 2px 0;
  }
  // Placeholder 样式
  p.is-editor-empty:first-child::before {
    color: hsl(var(--disabled));
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
  }

  .mention-file {
    display: inline-block;
    max-width: 12rem;
    font-size: 14px;
    vertical-align: bottom;
    line-height: 20px;
    background: var(--select);
    color: var(--foreground);
    border-radius: 5px;
    padding: 0 3px;
    font-weight: 400;

    .mention-file-content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }
  }
}
