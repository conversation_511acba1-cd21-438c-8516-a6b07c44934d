import { BoardStatusEnum } from '@repo/common/types/board/types';
import { Button } from '@repo/ui/components/ui/button';
import { toast } from '@repo/ui/components/ui/sonner';
import * as boardIcons from '@youmindinc/youicon';
import { atom, useAtom } from 'jotai';
import { Ellipsis } from 'lucide-react';
import { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { deleteBoard as deleteBoardAPI } from '@/apis/board';
import { boardDetailAtom, deleteBoardAtom } from '@/hooks/useBoards';
import { useTranslation } from '@/hooks/useTranslation';
import BoardModifyButton from '@/pages/board-detail/board-modify-button';
import type { Board } from '@/typings/board';
import { cn } from '@/utils/utils';

// import { deleteBoardAction } from '../../../boards/actions';

interface BoardItemProps {
  board: Board;
}

// 用于控制 board item 菜单打开状态的 atom
export const openBoardItemMenuIdAtom = atom<string | null>(null);

export const BoardItem = ({ board }: BoardItemProps) => {
  const [isHover, setIsHover] = useState(false);
  const [, deleteBoard] = useAtom(deleteBoardAtom);

  const [boardDetail, setBoardDetail] = useAtom(boardDetailAtom);

  const location = useLocation();

  const { t } = useTranslation('Library.Board');

  const navigate = useNavigate();

  // 获取和设置当前打开的菜单 ID
  const [openMenuId, setOpenMenuId] = useAtom(openBoardItemMenuIdAtom);

  const isActive = location.pathname.startsWith(`/boards/${board.id}`);
  // @ts-expect-error icon name is dynamic
  const Icon = boardIcons[board.icon?.name] || boardIcons.Planet;

  // 判断当前菜单是否打开
  const isMenuOpen = openMenuId === board.id;

  // 菜单状态变化处理
  const handleMenuOpenChange = (open: boolean) => {
    if (open) {
      // 打开当前菜单，同时关闭其他菜单
      setOpenMenuId(board.id);
    } else {
      // 如果菜单关闭且当前菜单就是之前打开的那个，则设置为 null
      if (openMenuId === board.id) {
        setOpenMenuId(null);
      }
    }
  };

  const onBoardDelete = async () => {
    if (!board?.id) {
      return;
    }
    try {
      const { error } = await deleteBoardAPI(board.id);
      if (error) {
        return;
      }
      deleteBoard(board.id);
      if (location.pathname === `/boards/${board.id}`) {
        navigate('/boards', { replace: true });
      }
    } catch (e) {
      toast((e as Error).message);
    }
  };

  const onBoardEditFinish = (data: Partial<Board>) => {
    if (boardDetail?.id !== board.id) {
      return;
    }
    setBoardDetail({
      ...(boardDetail as Board),
      name: data.name!,
      icon: data.icon!,
    });
  };

  return (
    <li
      className={cn(
        'group my-0.5 flex w-full items-center justify-between gap-1 rounded-md pl-2 pr-[5px] transition-colors hover:bg-snip-card hover:text-card-foreground',
        isActive && 'bg-muted',
      )}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      <Link
        className={cn('flex flex-1 cursor-pointer flex-row items-center text-sm')}
        to={{ pathname: `/boards/${board.id}`, search: undefined }}
      >
        <div className="flex flex-row items-center flex-1 w-full h-8">
          <Icon size={18} style={{ color: `var(${board.icon?.color})` }} />

          <div className="flex-1 min-w-0 ml-2 line-clamp-1">{board.name}</div>
        </div>
      </Link>
      <BoardModifyButton
        board={board as unknown as Board}
        dialogAlign="start"
        itemsCount={board.items_count}
        menuOpen={isMenuOpen}
        onMenuOpenChange={handleMenuOpenChange}
        onBoardEditFinish={onBoardEditFinish}
        onBoardDelete={onBoardDelete}
        onArchiveChange={(status) => {
          toast(
            status === BoardStatusEnum.OTHER
              ? t('archivedLongDesc', { boardName: board.name })
              : t('unarchivedLongDesc', { boardName: board.name }),
            {
              position: 'bottom-center',
              duration: 5000,
            },
          );
        }}
      >
        <Button
          iconOnly
          variant="ghost"
          size="xs"
          className={cn('rounded-[4px] w-6', isHover || isMenuOpen ? 'opacity-100' : 'opacity-0')}
        >
          <Ellipsis size={16} />
        </Button>
      </BoardModifyButton>
    </li>
  );
};
