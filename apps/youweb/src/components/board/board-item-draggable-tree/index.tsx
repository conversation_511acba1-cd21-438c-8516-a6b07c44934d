'use client';

import {
  Announcements,
  closestCenter,
  DndContext,
  DragEndEvent,
  DragMoveEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  DropAnimation,
  defaultDropAnimation,
  MeasuringStrategy,
  Modifier,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { BoardItemType } from '@repo/api/generated-client/snake-case/index';
import { useAtomValue, useSetAtom } from 'jotai';
import { useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { moveBoardItemToBoardGroup, moveBoardItemToRoot } from '@/apis/board';
import { isEditingBoardItemAtom, useBoardItemTree } from '@/hooks/useBoardItemTree';
import { SensorContext } from '@/hooks/useBoardItemTree/type';
import { useFlattenBoardItemTree } from '@/hooks/useBoardItemTree/useFlattenBoardItemTree';
import { boardDetailAtom, useSetBoardDetailItems } from '@/hooks/useBoards';
import { BoardWithTreeItems } from '@/typings/board';
import { BoardTreeItem } from '@/typings/board-item';
import { SortableTreeItem } from './components';
import { buildTree, flattenTree, getChildCount } from './utilities';

const measuring = {
  droppable: {
    strategy: MeasuringStrategy.Always,
  },
};

const dropAnimationConfig: DropAnimation = {
  keyframes({ transform }) {
    return [
      { opacity: 1, transform: CSS.Transform.toString(transform.initial) },
      {
        opacity: 0,
        transform: CSS.Transform.toString({
          ...transform.final,
          x: transform.final.x + 0,
          y: transform.final.y - 20,
        }),
      },
    ];
  },
  easing: 'ease-out',
  sideEffects({ active }) {
    active.node.animate([{ opacity: 0 }, { opacity: 1 }], {
      duration: defaultDropAnimation.duration,
      easing: defaultDropAnimation.easing,
    });
  },
};

interface Props {
  draggable?: boolean;
  collapsible?: boolean;
  defaultItems?: BoardTreeItem[];
  indentationWidth?: number;
  indicator?: boolean;
}

export function BoardItemDraggableTree({
  draggable = true,
  collapsible,
  indicator = false,
  indentationWidth = 50,
}: Props) {
  const {
    treeItems,
    collapsedBoardGroupIds,
    activeId,
    setActiveId,
    setTreeItems,
    toggleCollapseBoardGroup,
  } = useBoardItemTree();
  const [overId, setOverId] = useState<string | null>(null);
  const [offsetLeft, setOffsetLeft] = useState(0);
  const [currentPosition, setCurrentPosition] = useState<{
    parentId: string | null;
    overId: string;
  } | null>(null);

  const { flattenedItems } = useFlattenBoardItemTree();

  const board = useAtomValue(boardDetailAtom) as BoardWithTreeItems;

  const { setBoardItems } = useSetBoardDetailItems();

  useEffect(() => {
    setTreeItems(buildTree(board?.items || []));
  }, [board]);

  const setIsEditingBoardItem = useSetAtom(isEditingBoardItemAtom);

  // 控制 isEditingBoardItem 的变化
  useEffect(() => {
    const isEditing = flattenedItems.some((item) => item.editing);
    setIsEditingBoardItem(isEditing);
  }, [flattenedItems.map((item) => item.editing).join(',')]);

  // 计算目标深度
  function calculateTargetDepth(params: {
    activeItem: BoardTreeItem;
    previousItem?: BoardTreeItem;
    nextItem?: BoardTreeItem;
    items: BoardTreeItem[];
    offsetLeft: number;
  }): number {
    const { activeItem, previousItem, nextItem, offsetLeft } = params;

    const dragDepth = Math.round(offsetLeft / indentationWidth);

    const isGroup = activeItem.entity_type === BoardItemType.boardGroup;

    // group 的深度一定是 0
    if (isGroup) {
      return 0;
    }

    // 拖拽向左，且下一项深度为0，则返回深度0
    if (dragDepth < 0 && nextItem?.depth === 0) {
      return 0;
    }

    // 非 group
    // 如果前一个是 group，则深度一定是 1
    if (previousItem && previousItem.entity_type === BoardItemType.boardGroup) {
      return 1;
    }

    // 如果前一个或者后一个的深度为1，则当前深度一定是1
    if ((previousItem && previousItem.depth === 1) || (nextItem && nextItem.depth === 1)) {
      return 1;
    }

    return 0;
  }

  const projected =
    activeId && overId
      ? (() => {
          const overItemIndex = flattenedItems.findIndex(({ id }) => id === overId);
          const activeItemIndex = flattenedItems.findIndex(({ id }) => id === activeId);
          const activeItem = flattenedItems[activeItemIndex];
          const newItems = arrayMove(flattenedItems, activeItemIndex, overItemIndex);
          const previousItem = newItems[overItemIndex - 1];
          const nextItem = newItems[overItemIndex + 1];

          // 如果 previousItem 是 group，且存在 children 且没有展开，则展开
          if (
            previousItem &&
            previousItem.entity_type === BoardItemType.boardGroup &&
            previousItem.children.length &&
            collapsedBoardGroupIds.includes(previousItem.id) &&
            activeItem.entity_type !== BoardItemType.boardGroup
          ) {
            toggleCollapseBoardGroup(previousItem.id);
          }

          // 使用独立的深度计算函数
          const depth = calculateTargetDepth({
            activeItem,
            previousItem,
            nextItem,
            items: flattenedItems,
            offsetLeft,
          });

          // 计算新的 parent
          const fromBelow = activeItemIndex >= overItemIndex;
          const { parentId, parentItem } = calculateParentId(
            flattenedItems,
            overItemIndex,
            depth,
            fromBelow,
          );

          return {
            depth,
            parentId,
            parentItem,
            previousItem,
            nextItem,
            fromBelow,
          };
        })()
      : null;

  const sensorContext: SensorContext = useRef({
    items: flattenedItems,
    offset: offsetLeft,
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: draggable ? 8 : 8888,
      },
    }),
  );

  const sortedIds = useMemo(() => flattenedItems.map(({ id }) => id), [flattenedItems]);

  const activeItem = activeId ? flattenedItems.find(({ id }) => id === activeId) : null;

  useEffect(() => {
    sensorContext.current = {
      items: flattenedItems,
      offset: offsetLeft,
    };
  }, [flattenedItems, offsetLeft]);

  const announcements: Announcements = {
    onDragStart({ active }) {
      return `Picked up ${active.id}.`;
    },
    onDragMove({ active, over }) {
      return getMovementAnnouncement(
        'onDragMove',
        active.id as string,
        over?.id as string | undefined,
      );
    },
    onDragOver({ active, over }) {
      return getMovementAnnouncement(
        'onDragOver',
        active.id as string,
        over?.id as string | undefined,
      );
    },
    onDragEnd({ active, over }) {
      return getMovementAnnouncement(
        'onDragEnd',
        active.id as string,
        over?.id as string | undefined,
      );
    },
    onDragCancel({ active }) {
      return `Moving was cancelled. ${active.id} was dropped in its original position.`;
    },
  };

  return (
    <DndContext
      accessibility={{ announcements }}
      sensors={sensors}
      collisionDetection={closestCenter}
      measuring={measuring}
      onDragStart={handleDragStart}
      onDragMove={handleDragMove}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
    >
      <SortableContext items={sortedIds} strategy={verticalListSortingStrategy}>
        {flattenedItems.map((item, index) => {
          const { id, children, depth } = item;

          if (
            collapsedBoardGroupIds.includes(item.parentId || '') &&
            item.entity_type !== BoardItemType.boardGroup
          ) {
            return null;
          }

          return (
            <SortableTreeItem
              key={`${id}-${index}`}
              item={item}
              id={id}
              depth={id === activeId && projected ? projected.depth : depth}
              indentationWidth={indentationWidth}
              indicator={indicator}
              collapsed={Boolean(collapsedBoardGroupIds.includes(id))}
              onCollapse={collapsible ? () => toggleCollapseBoardGroup(id) : undefined}
              isInside={projected?.parentId === id}
              childrenCount={children.length}
              activeItem={activeItem}
              activeItemFromBelow={projected?.fromBelow}
            />
          );
        })}
        {createPortal(
          <DragOverlay
            dropAnimation={dropAnimationConfig}
            modifiers={indicator ? [adjustTranslate] : undefined}
            className="opacity-90"
          >
            {activeId && activeItem ? (
              <SortableTreeItem
                item={activeItem}
                id={activeId}
                depth={activeItem.depth}
                clone
                childCount={getChildCount(treeItems, activeId)}
                indentationWidth={indentationWidth}
              />
            ) : null}
          </DragOverlay>,
          document.body,
        )}
      </SortableContext>
    </DndContext>
  );

  function handleDragStart({ active: { id: activeId } }: DragStartEvent) {
    setActiveId(activeId as string);
    setOverId(activeId as string);

    const activeItem = flattenedItems.find(({ id }) => id === activeId);

    if (activeItem) {
      setCurrentPosition({
        parentId: activeItem.parentId,
        overId: activeId as string,
      });
    }

    document.body.style.setProperty('cursor', 'grabbing');
  }

  function handleDragMove({ delta }: DragMoveEvent) {
    setOffsetLeft(delta.x);
  }

  function handleDragOver({ over }: DragOverEvent) {
    setOverId(over?.id as string | null);
  }

  function handleDragEnd({ active, over }: DragEndEvent) {
    resetState();

    // console.log("projected", projected);
    // console.log("over", over);
    // console.log("active", active);

    if (projected && over) {
      const { depth, parentId, parentItem } = projected;
      const clonedItems: BoardTreeItem[] = JSON.parse(JSON.stringify(flattenTree(treeItems)));
      const overIndex = clonedItems.findIndex(({ id }) => id === over.id);
      const activeIndex = clonedItems.findIndex(({ id }) => id === active.id);
      const activeTreeItem = clonedItems[activeIndex];

      if (activeTreeItem.entity_type === BoardItemType.boardGroup) {
        clonedItems[activeIndex] = { ...activeTreeItem, depth: 0 };
      } else {
        clonedItems[activeIndex] = { ...activeTreeItem, depth, parentId };
      }

      const sortedItems = arrayMove(clonedItems, activeIndex, overIndex);
      const newItems = buildTree(sortedItems);

      setTreeItems(newItems);

      setBoardItems(flattenTree(newItems));

      const checkInvalidRankAfterId = (id: string | undefined) => {
        if (id?.startsWith('new-group-')) {
          return true;
        }
        return false;
      };

      // 更新服务端
      if (depth === 0) {
        let prevBoardItemId: string | undefined = projected.previousItem?.id;
        // 如果前一个项在 group 里，且自己不在 group 里，则使用前一个项的父级作为 prevRank
        if (projected.previousItem?.depth === 1 && activeItem?.depth === 0) {
          const truePrevItem = flattenedItems.find(
            ({ id }) => id === projected.previousItem?.parentId,
          );
          prevBoardItemId = truePrevItem?.id;
        }
        if (checkInvalidRankAfterId(prevBoardItemId)) {
          return;
        }
        moveBoardItemToRoot({
          board_item_id: active.id as string,
          rank_after_id: prevBoardItemId,
        });
      } else {
        let rank_after_id: string | undefined = projected.previousItem?.id;
        // 如果拖到了 Group 第一位，那么 rank_after_id 要传空
        if (projected.previousItem?.entity?.id === parentItem?.entity?.id) {
          rank_after_id = undefined;
        }
        if (checkInvalidRankAfterId(rank_after_id)) {
          return;
        }
        moveBoardItemToBoardGroup({
          board_item_id: active.id as string,
          parent_board_group_id: parentItem?.entity?.id || '',
          rank_after_id,
        });
      }
    }
  }

  function handleDragCancel() {
    resetState();
  }

  function resetState() {
    setOverId(null);
    setActiveId(null);
    setOffsetLeft(0);
    setCurrentPosition(null);

    document.body.style.setProperty('cursor', '');
  }

  function getMovementAnnouncement(eventName: string, activeId: string, overId?: string) {
    if (overId && projected) {
      if (eventName !== 'onDragEnd') {
        if (
          currentPosition &&
          projected.parentId === currentPosition.parentId &&
          overId === currentPosition.overId
        ) {
          return;
        } else {
          setCurrentPosition({
            parentId: projected.parentId,
            overId,
          });
        }
      }

      const clonedItems: BoardTreeItem[] = JSON.parse(JSON.stringify(flattenTree(treeItems)));
      const overIndex = clonedItems.findIndex(({ id }) => id === overId);
      const activeIndex = clonedItems.findIndex(({ id }) => id === activeId);
      const sortedItems = arrayMove(clonedItems, activeIndex, overIndex);

      const previousItem = sortedItems[overIndex - 1];

      let announcement;
      const movedVerb = eventName === 'onDragEnd' ? 'dropped' : 'moved';
      const nestedVerb = eventName === 'onDragEnd' ? 'dropped' : 'nested';

      if (!previousItem) {
        const nextItem = sortedItems[overIndex + 1];
        announcement = `${activeId} was ${movedVerb} before ${nextItem.id}.`;
      } else {
        if (projected.depth > previousItem.depth) {
          announcement = `${activeId} was ${nestedVerb} under ${previousItem.id}.`;
        } else {
          let previousSibling: BoardTreeItem | undefined = previousItem;
          while (previousSibling && projected.depth < previousSibling.depth) {
            const parentId: string | null = previousSibling.parentId;
            previousSibling = sortedItems.find(({ id }) => id === parentId);
          }

          if (previousSibling) {
            announcement = `${activeId} was ${movedVerb} after ${previousSibling.id}.`;
          }
        }
      }

      return announcement;
    }

    return;
  }
}

const adjustTranslate: Modifier = ({ transform }) => {
  return {
    ...transform,
    y: transform.y - 25,
  };
};

// 辅助函数：根据深度计算父节点
function calculateParentId(
  items: BoardTreeItem[],
  currentIndex: number,
  targetDepth: number,
  fromBelow: boolean,
) {
  let parentItem: BoardTreeItem | null = null;

  // 向上查找最近的可能父节点
  for (let i = fromBelow ? currentIndex - 1 : currentIndex; i >= 0; i--) {
    const item = items[i];

    // 如果目标深度为1，我们需要寻找最近的 group
    if (targetDepth === 1) {
      if (item.entity_type === BoardItemType.boardGroup) {
        parentItem = item;
        break;
      }
    }
    // 否则查找第一个深度小于目标深度的节点
    else if (item.depth < targetDepth) {
      parentItem = item;
      break;
    }
  }

  // 确保父节点必须是 BOARD_GROUP
  if (parentItem?.entity_type !== BoardItemType.boardGroup) {
    parentItem = null;
  }

  return {
    parentId: parentItem?.id || null,
    parentItem,
  };
}
