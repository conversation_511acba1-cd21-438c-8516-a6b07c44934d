import { BoardGroup } from '@repo/common/types/board-group/types';
import { Button } from '@repo/ui/components/ui/button';
import { SnipArticle, SnipImage, SnipTypeEnum } from '@repo/ui-business-snip';
import { useAtomValue, useSetAtom } from 'jotai';
import { ChevronDown, ChevronRight, Loader2 } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useSearchedData } from '@/components/ai-ask/tool/hooks/useSearchedData';
import { checkTitleValid } from '@/components/ai-ask/tool/utils/checkTitleValid';
import { isBoardItemDraggableTreeHoveredAtom, useBoardItemTree } from '@/hooks/useBoardItemTree';
import { changePanelDataAtom, panelStateAtom, winkFocusPanelAtom } from '@/hooks/useBoardState';
import { renamingBreadcrumbItemIdAtom } from '@/hooks/useBoards';
import { BoardItemTypeEnum, BoardTreeItem } from '@/typings/board-item';
import {
  getImageAndTitleFromBoardItem,
  IMAGE_ICON,
  WEBSITE_ICON,
} from '@/utils/board/getImageAndTitleFromBoardItem';
import { cn } from '@/utils/utils';
import { GroupIconV2 } from '../../../../group/GroupIconV2';
import { MoreMenu } from './MoreMenu';

interface Props {
  item: BoardTreeItem;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleProps?: any;
  className?: string;
  onCollapse?: () => void;
  collapsed?: boolean;
  isDragging?: boolean;
}

export const BoardItem: React.FC<Props> = (props) => {
  const { item, handleProps = {}, onCollapse, collapsed, isDragging, className } = props;

  const panelState = useAtomValue(panelStateAtom);
  const changePanelData = useSetAtom(changePanelDataAtom);
  const renamingBreadcrumbItemId = useAtomValue(renamingBreadcrumbItemIdAtom);

  const isItemOpened = panelState.panelData?.id === item.id;

  const defaultTitleForThought = 'New thought';
  const defaultTitleForGroup = 'New group';
  const defaultTitleForSnip = 'New snip';

  const getDefaultTitle = () => {
    if (item.entity_type === BoardItemTypeEnum.boardGroup) {
      return defaultTitleForGroup;
    }
    if (item.entity_type === BoardItemTypeEnum.thought) {
      return defaultTitleForThought;
    }
    return defaultTitleForSnip;
  };

  const { title, imageUrl } = getImageAndTitleFromBoardItem(item, {
    defaultTitle: getDefaultTitle(),
  });

  const [inputTitle, setInputTitle] = useState(title);
  const [loading, setLoading] = useState(false);
  const { saveNewGroup, saveRenamedBoardItem, stopEditingAnItem, cancelEditingBoardItem } =
    useBoardItemTree();

  const titleInputRef = useRef<HTMLInputElement>(null);

  const selectionStyleRef = useRef<HTMLStyleElement | null>(null);

  const mountedTimeRef = useRef<number>(0);

  const setWinkFocusPanel = useSetAtom(winkFocusPanelAtom);

  const isTreeHovered = useAtomValue(isBoardItemDraggableTreeHoveredAtom);

  const [isSaving, setIsSaving] = useState(false);

  const [isComposing, setIsComposing] = useState(false);

  const { getSearchedDataByUrl } = useSearchedData();

  const renderValidTitle = () => {
    if (title && checkTitleValid(title)) {
      return title;
    }
    // 返回 web search 那边的值（适配 newboard 场景）
    const searchedData = getSearchedDataByUrl((item.entity as SnipArticle)?.webpage?.url);
    if (searchedData && checkTitleValid(searchedData.title)) {
      return searchedData.title;
    }
    return title;
  };

  useEffect(() => {
    mountedTimeRef.current = Date.now();
  }, []);

  useEffect(() => {
    setInputTitle(title || getDefaultTitle());
    if (item.editing) {
      setTimeout(
        () => {
          titleInputRef.current?.focus();
          // 自动选中当前 input 的文本
          titleInputRef.current?.select();
          // 临时修改 selection 的颜色为 #02041A0F
          const selection = window.getSelection();
          if (selection && !selectionStyleRef.current) {
            // 添加临时样式到 head
            const style = document.createElement('style');
            style.textContent = `
            ::selection {
              background: #02041A0F;
            }
          `;
            document.head.appendChild(style);
            selectionStyleRef.current = style;
          }
        },
        item.id.startsWith('new-group-') ? 500 : 100,
      );
    }
  }, [item]);

  const isGroup = item.entity_type === BoardItemTypeEnum.boardGroup;

  const handleClick = () => {
    if (item.editing) {
      return;
    }
    if (!item.entity || item.entity_type === null) {
      return;
    }
    if (isItemOpened) {
      // 如果已经在 split view 中，聚焦它
      setWinkFocusPanel(true);
      return;
    }
    changePanelData({
      id: item.id!,
      entity: item.entity,
      entity_type: item.entity_type,
    });
  };

  const handleSaveTitle = async () => {
    // 移除临时样式
    selectionStyleRef.current?.remove();
    selectionStyleRef.current = null;

    if (isSaving) return;
    setIsSaving(true);

    const newTitle = inputTitle.trim();
    if (!newTitle || newTitle === title) {
      if (item.id.startsWith('new-group-')) {
        cancelEditingBoardItem(item);
      } else {
        stopEditingAnItem(item);
      }
      setIsSaving(false);
      return;
    }

    setLoading(true);
    if (item.id.startsWith('new-group-')) {
      await saveNewGroup(newTitle);
    } else {
      await saveRenamedBoardItem(item, newTitle);
    }
    setLoading(false);
    setIsSaving(false);
  };

  // 如果当前 item 在面包屑中被重命名，则不显示编辑状态
  const isBeingRenamedInBreadcrumb = renamingBreadcrumbItemId === item.id;

  return (
    <div
      className={cn(
        'group relative my-1 flex h-[36px] w-full cursor-pointer items-center justify-between rounded-[8px] pl-3 pr-2 hover:bg-snip-card',
        isItemOpened && !isDragging && 'bg-muted',
        item.editing && !isBeingRenamedInBreadcrumb && 'box-border border !bg-card pl-[11px]',
        className,
      )}
    >
      {isGroup ? (
        <GroupIconV2
          className="mr-2"
          icon={(item.entity as BoardGroup)?.icon}
          open={!collapsed}
          onClick={handleClick}
        />
      ) : null}
      <button
        title={title}
        onClick={handleClick}
        className={cn('!flex h-full flex-1 touch-none items-center')}
        data-cypress="draggable-handle"
        {...handleProps}
      >
        {!isGroup && (
          <img
            src={imageUrl}
            alt="icon"
            className="mr-[10px] h-4 w-4 flex-shrink-0 rounded-[4px] object-cover"
            onError={(e) => {
              // 如果是 webpage
              if ((item.entity as SnipImage).type === SnipTypeEnum.image) {
                e.currentTarget.src = IMAGE_ICON;
              } else {
                e.currentTarget.src = WEBSITE_ICON;
              }
            }}
          />
        )}

        <div className="min-w-0 flex-1">
          {item.editing && !isBeingRenamedInBreadcrumb ? (
            <input
              ref={titleInputRef}
              type="text"
              value={inputTitle}
              className="relative bottom-[0.5px] w-full rounded-[8px] border-foreground bg-transparent py-0.5 text-sm"
              disabled={loading}
              onChange={(e) => {
                setInputTitle(e.target.value);
              }}
              onBlur={() => {
                if (Date.now() - mountedTimeRef.current < 500) {
                  return;
                }
                handleSaveTitle();
              }}
              onCompositionStart={() => {
                setIsComposing(true);
              }}
              onCompositionEnd={() => {
                setIsComposing(false);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !isComposing) {
                  handleSaveTitle();
                }
                if (e.key === 'Escape') {
                  cancelEditingBoardItem(item);
                }
              }}
            />
          ) : (
            <div className="line-clamp-1 w-full break-all text-left text-sm">
              {renderValidTitle()}
            </div>
          )}
        </div>
      </button>
      {loading && (
        <Loader2
          size={16}
          className="absolute right-[10px] top-[10px] animate-spin text-secondary-fg"
        />
      )}
      {!item.editing && !isDragging && <MoreMenu item={item} />}
      {isGroup && !item.editing && (
        <Button
          variant="ghost"
          size="icon"
          className="ml-0.5 h-6 w-6 p-0 rounded-sm hover:bg-muted"
          onClick={(e) => {
            e.stopPropagation();
            onCollapse?.();
          }}
        >
          <div
            className="text-secondary-fg"
            style={{
              display: isTreeHovered ? 'flex' : 'none',
            }}
          >
            {collapsed ? <ChevronRight size={16} /> : <ChevronDown size={16} />}
          </div>
        </Button>
      )}
    </div>
  );
};
