'use client';

import { BoardStatusEnum } from '@repo/common/types/board/types';
import { Button } from '@repo/ui/components/ui/button';
import { toast } from '@repo/ui/components/ui/sonner';
import * as boardIcons from '@youmindinc/youicon';
import { useAtom } from 'jotai';
import { Ellipsis, Plus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { deleteBoard as deleteBoardAPI } from '@/apis/board';
import { boardDetailAtom, deleteBoardAtom } from '@/hooks/useBoards';
import { useTranslation } from '@/hooks/useTranslation';
import BoardModifyButton from '@/pages/board-detail/board-modify-button';
import type { Board } from '@/typings/board';
import { TimeSince } from '@/utils/timeSince';
import { BoardAdderDialog } from './board-adder-dialog';
import { PALETTE } from './board-creator';

interface BoardDetailHeaderProps {
  hideTitle?: boolean;
}

export default function BoardDetailHeader({ hideTitle = false }: BoardDetailHeaderProps) {
  const [board, setBoardDetail] = useAtom(boardDetailAtom);
  const [, deleteBoard] = useAtom(deleteBoardAtom);
  const { t } = useTranslation('Library.Board');

  const navigate = useNavigate();

  // @ts-expect-error icon name is dynamic
  const CurrentIcon = boardIcons[board?.icon?.name] || boardIcons.Planet;
  const color = `var(${board?.icon?.color || PALETTE[0]})`;

  const onBoardDelete = async () => {
    if (!board) return;
    try {
      const { error } = await deleteBoardAPI(board.id);
      if (error) {
        return;
      }
      deleteBoard(board.id);
      navigate('/boards', { replace: true });
    } catch (e) {
      toast((e as Error).message);
    }
  };

  const onBoardEditFinish = (data: Partial<Board>) => {
    setBoardDetail({
      ...(board as Board),
      name: data.name!,
      icon: data.icon!,
    });
  };

  const onBoardPinChange = (pin: Date | undefined) => {
    setBoardDetail({
      ...(board as Board),
      pinned_at: pin,
    });
  };

  if (!board) return null;

  return (
    <div className="flex items-center justify-between w-full gap-4">
      <div className="relative flex h-[48px] w-[48px] items-center justify-center rounded-[10px] bg-snip-card">
        <CurrentIcon
          size={28}
          style={{
            color,
          }}
        />
      </div>

      <div className="flex flex-col flex-1">
        {hideTitle ? null : (
          <>
            <div className="flex text-xl font-medium truncate">
              <div className="max-w-[25vw] truncate">{board.name}</div>
            </div>
            <div className="text-xs font-normal text-caption-fg">
              {`${board.items.length} items · Created `}
              <TimeSince dateString={board.created_at} />
            </div>
          </>
        )}
      </div>

      <BoardAdderDialog board={board} align={'end'}>
        <Button
          size="icon"
          // variant=""
          className="w-8 h-8 rounded-full"
        >
          <Plus size={16} className="cursor-pointer" />
        </Button>
      </BoardAdderDialog>
      <BoardModifyButton
        board={board}
        buttonWidth={32}
        itemsCount={board.items.length}
        onBoardEditFinish={onBoardEditFinish}
        onBoardDelete={onBoardDelete}
        onBoardPinChange={onBoardPinChange}
        onArchiveChange={(status) => {
          toast(
            status === BoardStatusEnum.OTHER
              ? t('archivedLongDesc', { boardName: board.name })
              : t('unarchivedLongDesc', { boardName: board.name }),
            {
              position: 'bottom-center',
              duration: 5000,
            },
          );
        }}
      >
        <Button size="icon" className="w-8 h-8 rounded-full bg-snip-card text-secondary-fg">
          <Ellipsis size={16} />
        </Button>
      </BoardModifyButton>
    </div>
  );
}
