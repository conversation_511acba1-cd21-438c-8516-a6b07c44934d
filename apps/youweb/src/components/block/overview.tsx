'use client';

import type { Block } from '@repo/common/types/block/types';
import type { GenerateSnipOverviewParam, SnipBlock } from '@repo/common/types/snip/app-types';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { Snip, SnipTypeEnum, SnipVideo } from '@repo/ui-business-snip';
import { useUpdateEffect } from 'ahooks';
import { useAtom } from 'jotai';
import { marked } from 'marked';
import {
  type ForwardedRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { convertTimestampToSeconds, youTubePlayerAtom } from '@/hooks/scoped/useYouTubePlayer';
import { userAtom } from '@/hooks/useUser';
import { callHTTPStream } from '@/utils/callHTTP';
import type { RestErrorInfo } from '@/utils/error';
import { StreamDataTypeEnum, type StreamMessage } from '@/utils/server';
import { removeMarkdownLinks } from '@/utils/string';
import { isBilibili, isYouTube } from '@/utils/utils';
import { LoadingSpinner } from '../../../../../packages/ui/src/components/custom/loading-spinner';
import { highlightRange } from '../highlight/highlight';
import { getRangeInfo } from '../highlight/range';
import styles from './block.module.css';
import './overview.css';
import { type AllLanguageEnumKeys, iso6391ToLanguage } from '@repo/common/types';
import { useTranslation } from '@/hooks/useTranslation';
import { hasNoTranscripts } from '@/pages/snips/utils';
import { getLanguageEnum } from '@/utils/language';

const colorIndexMap: Record<number, string> = {
  1: 'dark-blue',
  2: 'blue',
  3: 'purple',
  4: 'green',
  5: 'red',
  0: 'yellow',
};

const ANCHOR_CLASS = 'ym-citation-trigger';

let h3history: Record<number, { animated: boolean; color: string }> = {};
let h4history: Record<number, { animated: boolean }> = {};
let h3IndexCounter = 0;
let h4IndexCounter = 0;

const refreshCounter = () => {
  h3IndexCounter = 0;
  h4IndexCounter = 0;
};

const refreshHistory = () => {
  refreshCounter();
  h3history = {};
  h4history = {};
};

marked.use({
  renderer: {
    // @ts-expect-error 预期内
    link(_href, _titlee, text) {
      return text;
    },
    // @ts-expect-error 预期内
    heading(text, level) {
      if (level === 3) {
        h3IndexCounter += 1;
        // 拿到当前的颜色
        let color = '';
        const curH3History = h3history[h3IndexCounter];
        const shouldAnimate = !curH3History?.animated;
        if (curH3History) {
          color = curH3History.color;
        } else {
          color = colorIndexMap[h3IndexCounter % Object.keys(colorIndexMap).length];
          h3history[h3IndexCounter] = { animated: true, color };
        }
        // 如果当前 h3 是最后一个
        const isLastH3 = h3IndexCounter === Object.keys(h3history).length;
        const pointElement = `<div class="${shouldAnimate && styles['animated-point']} ${styles.point} top-[-2px] left-[-37px]"><button anchor-action="ov-h3-${h3IndexCounter}"></button></div>`;
        return `${h3IndexCounter > 1 ? '</div>' : ''}<div class="${styles[color]} relative">
        <div class="${styles.vline} ${isLastH3 && styles.lastline}"></div>
        <h3 class=" relative ml-[31px]" anchor="ov-h3-${h3IndexCounter}" index="${h3IndexCounter}">${text} ${pointElement}</h3>`;
      }

      if (level === 4) {
        h4IndexCounter += 1;
        const shouldAnimate = !h4history[h4IndexCounter]?.animated;
        h4history[h4IndexCounter] = { animated: true };
        const arcElement = `<div anchor="ov-h4-${h4IndexCounter}" class="${shouldAnimate && styles['animated-arc']} ${styles.arc} top-[-19px] left-[-43px] "/>`;
        const pointElement = `<div class="${shouldAnimate && styles['animated-point']} ${styles.point} top-[17px] left-[17px]"><button anchor-action="ov-h4-${h4IndexCounter}" anchor="ov-h4-${h4IndexCounter}"></button></div>`;
        return `<h4 h3="${h3IndexCounter}" index="${h4IndexCounter}" class=" relative ml-12" anchor="ov-h4-${h4IndexCounter}">${text} ${arcElement} ${pointElement}</h4>`;
      }
      return `<h${level}>${text}</h${level}>`;
    },
    list(token) {
      return `<ul class="ml-8" h3="${h3IndexCounter}" h4="${h4IndexCounter}">${token}</ul>`;
    },
    listitem(token) {
      // 删除锚点后面的内容
      const text = token.text.replace(/(\[citation-[^\]]+\]).*/, '$1');
      const citation = token.text.match(/\[citation-([^\]]+)\]/)?.[1];
      const hasCitation = !!citation;
      return `<li data-citation="${citation}" class="text-secondary-fg ${styles.li} ${hasCitation && ' cursor-pointer'} ${hasCitation && ANCHOR_CLASS}">${text}</li>`;
    },
    paragraph(text) {
      return `<p>${text}</p>`;
    },
  },
});

export interface OverviewBlockProps extends React.HTMLAttributes<HTMLDivElement> {
  snip?: Snip;
  pointBackground?: string;
  onLoadingChange?: (loading: boolean) => void;
  onTraceIdChange?: (traceId: string) => void;
  onGenerateFinish?: (overviewData: SnipBlock['contents'][0]) => void;
}

export interface OverviewBlockHandle {
  generateNewOverview: (params: { regenerate?: boolean }) => Promise<void>;
  abortGeneration: () => void;
}

const OverviewBlock = forwardRef<OverviewBlockHandle, OverviewBlockProps>(
  (
    {
      className,
      snip: initialSnip,
      pointBackground,
      onLoadingChange,
      onTraceIdChange,
      onGenerateFinish,
    }: OverviewBlockProps,
    ref: ForwardedRef<OverviewBlockHandle>,
  ) => {
    // @ts-expect-error 预期内
    const lastOverview = initialSnip?.overview?.contents?.[0];
    const snipPlain =
      // @ts-expect-error 预期内
      initialSnip?.content?.plain ||
      // @ts-expect-error 预期内
      initialSnip?.show_notes?.plain ||
      // @ts-expect-error 预期内
      initialSnip?.transcript?.contents?.find((tran) => tran.language === 'en-US')?.plain ||
      // @ts-expect-error 预期内
      initialSnip?.transcript?.contents?.[0]?.plain;

    const [overview, setOverview] = useState<string>(lastOverview?.plain || 'No content');
    const [latestOverviewData, setLatestOverviewData] = useState<Block>();

    const overviewRef = useRef<string>('');
    useEffect(() => {
      overviewRef.current = overview;
    }, [overview]);

    const latestOverviewRef = useRef<Block>();
    useEffect(() => {
      latestOverviewRef.current = latestOverviewData;
    }, [latestOverviewData]);

    const [loading, setLoading] = useState(false);

    const [overviewTraceId, setOverviewTraceId] = useState(
      // @ts-expect-error 预期内
      initialSnip?.overview?.contents?.[0]?.trace_id || '',
    );

    const [user] = useAtom(userAtom);
    const { t } = useTranslation('Library.SnipDetail');

    const aiResponseLanguage = user?.preference?.ai_response_language;

    // 新增：AbortController ref 用于终止生成
    const abortControllerRef = useRef<AbortController | null>(null);

    useEffect(() => {
      onTraceIdChange?.(overviewTraceId);
    }, [overviewTraceId, onTraceIdChange]);

    useEffect(() => {
      onLoadingChange?.(loading);
    }, [loading, onLoadingChange]);

    // 刷新全局变量
    useEffect(() => {
      refreshHistory();
    }, []);

    const generateOverview = async (language: AllLanguageEnumKeys, regenerate = false) => {
      // 计算语言
      let realLanguage: string = language;
      if (language === 'system') {
        realLanguage = iso6391ToLanguage[navigator.language] || 'en-US';
      }
      if (language === 'follow-content') {
        realLanguage = getLanguageEnum(removeMarkdownLinks(snipPlain || '')) || 'en-US';
      }

      // 创建新的 AbortController
      abortControllerRef.current = new AbortController();

      const { error } = await callHTTPStream<StreamMessage<Block>>(
        '/api/v1/snip/generateOverview',
        {
          method: 'POST',
          body: {
            snip_id: initialSnip?.id,
            // @see https://stackoverflow.com/questions/8199760/how-to-get-the-browser-language-using-javascript
            response_language: realLanguage,
            regenerate,
          } as GenerateSnipOverviewParam,
          signal: abortControllerRef.current.signal, // 传入 signal
          onMessage: (line) => {
            switch (line.type) {
              case StreamDataTypeEnum.DATA:
                if (line.dataType === 'SnipOverview') {
                  // 找到符合当前语言的
                  const currentOverview = line.data.contents.find(
                    (item) => item.language === realLanguage,
                  );
                  if (currentOverview) {
                    setOverview(currentOverview.plain || '');
                    setLatestOverviewData(line.data);
                  }
                }
                break;
              case StreamDataTypeEnum.CONTENT: {
                setOverview((prev) => prev + line.data);
                break;
              }
              case StreamDataTypeEnum.ERROR:
                // 处理错误
                break;
              case StreamDataTypeEnum.TRACE_ID: {
                setOverviewTraceId(line.trace_id);
                break;
              }
              default:
                break;
            }
          },
        },
      );
      if (error) {
        throw error;
      }
    };

    const [youTubePlayer] = useAtom(youTubePlayerAtom);
    const fromYouTube = initialSnip && isYouTube(initialSnip as SnipVideo);
    const fromBilibili = initialSnip && isBilibili(initialSnip as SnipVideo);
    const fromVoice = initialSnip && initialSnip.type === SnipTypeEnum.voice;
    const noTranscript = hasNoTranscripts(initialSnip);

    const contentRef = useRef<HTMLDivElement>(null);
    const [html, setHtml] = useState('');
    const [hiddenH3Map, setHiddenH3Map] = useState<Record<string, boolean>>({});
    const [hiddenH4Map, setHiddenH4Map] = useState<Record<string, boolean>>({});

    const { trackButtonClick } = useTrackActions();

    const generateNewOverview = async ({ regenerate = false }: { regenerate?: boolean }) => {
      setOverview('');
      setLoading(true);
      try {
        await generateOverview(aiResponseLanguage as AllLanguageEnumKeys, regenerate);

        let overviewData = latestOverviewRef.current?.contents?.[0];
        if (overviewData) {
          overviewData = {
            ...overviewData,
            raw: overviewRef.current,
            plain: overviewRef.current,
          };
          onGenerateFinish?.(overviewData);
        }
      } catch (error) {
        // 如果是用户主动中止，不显示错误
        if ((error as RestErrorInfo)?.code === 'AbortError') {
          console.log('Overview generation aborted by user');
          return;
        }
        throw error;
      } finally {
        setLoading(false);
        abortControllerRef.current = null; // 清理 controller
      }
    };

    // 新增：中止生成的方法
    const abortGeneration = () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
        setLoading(false);
      }
    };

    // 组件卸载时中止生成
    useEffect(() => {
      return () => {
        abortGeneration();
      };
    }, []);

    // initialOverview 为空时，需要先自动触发一次生成
    useEffect(() => {
      // 无字幕或者有正在处理中的字幕，暂不生成 overview
      if (lastOverview || hasNoTranscripts(initialSnip)) {
        return;
      }
      generateNewOverview({ regenerate: false });
    }, []);

    // 切换语言时重新生成
    useUpdateEffect(() => {
      generateNewOverview({ regenerate: false });
    }, [aiResponseLanguage]);

    useEffect(() => {
      summarizeToHtml(overview || '');
    }, [overview]);

    // 加载完成后，再触发一轮渲染，并且处理最后一条线
    useEffect(() => {
      if (loading) {
        return;
      }
      if (!overview) {
        return;
      }
      summarizeToHtml(overview || '');
      setTimeout(() => {
        // 监听所有 li 的 hover 事件，然后修改 li 内的 button 的 style
        const allLi = document.querySelectorAll('li');
        allLi.forEach((li) => {
          const button = li.querySelector('button');
          if (!button) return;
          li.addEventListener('mouseover', () => {
            button.style.display = 'inline-flex';
          });
          li.addEventListener('mouseleave', () => {
            button.style.display = 'none';
          });
        });
      }, 200);
    }, [loading]);

    useEffect(() => {
      const handlePointClick = (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        if (target?.getAttribute('anchor-action')) {
          e.preventDefault();
          const anchor = target.getAttribute('anchor-action');

          // 上报埋点
          trackButtonClick('overview_tree_point_click', {
            anchor,
          });

          // 处理 h3 点击
          if (anchor?.startsWith('ov-h3')) {
            const currentH3Index = document
              .querySelector(`h3[anchor="${anchor}"]`)
              ?.getAttribute('index');
            if (!currentH3Index) return;

            setHiddenH3Map((prev) => ({
              ...prev,
              [currentH3Index]: !prev[currentH3Index],
            }));
          }

          // 处理 h4 点击
          if (anchor?.startsWith('ov-h4')) {
            const currentH4Index = document
              .querySelector(`h4[anchor="${anchor}"]`)
              ?.getAttribute('index');
            if (!currentH4Index) return;

            setHiddenH4Map((prev) => {
              const nextStatus = !prev[currentH4Index];
              return {
                ...prev,
                [currentH4Index]: nextStatus,
              };
            });
          }
        }
      };

      window.addEventListener('click', handlePointClick);

      // 清理函数
      return () => {
        window.removeEventListener('click', handlePointClick);
      };
    }, []); // 添加依赖项

    const summarizeToHtml = async (markdown: string) => {
      refreshCounter();
      const output = await marked(markdown);
      const formattedHtml = output.replace(
        /\[citation-([^\]]+)\]/g,
        `<button data-citation="$1" style="display:none" class="${ANCHOR_CLASS} ym-citation-btn ${styles.anchor}"><svg class=" pointer-events-none" width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.645277 3.07963L7.18503 0.787384C7.2285 0.772127 7.27545 0.769787 7.32022 0.780645C7.36499 0.791503 7.40566 0.815095 7.43731 0.84857C7.46896 0.882044 7.49024 0.923969 7.49857 0.969277C7.50691 1.01458 7.50194 1.06134 7.48428 1.10388L4.99678 7.09838C4.91253 7.30238 4.62028 7.29213 4.54978 7.08263L3.66553 4.44988C3.65279 4.4126 3.63112 4.379 3.6024 4.35202C3.57368 4.32504 3.53879 4.30551 3.50078 4.29513L0.662527 3.53488C0.435277 3.47463 0.423527 3.15688 0.645277 3.07938V3.07963Z" fill="currentColor" fill-opacity="0.5"/>
</svg>
</button>`,
      );
      setHtml(formattedHtml);
    };

    useEffect(() => {
      try {
        Object.keys(hiddenH4Map).forEach((key) => {
          // 隐藏对应的 dom
          const childs = document.querySelectorAll(`[h4="${key}"]`);
          if (childs.length) {
            childs.forEach((child) => {
              if (hiddenH4Map[key]) {
                child.classList.add(styles.hiddenByH3);
              } else {
                child.classList.remove(styles.hiddenByH3);
              }
            });
          }
          // 改变对应的 point 的样式
          const point = document.querySelector(`[anchor-action="ov-h4-${key}"]`);
          if (hiddenH4Map[key]) {
            point?.classList.add(styles['point-collapse']);
          } else {
            point?.classList.remove(styles['point-collapse']);
          }
        });
        Object.keys(hiddenH3Map).forEach((key) => {
          const childs = document.querySelectorAll(`[h3="${key}"]`);
          if (childs.length) {
            childs.forEach((child) => {
              if (hiddenH3Map[key]) {
                child.classList.add(styles.hiddenByH4);
              } else {
                child.classList.remove(styles.hiddenByH4);
              }
            });
          }
          // 改变对应的 point 的样式
          const point = document.querySelector(`[anchor-action="ov-h3-${key}"]`);
          if (hiddenH3Map[key]) {
            point?.classList.add(styles['point-collapse']);
          } else {
            point?.classList.remove(styles['point-collapse']);
          }
        });
      } catch (_error) {
        // logDev('hiding tree error', error)
      }
    }, [html, hiddenH3Map, hiddenH4Map]);

    // 监听锚点的点击事件
    useEffect(() => {
      const handleCitationClick = async (e: MouseEvent) => {
        const target = e.target as HTMLElement;
        if (!target?.classList.contains(ANCHOR_CLASS)) return;

        e.preventDefault();
        const citation = target.getAttribute('data-citation');
        // 上报埋点
        trackButtonClick('overview_citation_click', {
          citation_type: initialSnip?.type === 'video' ? 'subtitle' : 'article',
          citation,
        });

        if (!citation) return;

        // 处理视频类型的引用
        if (fromYouTube) {
          const timestamp = convertTimestampToSeconds(citation);
          youTubePlayer?.seekTo(timestamp, true);
          return;
        }

        if (fromBilibili) {
          window.postMessage({ type: 'youmind_seek_bilibili_iframe', timestamp: citation }, '*');
          return;
        }

        if (fromVoice) {
          window.postMessage({ type: 'youmind_seek_audio_player', timestamp: citation }, '*');
          return;
        }

        // 处理文章类型的引用
        const citations = citation.split(',');
        const citationEles = citations
          .map((cite: string) =>
            document.querySelector<HTMLElement>(`[data-ym-citation="${cite}"]`),
          )
          .filter((ele): ele is HTMLElement => ele !== null);

        if (citationEles.length === 0) return;

        // 滚动到第一个引用位置
        citationEles[0].scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center',
        });

        // 高亮引用范围
        const [firstCitation, lastCitation] = [
          citationEles[0],
          citationEles[citationEles.length - 1],
        ];

        const firstNode = firstCitation.firstChild;
        const lastNode = lastCitation.lastChild;

        if (firstNode && lastNode) {
          try {
            const range = document.createRange();
            range.setStart(firstNode, 0);
            const textLength = lastNode.textContent?.length ?? 0;
            range.setEnd(lastNode, Math.min(textLength, lastNode.textContent?.length || 0));
            const rangeInfo = await getRangeInfo(range, document.body);
            if (!rangeInfo) {
              return;
            }

            // highlightRange(range, {
            //   type: "highlight",
            //   rangeInfo,
            // });
            highlightRange(range, {
              type: 'anchor',
              rangeInfo,
              duration: 2000,
            });
          } catch (error) {
            console.warn('highlight error', error);
          }
        }
      };

      window.addEventListener('click', handleCitationClick);

      return () => {
        window.removeEventListener('click', handleCitationClick);
      };
    }, [youTubePlayer, fromYouTube, fromBilibili, fromVoice]);

    useImperativeHandle(ref, () => ({
      generateNewOverview,
      abortGeneration,
    }));

    return (
      <div
        className={className}
        style={{ '--point-background': pointBackground } as React.CSSProperties}
      >
        {noTranscript ? (
          <div className="body flex flex-col items-center justify-center text-secondary-fg">
            <img
              src="https://cdn.gooo.ai/assets/no-content-in-unavailable.png"
              alt="overview is unavailable"
              className="mb-4 mt-[200px] h-[128px] w-[128px]"
            />
            {t('noOverview')}
          </div>
        ) : (
          <>
            <div
              className="overview-container body"
              ref={contentRef}
              dir="auto"
              // biome-ignore lint/security/noDangerouslySetInnerHtml: 啊不行吗
              dangerouslySetInnerHTML={{ __html: html }}
              // onMouseOver={handleMouseOver}
              // onMouseLeave={handleMouseLeave}
            ></div>
            {loading && <LoadingSpinner />}
          </>
        )}
      </div>
    );
  },
);

OverviewBlock.displayName = 'OverviewBlock';
export default OverviewBlock;
