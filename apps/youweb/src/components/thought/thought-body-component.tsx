import './thought-body.less';
import { AutoSizeInput, type AutoSizeInputRef } from '@repo/ui/components/custom/auto-size-input';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import {
  type ExtensionInitContent,
  TableColMenu,
  TableRowMenu,
  ThoughtEditor,
  ThoughtEditorProps,
  ThoughtEditorReadyParams,
} from '@repo/ui-business-editor';
import type { Editor } from '@tiptap/core';
import { debounce, throttle } from 'lodash-es';
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { type PatchThought, type Thought, ThoughtTitleTypeEnum } from '@/typings/thought';
import { getThoughtEditorExtensions } from './editor-options';
import { ThoughtGenerateTitleIcon } from './icon/thought-generate-title';
import { TextMenu } from './text-menu';
import type {
  OnSelectionChangeParams,
  ThoughtBodyComponentProps,
  ThoughtBodyComponentRef,
} from './type';

const MemoizedThoughtEditor = memo<ThoughtEditorProps>((props) => {
  return <ThoughtEditor {...props} />;
});

MemoizedThoughtEditor.displayName = 'MemoizedThoughtEditor';

export const ThoughtBodyComponent = forwardRef<ThoughtBodyComponentRef, ThoughtBodyComponentProps>(
  (props, ref) => {
    const { thought, onUpdate = () => {}, onSelectionChange, workflow, id, onReady } = props;

    const [isGenTitle, setIsGenTitle] = useState(false);
    const [editor, setEditor] = useState<Editor | null>(null);
    const [title] = useState<string>(thought.title || '');
    const [titleType, setTitleType] = useState(thought.title_type);

    const titleELRef = useRef<AutoSizeInputRef>(null);
    const titleTypeValueRef = useRef<ThoughtTitleTypeEnum>(thought.title_type);
    const isGenTitleRef = useRef<boolean>(isGenTitle);
    const onUpdateRef = useRef<ThoughtBodyComponentRef['onUpdate']>(onUpdate);
    const onSelectionChangeRef =
      useRef<ThoughtBodyComponentRef['onSelectionChange']>(onSelectionChange);
    const thoughtRef = useRef<Thought>(thought);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      titleTypeValueRef.current = titleType;
      isGenTitleRef.current = isGenTitle;
      onUpdateRef.current = onUpdate;
      onSelectionChangeRef.current = onSelectionChange;
      thoughtRef.current = thought;
    }, [title, isGenTitle, titleType, onUpdate, onSelectionChange, thought]);

    // 创建节流的 editTitle 函数，500ms 执行一次
    // 注意：这里不传 title 参数，因为缓存会立即更新，editTitle 主要处理业务逻辑
    const throttledEditTitle = useMemo(
      () =>
        throttle(() => {
          workflow?.editTitle();
        }, 500),
      [workflow],
    );

    // 创建防抖的 onUpdate 函数，100ms 执行一次
    const debouncedOnUpdate = useMemo(
      () =>
        debounce((thoughtData: PatchThought) => {
          onUpdateRef.current(thoughtData);
        }, 100),
      [],
    );

    // 组件卸载时取消节流和防抖
    useEffect(() => {
      return () => {
        throttledEditTitle.cancel();
        debouncedOnUpdate.cancel();
      };
    }, [throttledEditTitle, debouncedOnUpdate]);

    useImperativeHandle(ref, () => ({
      onUpdate: (data: PatchThought) => {
        onUpdateRef.current({ ...thoughtRef.current, ...data } as PatchThought);
      },
      getTitle: () => titleELRef.current?.getValue() || '',
      setTitle: (newTitle: string) => {
        titleELRef.current?.setValue(newTitle);
      },
      getIsGenTitle: () => isGenTitleRef.current,
      setIsGenTitle: (isGenTitle: boolean) => {
        setIsGenTitle(isGenTitle);
      },
      getTitleType: () => titleTypeValueRef.current,
      setTitleType: (titleType: ThoughtTitleTypeEnum) => {
        setTitleType(titleType);
      },
      getIsFocused: () => titleELRef.current?.getIsFocused() || false,
      onSelectionChange: (selection: OnSelectionChangeParams) => {
        onSelectionChangeRef.current?.(selection);
      },
      focusTitle: () => {
        if (titleELRef.current) {
          titleELRef.current.focus();
        }
      },
    }));

    const handleTitleInput = (title: string) => {
      onUpdate?.({ id, title, title_type: ThoughtTitleTypeEnum.manual });
      workflow?.updateTitleCache(title);
      workflow?.updateTitleTypeCache(ThoughtTitleTypeEnum.manual);
      throttledEditTitle();
    };

    const onEditorReady = useCallback(
      (params: ThoughtEditorReadyParams) => {
        const { editor } = params;
        setEditor(editor);
        onReady?.(params);
      },
      [onReady],
    );

    const handleGenTitle = useCallback(() => {
      if (isGenTitle || !workflow) {
        return;
      }
      workflow.manualUpdateAITitle();
    }, [workflow, isGenTitle]);

    return (
      <div
        className="text-foreground flex h-full min-h-[70vh] flex-col pb-[30vh]"
        ref={containerRef}
        onClick={(e) => {
          // 只有当点击的是容器本身，而不是编辑器内容时，才执行兜底逻辑
          if (e.target === e.currentTarget) {
            e.stopPropagation();
            editor?.commands.focus('end', {
              scrollIntoView: false,
            });
          }
        }}
      >
        <div
          id="thought-title-container"
          className="relative items-start justify-between w-full mb-4 group"
        >
          {editor?.isEditable && (
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div
                    className="absolute left-[-28px] pt-1 cursor-pointer opacity-0 hover:!opacity-100 group-hover:opacity-80 before:content-[''] before:absolute before:top-[-20px] before:left-[-20px] before:right-[-5px] before:bottom-[-20px] transition-all duration-300"
                    onClick={handleGenTitle}
                  >
                    <ThoughtGenerateTitleIcon size={20} />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Generate title</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <AutoSizeInput
            ref={titleELRef}
            placeholder={isGenTitle ? 'Generating title...' : 'New thought'}
            value={title}
            onInput={handleTitleInput}
            onChange={handleTitleInput}
          />
        </div>
        <MemoizedThoughtEditor
          id={id}
          content={thought.content.raw || ''}
          onReady={onEditorReady}
          className="thought-body-editor"
          extensions={(context: ExtensionInitContent) =>
            getThoughtEditorExtensions({
              id,
              initContent: context,
            })
          }
        />
        {editor && <TextMenu editor={editor} />}
        {editor && <TableRowMenu editor={editor} appendTo={containerRef} />}
        {editor && <TableColMenu editor={editor} appendTo={containerRef} />}
      </div>
    );
  },
);
