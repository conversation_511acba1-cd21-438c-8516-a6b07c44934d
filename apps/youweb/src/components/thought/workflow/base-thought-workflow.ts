import { DIFF_CHANGE_TYPE, diffTransformUtils } from '@repo/editor-common';
import { toast } from '@repo/ui/components/ui/sonner';
import type { Editor } from '@tiptap/core';
import { getText } from '@tiptap/core';
import { debounce, throttle } from 'lodash-es';
import type { RefObject } from 'react';
import type { IndexeddbPersistence } from 'y-indexeddb';
import type { Doc } from 'yjs';
import { patchThought } from '@/apis/thought';
import { PatchThought, ThoughtTitleTypeEnum, ThoughtVersion } from '@/typings/thought';
import { User } from '@/typings/user';
import { SelectionType } from '../const';
import type { ThoughtBodyComponentRef } from '../type';
import { ThoughtContentUtils } from './thought-content-utils';
import { MIN_GEN_TITLE_CONTENT_LENGTH, ThoughtTitleUtils } from './thought-title-utils';
import { ThoughtVersionUtils } from './thought-version-utils';

export interface BaseThoughtWorkflowOptions {
  id?: string;
  editor: Editor;
  ydoc: Doc;
  indexedDB?: IndexeddbPersistence | null;
  componentRef: RefObject<ThoughtBodyComponentRef>;
  versionSaveInterval?: number;
  user: User;
}

// 向服务端和向外部更新数据是 2s 一次
// 向外部更新标题数据时间会缩短一点，需要外部做一些配合，只更新标题
const THOUGHT_UPDATE_DATA_THROTTLE_TIME = 2000;

export abstract class BaseThoughtWorkflow {
  protected id?: string;
  protected editor: Editor;
  protected ydoc: Doc;
  protected indexedDB?: IndexeddbPersistence | null;
  protected componentRef: RefObject<ThoughtBodyComponentRef>;
  protected thoughtTitleUtils: ThoughtTitleUtils;
  protected executeUpdateTaskPromise: Promise<void> | null = null;
  protected shouldExecuteNext = false;
  protected versionUtils?: ThoughtVersionUtils;
  protected boundOnEditorUpdate: () => void;
  protected versionSaveInterval?: number;
  protected isDestroyed = false;

  // 内部缓存，确保在组件销毁时仍能获取到最新的 title 和 titleType
  protected cachedTitle: string = '';
  protected cachedTitleType: ThoughtTitleTypeEnum = ThoughtTitleTypeEnum.ai;

  constructor(options: BaseThoughtWorkflowOptions) {
    this.id = options.id;
    this.editor = options.editor;
    this.ydoc = options.ydoc;
    this.indexedDB = options.indexedDB;
    this.componentRef = options.componentRef;
    this.versionSaveInterval = options.versionSaveInterval;

    this.thoughtTitleUtils = new ThoughtTitleUtils({
      id: this.id,
      editor: this.editor,
      thoughtComponentRef: this.componentRef,
    });

    if (this.id) {
      this.versionUtils = new ThoughtVersionUtils({
        id: this.id,
        editor: this.editor,
        ydoc: this.ydoc,
        componentRef: this.componentRef,
        versionSaveInterval: this.versionSaveInterval,
        user: options.user,
      });
    }

    this.boundOnEditorUpdate = this.onEditorUpdate.bind(this);
    this.init();
    this.initializeCache();
  }

  /**
   * 初始化缓存值
   * 从组件中获取初始的 title 和 titleType 来设置缓存
   */
  private initializeCache(): void {
    // 初始化时同步缓存值
    this.updateCache();
  }

  /**
   * 更新缓存的 title 和 titleType
   * 确保缓存始终保持最新状态
   */
  private updateCache(): void {
    const component = this.componentRef.current;
    if (component) {
      const currentTitle = component.getTitle();
      const currentTitleType = component.getTitleType();

      if (currentTitle !== undefined) {
        this.cachedTitle = currentTitle;
      }

      if (currentTitleType !== undefined) {
        this.cachedTitleType = currentTitleType;
      }
    }
  }

  /**
   * 直接更新标题缓存
   * 用于组件输入时立即同步缓存，不依赖节流的 editTitle
   */
  updateTitleCache(title: string): void {
    this.cachedTitle = title;
  }

  /**
   * 直接更新标题类型缓存
   */
  updateTitleTypeCache(titleType: ThoughtTitleTypeEnum): void {
    this.cachedTitleType = titleType;
  }

  protected init() {
    if (this.id) {
      this.editor.on('update', this.boundOnEditorUpdate);
    }
  }

  protected onEditorUpdate() {
    if (!this.id) {
      return;
    }
    if (this.executeUpdateTaskPromise) {
      this.shouldExecuteNext = true;
      return;
    }
    this.executeUpdateTaskPromise = this.handleUpdate();
  }

  protected handleUpdate = throttle(async () => {
    if (this.isDestroyed) return;
    const contentMarkdownData = ThoughtContentUtils.getMarkdownData(this.editor.state.doc);

    this.versionUtils?.trySaveAutoVersion(contentMarkdownData);

    const isNeedGenAITitle = this.thoughtTitleUtils.shouldGenAITitle() && !this.isFocusedTitle;

    let aiTitleOrCurTitle = this.title || '';
    let genAITitleSuccess = false;

    if (isNeedGenAITitle) {
      const generatedTitle = await this.thoughtTitleUtils.getAITitle({
        content: contentMarkdownData,
      });
      if (generatedTitle) {
        aiTitleOrCurTitle = generatedTitle;
        genAITitleSuccess = true;
      }
    }

    if (this.isDestroyed) return;

    const { title, titleType } = this.getComplexTitleAfterGenAITitle(
      aiTitleOrCurTitle,
      isNeedGenAITitle && genAITitleSuccess,
    );
    if (!this.isFocusedTitle) {
      this.thoughtComponent?.setTitle(title);
    }

    this.thoughtComponent?.setTitleType(titleType);

    // 同步缓存 - 确保缓存值与组件状态保持一致
    this.cachedTitle = title;
    this.cachedTitleType = titleType;

    const content = {
      raw: ThoughtContentUtils.getRawData(this.ydoc),
      plain: contentMarkdownData,
    };
    // 子类实现具体的逻辑
    // 向外更新的时候，一定是最新数据
    this.performUpdate({
      title: this.title,
      title_type: this.titleType,
      content,
    });

    await this.callPatchThought(title, content, titleType);

    this.afterTaskFinished();
  }, THOUGHT_UPDATE_DATA_THROTTLE_TIME);

  // 抽象方法，由子类实现具体的更新逻辑
  protected abstract performUpdate(data: Partial<PatchThought>): void;

  private async callPatchThought(
    title: string,
    content: PatchThought['content'],
    titleType: ThoughtTitleTypeEnum,
  ) {
    if (!this.id) {
      return;
    }
    const { error } = await patchThought({
      id: this.id,
      title,
      content,
      title_type: titleType,
    });
    if (error) {
      this.handleFailed('Failed to update thought, please check your network');
    }
  }

  protected handleFailed(description: string): void {
    toast(description);
  }

  protected afterTaskFinished() {
    this.executeUpdateTaskPromise = null;
    if (this.shouldExecuteNext) {
      this.shouldExecuteNext = false;
      this.executeUpdateTaskPromise = this.handleUpdate();
    }
  }

  protected getComplexTitleAfterGenAITitle(
    aiTitleOrCurTitle: string,
    isAITitle: boolean,
  ): {
    title: string;
    titleType: ThoughtTitleTypeEnum;
  } {
    const curTitleType = this.thoughtComponent?.getTitleType();
    if (curTitleType === ThoughtTitleTypeEnum.manual) {
      return {
        title: this.title || '',
        titleType: ThoughtTitleTypeEnum.manual,
      };
    }
    if (isAITitle) {
      return {
        title: aiTitleOrCurTitle,
        titleType: ThoughtTitleTypeEnum.ai,
      };
    }
    return {
      title: aiTitleOrCurTitle,
      titleType: curTitleType || ThoughtTitleTypeEnum.default,
    };
  }

  getYDoc() {
    return this.ydoc;
  }

  editTitle(title?: string) {
    if (this.isDestroyed) return;

    // 设置标题类型为手动编辑
    this.thoughtComponent?.setTitleType(ThoughtTitleTypeEnum.manual);
    this.cachedTitleType = ThoughtTitleTypeEnum.manual;

    // 如果传入了 title，则更新标题（兼容旧的调用方式）
    if (title !== undefined) {
      // 过滤回车换行符
      const filteredTitle = title.replace(/[\r\n]/g, '');
      this.setTitle(filteredTitle);
    }

    this.onEditorUpdate();
  }

  async manualUpdateAITitle() {
    if (this.isDestroyed) return;
    const content = ThoughtContentUtils.getMarkdownData(this.editor.state.doc);
    if (content.length <= MIN_GEN_TITLE_CONTENT_LENGTH) {
      this.handleFailed('Content is too short to generate a meaningful title');
      return;
    }

    this.thoughtComponent?.setTitleType(ThoughtTitleTypeEnum.manual);
    // 同步 titleType 缓存
    this.cachedTitleType = ThoughtTitleTypeEnum.manual;

    const oldTitle = this.title || '';
    this.setTitle('');
    this.thoughtComponent?.setIsGenTitle(true);

    const title = await this.thoughtTitleUtils.getAITitle({
      content,
      useCache: false,
    });

    this.thoughtComponent?.setIsGenTitle(false);
    const curTitle = this.title;

    // 之前已经将标题设置为空了，如果此时还有标题, 那么就不设置
    if (curTitle) {
      return;
    }

    // 确定生成了新标题时再更新
    if (title) {
      this.setTitle(title);
      this.boundOnEditorUpdate();
    } else {
      this.setTitle(oldTitle);
    }
  }

  setTitle(title: string) {
    if (this.isDestroyed) return;
    this.thoughtComponent?.onUpdate?.({ title, id: this.id! });
    this.thoughtTitleUtils.broadcastTitleChange(title);
    this.thoughtComponent?.setTitle(title);

    // 同步缓存
    this.cachedTitle = title;
  }

  handleSelectionChange = debounce(() => {
    const { selection } = this.editor.state;
    if (selection.empty) {
      this.thoughtComponent?.onSelectionChange?.({
        plainText: '',
        selectionType: SelectionType.CURSOR,
      });
      return;
    }

    const selectedContent = selection.content();
    const tempDoc = this.editor.state.doc.type.createAndFill();
    if (!tempDoc) return;

    const fragment = selectedContent.content;
    const nodeWithSelection = tempDoc.copy(fragment);
    const node = diffTransformUtils.extractContent(nodeWithSelection, DIFF_CHANGE_TYPE.ADDED);

    this.thoughtComponent?.onSelectionChange?.({
      plainText: getText(node),
      selectionType: SelectionType.SELECTION,
    });
  }, 100);

  get title() {
    if (this.thoughtComponent) {
      const title = this.thoughtComponent.getTitle();
      this.cachedTitle = title;
      return title;
    }
    return this.cachedTitle;
  }

  get isFocusedTitle() {
    if (this.thoughtComponent) {
      const isFocused = this.thoughtComponent.getIsFocused();
      return isFocused;
    }
    return false;
  }

  get titleType() {
    if (this.thoughtComponent) {
      const titleType = this.thoughtComponent.getTitleType();
      this.cachedTitleType = titleType || ThoughtTitleTypeEnum.ai;
      return titleType;
    }
    return this.cachedTitleType;
  }

  get thoughtComponent() {
    return this.componentRef.current;
  }

  async createManualVersion(title: string, description: string) {
    return this.versionUtils?.createManualVersion(title, description);
  }

  async getVersionList() {
    return this.versionUtils?.getVersionList();
  }

  async deleteVersion(versionId: string) {
    return this.versionUtils?.deleteVersion(versionId);
  }

  async restoreThought(version: ThoughtVersion) {
    return this.versionUtils?.restoreThought(version);
  }

  getId() {
    return this.id;
  }

  /**
   * 销毁时的数据提交函数
   * 专门用于组件销毁时的最后一次数据保存，逻辑简化且可靠
   * 直接使用缓存值，确保在组件 ref 被清空后仍能获取到正确的数据
   */
  protected async finalDataSubmission(): Promise<void> {
    if (!this.id) return;

    try {
      const contentMarkdownData = ThoughtContentUtils.getMarkdownData(this.editor.state.doc);

      // 直接使用缓存值，确保在组件销毁时仍能获取到正确的数据
      // 不依赖组件 ref，避免获取不到数据的问题
      const currentTitle = this.cachedTitle || '';
      const currentTitleType = this.cachedTitleType || ThoughtTitleTypeEnum.ai;

      const content = {
        raw: ThoughtContentUtils.getRawData(this.ydoc),
        plain: contentMarkdownData,
      };

      // 保存到服务器
      await this.callPatchThought(currentTitle, content, currentTitleType);
    } catch (error) {
      console.error('Failed to submit data during destroy:', error);
    }
  }

  destroy() {
    this.isDestroyed = true;

    // 1. 取消未执行的节流函数
    this.handleUpdate.cancel();

    // 2. 执行销毁时的数据提交
    this.finalDataSubmission();

    this.editor.off('update', this.boundOnEditorUpdate);
    this.thoughtTitleUtils.destroy();
  }

  getThoughtData() {
    return {
      id: this.id,
      title: this.title,
      title_type: this.titleType,
      content: {
        raw: ThoughtContentUtils.getRawData(this.ydoc),
        plain: ThoughtContentUtils.getMarkdownData(this.editor.state.doc, DIFF_CHANGE_TYPE.ADDED),
      },
    };
  }

  getThoughtId() {
    return this.id;
  }
}
