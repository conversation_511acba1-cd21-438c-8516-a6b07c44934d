import { AutoSizeInput, type AutoSizeInputRef } from '@repo/ui/components/custom/auto-size-input';
import type { Editor } from '@tiptap/react';
import { debounce, throttle } from 'lodash-es';
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { type PatchThought, type Thought, ThoughtTitleTypeEnum } from '@/typings/thought';
import {
  MobileEditor,
  type MobileEditorProps,
  type MobileEditorRef,
} from '../../editor-kit/thought-mobile-editor/mobile-editor';
import { ThoughtGenerateTitleIcon } from '../icon/thought-generate-title';
import type { ThoughtBodyComponentRef } from '../type';
import type { MobileThoughtWorkflow } from '../workflow/mobile-thought-workflow';
import './mobile-thought-body.css';

export type MobileThoughtBodyComponentProps = {
  id: string;
  thought: Thought | null;
  workflow: MobileThoughtWorkflow | null;
  mobileEditorProps: MobileEditorProps;
  onUpdate?: (data: PatchThought) => void;
};

const MobileThoughtBodyComponentBase = forwardRef<
  ThoughtBodyComponentRef,
  MobileThoughtBodyComponentProps
>(({ workflow, thought, mobileEditorProps, onUpdate }, ref) => {
  const mobileEditorRef = useRef<MobileEditorRef>(null);
  const [isGenTitle, setIsGenTitle] = useState(false);
  const [title, setTitle] = useState(thought?.title ?? '');
  const [editor, setEditor] = useState<Editor | null>(null);
  const [titleType, setTitleType] = useState<ThoughtTitleTypeEnum>(
    thought?.title_type ?? ThoughtTitleTypeEnum.default,
  );
  const titleELRef = useRef<AutoSizeInputRef>(null);
  const isGenTitleRef = useRef(isGenTitle);
  const titleTypeRef = useRef(titleType);
  const onUpdateRef = useRef(onUpdate);
  const thoughtRef = useRef<Thought | null>(thought);

  // 更新 ref 值以跟踪最新状态
  useEffect(() => {
    isGenTitleRef.current = isGenTitle;
  }, [isGenTitle]);

  useEffect(() => {
    titleTypeRef.current = titleType;
  }, [titleType]);

  useEffect(() => {
    onUpdateRef.current = onUpdate;
  }, [onUpdate]);

  useEffect(() => {
    thoughtRef.current = thought;
  }, [thought]);

  // 创建节流的 editTitle 函数，500ms 执行一次
  // 注意：这里不传 title 参数，因为缓存会立即更新，editTitle 主要处理业务逻辑
  const throttledEditTitle = useMemo(
    () =>
      throttle(() => {
        workflow?.editTitle();
      }, 500),
    [workflow],
  );

  // 创建防抖的 onUpdate 函数，100ms 执行一次
  const debouncedOnUpdate = useMemo(
    () =>
      debounce((payload: PatchThought) => {
        onUpdateRef.current?.(payload);
      }, 100),
    [],
  );

  // 组件卸载时取消节流和防抖
  useEffect(() => {
    return () => {
      throttledEditTitle.cancel();
      debouncedOnUpdate.cancel();
    };
  }, [throttledEditTitle, debouncedOnUpdate]);

  useImperativeHandle(ref, () => ({
    getIsGenTitle: () => isGenTitleRef.current,
    getTitleType: () => titleTypeRef.current,
    getTitle: () => titleELRef.current?.getValue() || '',
    setTitleType: (titleType: ThoughtTitleTypeEnum) => setTitleType(titleType),
    setIsGenTitle: (isGenTitle: boolean) => setIsGenTitle(isGenTitle),
    setTitle: (newTitle: string) => {
      titleELRef.current?.setValue(newTitle);
    },
    getIsFocused: () => titleELRef.current?.getIsFocused() || false,
    focusTitle: () => {
      if (titleELRef.current) {
        titleELRef.current.focus();
      }
    },
    onUpdate: (data: PatchThought) => {
      onUpdate?.(data);
    },
  }));

  const handleTitleInput = useCallback(
    (title: string) => {
      setTitle(title);
      onUpdate?.({
        id: mobileEditorProps.id || '',
        title,
        title_type: ThoughtTitleTypeEnum.manual,
      });
      workflow?.updateTitleCache(title);
      workflow?.updateTitleTypeCache(ThoughtTitleTypeEnum.manual);
      throttledEditTitle();
    },
    [onUpdate, workflow, throttledEditTitle, mobileEditorProps.id],
  );

  const handleGenerateTitle = useCallback(() => {
    if (isGenTitle || !workflow) {
      return;
    }
    workflow.manualUpdateAITitle();
  }, [workflow, isGenTitle]);

  return (
    <div className="flex flex-col w-full h-auto bg-transparent mobile-thought-body-container">
      <div className="flex items-start justify-between w-full mb-3">
        <AutoSizeInput
          ref={titleELRef}
          placeholder={isGenTitle ? 'Generating...' : 'New thought'}
          value={title}
          onInput={handleTitleInput}
          onChange={handleTitleInput}
          className="w-full h-auto pr-6 cursor-text min-h-[36px] border-none bg-transparent px-0 py-0 text-2xl font-[600] outline-none"
        />
        <div className="absolute flex p-2 pt-1 right-2" onClick={handleGenerateTitle}>
          <ThoughtGenerateTitleIcon size={20} />
        </div>
      </div>
      <MobileEditor
        {...mobileEditorProps}
        ref={mobileEditorRef}
        content={thought?.content.raw}
        onCreate={(params) => {
          setEditor(params.editor);
          mobileEditorProps.onCreate?.(params);
        }}
      />
    </div>
  );
});

MobileThoughtBodyComponentBase.displayName = 'MobileThoughtBodyComponentBase';

export const MobileThoughtBodyComponent = memo(
  MobileThoughtBodyComponentBase,
  (prevProps, nextProps) => {
    return prevProps.id === nextProps.id && prevProps.workflow === nextProps.workflow;
  },
);

MobileThoughtBodyComponent.displayName = 'MobileThoughtBodyComponent';
