import type { Editor } from '@tiptap/core';
import { methodRegistry } from '@youmindinc/jsbridge';
import { useEffect, useMemo, useRef, useState } from 'react';
import type { Doc } from 'yjs';
import { Thought } from '@/typings/thought';
import { User } from '@/typings/user';
import type { EditorOnReadyParams } from '../../editor-kit/thought-mobile-editor/mobile-editor';
import type { ThoughtBodyComponentRef } from '../type';
import { MobileThoughtWorkflow } from '../workflow';
import { ThoughtRegisterToNativeMethodName } from './const';
import { getMobileThoughtBodyExtensionOptions } from './mobile-extension-options';
import { MobileThoughtBodyComponent } from './mobile-thought-body-component';
import { notifyThoughtIsReady } from './mobile-thought-ready';

export interface MobileThoughtBodyProps {
  onCreate?: (params: EditorOnReadyParams) => void;
  user: User;
  onGetThoughtData?: (data: Thought) => void;
}

export const MobileThoughtBody = (props: MobileThoughtBodyProps) => {
  const { onGetThoughtData } = props;
  const componentRef = useRef<ThoughtBodyComponentRef>(null);
  const [editor, setEditor] = useState<Editor | null>(null);
  const [ydoc, setYdoc] = useState<Doc | null>(null);
  const [thought, setThought] = useState<Thought | null>(null);

  const workflow = useMemo(() => {
    if (!editor || !ydoc || !componentRef.current) {
      return null;
    }
    const workflow = new MobileThoughtWorkflow({
      id: thought?.id,
      editor,
      ydoc,
      componentRef,
      user: props.user,
    });
    editor.commands.setWorkflow(workflow);
    return workflow;
  }, [editor, ydoc, componentRef, thought]);

  useEffect(() => {
    return () => {
      workflow?.destroy();
    };
  }, [workflow]);

  useEffect(() => {
    methodRegistry.reexportMethod(ThoughtRegisterToNativeMethodName.GET_THOUGHT_DATA, async () => {
      return workflow?.getThoughtData();
    });

    methodRegistry.reexportMethod(
      ThoughtRegisterToNativeMethodName.SET_THOUGHT_DATA,
      async (data: Thought) => {
        setInterval(() => {
          console.log('setThought', data);
        }, 5000);
        setThought(data);
        onGetThoughtData?.(data);
        return { success: true };
      },
    );
  }, [thought, workflow]);

  useEffect(() => {
    setTimeout(() => {
      notifyThoughtIsReady();
    }, 100);
  }, []);

  return (
    <MobileThoughtBodyComponent
      key={thought?.id}
      ref={componentRef}
      workflow={workflow}
      id={thought?.id ?? ''}
      thought={thought}
      mobileEditorProps={{
        id: thought?.id,
        content: thought?.content.raw,
        storeOptions: {
          indexeddbStoreEnable: false,
        },
        onCreate: (params) => {
          setEditor(params.editor);
          setYdoc(params.ydoc);
          props.onCreate?.(params);
        },
        extensionsOptions: getMobileThoughtBodyExtensionOptions(),
      }}
    />
  );
};
