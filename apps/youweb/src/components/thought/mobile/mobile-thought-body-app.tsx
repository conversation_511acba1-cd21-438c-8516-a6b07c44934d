import { ThoughtNavigationMobile } from '@repo/ui-business-editor';
import type { Editor } from '@tiptap/react';
import { methodRegistry } from '@youmindinc/jsbridge';
import { useAtom } from 'jotai';
import { useEffect, useMemo, useRef, useState } from 'react';
import { ExportImagePreviewClean, ExportImagePreviewRef } from '@/components/export-image-preview';
import { userAtom } from '@/hooks/useUser';
import { Thought } from '@/typings/thought';
import { ThoughtRegisterToNativeMethodName } from './const';
import { MobileThoughtBody, type MobileThoughtBodyProps } from './mobile-thought-body';

// thought body 系列一定需要用户登陆才能使用
export const MobileThoughtBodyApp = (props: Partial<MobileThoughtBodyProps> = {}) => {
  const [editor, setEditor] = useState<Editor | null>(null);
  const appRef = useRef<HTMLDivElement>(null);
  const [user] = useAtom(userAtom);
  const exportRef = useRef<ExportImagePreviewRef>(null);
  const [thought, setThought] = useState<Thought | null>(null);
  // const loading = false;
  const exportImageSignal = useMemo(() => new AbortController(), []);

  // 当 material.id 变化时，清理上一个 AbortController
  useEffect(() => {
    return () => {
      exportImageSignal.abort();
    };
  }, [exportImageSignal]);

  useEffect(() => {
    methodRegistry.reexportMethod(ThoughtRegisterToNativeMethodName.EXPORT_AS_IMAGE, async () => {
      return exportRef?.current?.exportImage({
        needDownload: false,
      });
    });
  }, []);

  return (
    <div ref={appRef} className="w-full px-5 py-3 overflow-x-hidden bg-transparent h-full">
      <MobileThoughtBody
        {...props}
        user={user!}
        onGetThoughtData={(data) => {
          setThought(data);
        }}
        onCreate={(params) => {
          setEditor(params.editor);
          props.onCreate?.(params);
        }}
      />
      {editor && <ThoughtNavigationMobile editor={editor} />}
      <ExportImagePreviewClean
        offscreen
        ref={exportRef}
        rerenderContentBeforeExport={{
          enable: true,
          wait: 300,
        }}
        selector={() => {
          return editor?.view?.dom as HTMLDivElement;
        }}
        title={thought?.title}
        header={<div>&nbsp;</div>}
      />
    </div>
  );
};
