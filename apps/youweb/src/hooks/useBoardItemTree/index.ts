import { BoardItemType } from '@repo/api/generated-client/snake-case/index';
import { BoardGroup } from '@repo/common/types/board-group';
import { BoardItemTypeEnum } from '@repo/common/types/board-item';
import { toast } from '@repo/ui/components/ui/sonner';
import { atom, useAtom, useAtomValue, useSetAtom } from 'jotai';
import { BoardTreeItem } from '@/typings/board-item';
import { ThoughtTitleTypeEnum } from '@/typings/thought';
import { apiClient, callAPI, callHTTP } from '@/utils/callHTTP';
import { getWorkflow } from '../../components/editor-kit/extensions/thought-adpater-extension';
import { snipDetailAtom } from '../scoped/useSnips';
import { thoughtAtom, thoughtCurEditorAtom } from '../scoped/useThoughts';
import { changePanelDataAtom, panelStateAtom, updatePanelDataEntityAtom } from '../useBoardState';
import {
  boardDetail<PERSON>tom,
  deleteBoardItemAtom,
  refreshBoardDetailAtom,
  useSetBoardDetailItems,
} from '../useBoards';
import { favoritesAtom } from '../useFavorite';

export const isBoardItemDraggableTreeHoveredAtom = atom<boolean>(false);

export const boardItemTreeAtom = atom<BoardTreeItem[]>([]);

export const boardItemTreeLoadingAtom = atom<boolean>(false);

export const collapsedBoardGroupIdsAtom = atom<string[]>([]);

export const needAddGroupBoardItemAtom = atom<BoardTreeItem | null>(null);

export const activeBoardItemIdAtom = atom<string | null>(null);

export const isEditingBoardItemAtom = atom<boolean>(false);

export const useBoardItemTree = () => {
  const [treeItems, setTreeItems] = useAtom(boardItemTreeAtom);
  const [loading, setLoading] = useAtom(boardItemTreeLoadingAtom);
  const [activeId, setActiveId] = useAtom(activeBoardItemIdAtom);
  const [needAddGroupBoardItem, setNeedAddGroupBoardItem] = useAtom(needAddGroupBoardItemAtom);
  const setFavorites = useSetAtom(favoritesAtom);

  const changePanelData = useSetAtom(changePanelDataAtom);
  const panelState = useAtomValue(panelStateAtom);
  const updatePanelDataEntity = useSetAtom(updatePanelDataEntityAtom);

  const [snipDetail, setSnipDetail] = useAtom(snipDetailAtom);

  const [thought, setThoughtDetail] = useAtom(thoughtAtom);

  const [, deleteBoardItemInUseBoards] = useAtom(deleteBoardItemAtom);
  const [collapsedBoardGroupIds, setCollapsedBoardGroupIds] = useAtom(collapsedBoardGroupIdsAtom);

  const [board] = useAtom(boardDetailAtom);
  const { setBoardItems } = useSetBoardDetailItems();
  const [, refreshBoardDetail] = useAtom(refreshBoardDetailAtom);

  const [curEditor] = useAtom(thoughtCurEditorAtom);

  /**
   * 创建一个新的分组
   */
  const startCreatingNewGroup = () => {
    const newGroupItem = {
      id: `new-group-${board?.id}`,
      entity_type: BoardItemTypeEnum.BOARD_GROUP,
      editing: true,
      children: [],
    } as unknown as BoardTreeItem;
    setTreeItems((prev) => [newGroupItem, ...prev]);
  };

  const stopEditingAnItem = (item: BoardTreeItem) => {
    setBoardItems((prev) => prev.map((i) => (i.id === item.id ? { ...i, editing: false } : i)));
  };

  const cancelEditingBoardItem = (item: BoardTreeItem) => {
    if (item.id.startsWith('new-group-')) {
      setTreeItems((prev) => prev.filter((i) => i.id !== item.id));
    } else {
      stopEditingAnItem(item);
    }
  };

  /**
   * 保存创建的分组
   */
  const saveNewGroup = async (title: string) => {
    // 可能存在多个占位 new-group，同步按前缀清理
    const response = await callAPI(
      apiClient.boardGroupApi.createBoardGroup({
        board_id: board?.id!,
        name: title,
      }),
    );
    if (response.error) {
      setTreeItems((prev) => prev.filter((item) => !item.id.startsWith('new-group-')));
      return;
    }
    const data = response.data;
    setBoardItems((prev) => {
      const validPrevItems = prev.filter(
        (item) => item.entity && !item.id.startsWith('new-group-'),
      );
      return [
        {
          ...(data as any).board_item,
          id: (data as any).board_item?.id,
          entity: data,
          parentId: null,
          depth: 0,
          children: [],
          entity_type: BoardItemTypeEnum.BOARD_GROUP,
        } as any,
        ...validPrevItems,
      ];
    });
    // 看看有没有哪个幸运儿要立马被加进去
    if (needAddGroupBoardItem) {
      moveBoardItemToBoardGroup(
        needAddGroupBoardItem.id,
        data.id,
        false,
        (data.board_item as any)?.id,
      );
      setNeedAddGroupBoardItem(null);
    }
  };

  /**
   * 开始给一个分组重命名
   */
  const startRenamingBoardItem = (boardItemId: string) => {
    setBoardItems((prev) =>
      prev.map((item) => (item.id === boardItemId ? { ...item, editing: true } : item)),
    );
  };

  /**
   * 更新编辑后的分组状态
   */
  const updateEditedBoardGroup = (params: {
    groupId: string;
    name: string;
    icon?: { name?: string; color?: string };
  }) => {
    setBoardItems((prev) =>
      prev.map((item) =>
        item.entity.id === params.groupId
          ? {
              ...item,
              entity: {
                ...item.entity,
                name: params.name,
                icon: {
                  ...(item.entity as unknown as BoardGroup).icon,
                  ...params.icon,
                },
              },
            }
          : item,
      ),
    );
  };

  /**
   * 保存分组重命名
   */
  const saveRenamedBoardItem = async (targetItem: BoardTreeItem, newName: string) => {
    // 同步更新 favorite
    if (targetItem.entity_type === BoardItemType.boardGroup) {
      setFavorites((prev) =>
        prev.map((favorite) => {
          if (favorite.entity.id === targetItem.entity.id) {
            return {
              ...favorite,
              entity: {
                ...favorite.entity,
                name: newName,
              },
            };
          }
          return favorite;
        }),
      );
    } else {
      setFavorites((prev) =>
        prev.map((favorite) => {
          if (favorite.entity.id === targetItem.entity.id) {
            return {
              ...favorite,
              entity: {
                ...favorite.entity,
                title: newName,
              },
            };
          }
          return favorite;
        }),
      );
    }
    // 同步更新 Snip Detail
    if (targetItem.entity_type === BoardItemType.snip && snipDetail?.id === targetItem.entity.id) {
      setSnipDetail({
        ...snipDetail,
        title: newName,
      });
    }
    // 同步更新 Thought Detail
    if (targetItem.entity_type === BoardItemType.thought && thought?.id === targetItem.entity.id) {
      setThoughtDetail({ ...thought, title: newName, title_type: ThoughtTitleTypeEnum.manual });
      if (curEditor) {
        const workflow = getWorkflow(curEditor);
        if (workflow?.getId() === thought.id) {
          workflow.editTitle(newName);
        }
      }
    }

    // 每个请求
    if (targetItem.entity_type === BoardItemType.boardGroup) {
      // 重命名 group
      const { data, error } = await callAPI(
        apiClient.boardGroupApi.patchBoardGroup({
          id: targetItem.entity.id,
          name: newName,
        }),
      );
      if (error) {
        toast(error.message || 'Failed to rename group');
        return;
      }
      setBoardItems((prev) =>
        prev.map((item) =>
          item.id === targetItem.id
            ? {
                ...item,
                editing: false,
                entity: { ...item.entity, ...(data as any) },
              }
            : item,
        ),
      );
    }
    if (targetItem.entity_type === BoardItemType.chat) {
      // TODO: updateChatTitle API 在新的 API 客户端中未找到对应方法，暂时保留原调用
      const { error } = await callHTTP('/api/v1/chat/updateChatTitle', {
        method: 'POST',
        body: {
          chat_id: targetItem.entity.id,
          title: newName,
        },
      });
      if (error) {
        return;
      }
      setBoardItems((prev) =>
        prev.map((item) =>
          item.id === targetItem.id
            ? {
                ...item,
                editing: false,
                entity: { ...item.entity, title: newName },
              }
            : item,
        ),
      );
    }
    if (targetItem.entity_type === BoardItemType.snip) {
      // 重命名 snip
      const { error } = await callAPI(
        apiClient.snipApi.updateSnipTitle({
          id: targetItem.entity.id,
          title: newName,
        }),
      );
      if (error) {
        toast(error.message || 'Failed to rename group');
        return;
      }

      setBoardItems((prev) =>
        prev.map((item) =>
          item.id === targetItem.id
            ? {
                ...item,
                editing: false,
                entity: { ...item.entity, title: newName },
              }
            : item,
        ),
      );
    }
    if (targetItem.entity_type === BoardItemType.thought) {
      const { data, error } = await callAPI(
        apiClient.thoughtApi.patchThought({
          id: targetItem.entity.id,
          title: newName,
          title_type: ThoughtTitleTypeEnum.manual,
        }),
      );
      if (error) {
        toast(error.message || 'Failed to rename thought');
        return;
      }
      setBoardItems((prev) =>
        prev.map((item) =>
          item.id === targetItem.id
            ? {
                ...item,
                editing: false,
                entity: { ...item.entity, title: newName },
              }
            : item,
        ),
      );
    }
  };

  /**
   * 刷新 BoardItemTree
   */
  const refreshBoardItemTree = async () => {
    setLoading(true);
    await refreshBoardDetail();
    setLoading(false);
  };

  /**
   * 移动 BoardItem 到 BoardGroup
   */
  const moveBoardItemToBoardGroup = async (
    boardItemId: string,
    boardGroupId: string,
    waitServer = true,
    boardGroupItemId?: string,
  ) => {
    if (waitServer) {
      setLoading(true);
      const { error } = await callAPI(
        apiClient.boardItemApi.moveBoardItemToBoardGroup({
          board_item_id: boardItemId,
          parent_board_group_id: boardGroupId,
        }),
      );
      if (error) {
        toast(error.message || 'Failed to move board item to board group');
        return;
      }
      await refreshBoardItemTree();
      setLoading(false);
      return;
    }
    if (!boardGroupItemId) {
      return;
    }
    setBoardItems((prev) =>
      prev.map((item) =>
        item.id === boardItemId ? { ...item, parentId: boardGroupItemId } : item,
      ),
    );
    await callAPI(
      apiClient.boardItemApi.moveBoardItemToBoardGroup({
        board_item_id: boardItemId,
        parent_board_group_id: boardGroupId,
      }),
    );
  };

  /**
   * 取消分组
   */
  const ungroup = async (boardGroupId: string) => {
    setLoading(true);
    const { error } = await callAPI(
      apiClient.boardGroupApi.ungroup({
        id: boardGroupId,
      }),
    );
    if (error) {
      toast(error.message || 'Failed to ungroup');
    }
    await refreshBoardItemTree();
    setLoading(false);
  };

  /**
   * 移动 BoardItem 到根目录
   */
  const moveBoardItemToRoot = async (boardItemId: string) => {
    setLoading(true);
    const { error } = await callAPI(
      apiClient.boardItemApi.moveBoardItemToRoot({
        board_item_id: boardItemId,
      }),
    );
    if (error) {
      toast(error.message || 'Failed to move board item to root');
      return;
    }
    await refreshBoardItemTree();
    setLoading(false);
  };

  /**
   * 删除 BoardGroup
   */
  const deleteBoardGroup = async (boardGroupItem: BoardTreeItem) => {
    // 删除 BoardGroup 后，也需要删除 BoardItemTree 中的所有子元素
    setBoardItems((prev) =>
      prev.filter(
        (item) =>
          item.id !== boardGroupItem.id && (item as BoardTreeItem).parentId !== boardGroupItem.id,
      ),
    );
    // 删除 favorite
    setFavorites((prev) =>
      prev.filter((favorite) => favorite.entity.id !== boardGroupItem.entity.id),
    );
    // 取消所有子元素的 favorites
    boardGroupItem.children?.map((item) => {
      setFavorites((prev) => prev.filter((favorite) => favorite.entity.id !== item.entity.id));
    });
    // 直接跳转到 board
    changePanelData();
    const { error } = await callAPI(
      apiClient.boardGroupApi.deleteBoardGroup({
        id: boardGroupItem.entity.id,
      }),
    );
    if (error) {
      toast(error.message || 'Failed to delete board group');
      return;
    }
  };

  /**
   * 删除 BoardItem
   */
  const deleteBoardItem = async (boardItemId: string) => {
    await deleteBoardItemInUseBoards(boardItemId);
  };

  /**
   * 折叠 BoardGroup
   */
  const collapseBoardGroup = (boardGroupId: string) => {
    setCollapsedBoardGroupIds((prev) => [...prev, boardGroupId]);
  };

  /**
   * 改变 BoardGroup 的折叠状态
   */
  const toggleCollapseBoardGroup = (boardGroupId: string) => {
    setCollapsedBoardGroupIds((prev) =>
      prev.includes(boardGroupId)
        ? prev.filter((id) => id !== boardGroupId)
        : [...prev, boardGroupId],
    );
  };

  /**
   * 分享 BoardItem
   */
  const shareBoardItem = async (targetItem: BoardTreeItem) => {
    if (targetItem.entity_type === BoardItemType.thought) {
      const { data, error } = await callAPI(
        apiClient.thoughtApi.getThought({
          id: targetItem.entity.id,
        }),
      );

      if (error) {
        return;
      }

      setBoardItems((prev) =>
        prev.map((item) =>
          item.id === targetItem.id
            ? {
                ...item,
                editing: false,
                entity: { ...item.entity, ...(data as any) },
              }
            : item,
        ),
      );

      if (panelState.panelData?.id === targetItem.id) {
        updatePanelDataEntity({
          visibility: (data as any).visibility,
        });
      }
    }

    if (targetItem.entity_type === BoardItemType.snip) {
      const { data, error } = await callAPI(
        apiClient.snipApi.getSnip({
          id: targetItem.entity.id,
        }),
      );

      if (error) {
        return;
      }

      setBoardItems((prev) =>
        prev.map((item) =>
          item.id === targetItem.id
            ? {
                ...item,
                editing: false,
                entity: { ...item.entity, ...(data as any) },
              }
            : item,
        ),
      );

      if (panelState.panelData?.id === targetItem.id) {
        updatePanelDataEntity({
          visibility: (data as any).visibility,
        });
      }
    }
  };

  return {
    treeItems,
    loading,
    collapsedBoardGroupIds,
    activeId,
    setActiveId,
    stopEditingAnItem,
    cancelEditingBoardItem,
    refreshBoardItemTree,
    setTreeItems,
    startCreatingNewGroup,
    saveNewGroup,
    startRenamingBoardItem,
    saveRenamedBoardItem,
    moveBoardItemToBoardGroup,
    ungroup,
    moveBoardItemToRoot,
    deleteBoardGroup,
    deleteBoardItem,
    collapseBoardGroup,
    toggleCollapseBoardGroup,
    shareBoardItem,
    updateEditedBoardGroup,
  };
};
