'use client';

import { BoardStatusEnum } from '@repo/common/types/board/types';
import { toast } from '@repo/ui/components/ui/sonner';
import { useAtom } from 'jotai';
import { partition } from 'lodash-es';
import { useMemo, useRef, useState } from 'react';
import BoardListHeader, { type BoardSortBy } from '@/components/board/board-list-header';
import { BoardPreview } from '@/components/board/board-preview';
import { CollapsePane } from '@/components/ui/custom/collapse-pane';
import { boardsAtom } from '@/hooks/useBoards';
import { useTranslation } from '@/hooks/useTranslation';
import { Board } from '@/typings/board';
import { cn } from '@/utils/utils';
import { BoardOnboarding } from './onboarding';

export default function List() {
  const [boards] = useAtom(boardsAtom);
  const [isArchiveOpen, setIsArchiveOpen] = useState(false);
  const [sortBy, setSortBy] = useState<BoardSortBy>('updated_at');
  const archivedRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation('Library.Board');

  const sortedBoards = useMemo(() => {
    return [...boards].sort((a, b) => {
      const dateA = new Date(sortBy === 'created_at' ? a.created_at : a.updated_at);
      const dateB = new Date(sortBy === 'created_at' ? b.created_at : b.updated_at);
      return dateB.getTime() - dateA.getTime(); // Descending order
    });
  }, [boards, sortBy]);

  const [activeBoards, archivedBoards] = useMemo(() => {
    return partition(
      sortedBoards,
      (board) => board.status === BoardStatusEnum.IN_PROGRESS || !board.status,
    );
  }, [sortedBoards]);

  const handleArchiveChange = (board: Board, status: BoardStatusEnum) => {
    toast(
      status === BoardStatusEnum.OTHER
        ? t('archivedLongDesc', { boardName: board.name })
        : t('unarchivedLongDesc', { boardName: board.name }),
      {
        position: 'bottom-center',
        duration: 5000,
        label: 'Open',
        onClick: () => {
          status === BoardStatusEnum.OTHER && setIsArchiveOpen(true);
          setTimeout(() => {
            const boardElement = document.getElementById(`board-${board.id}`);
            if (boardElement) {
              boardElement.scrollIntoView({
                behavior: 'smooth',
              });
            }
          }, 300);
        },
      },
    );
  };

  const renderContent = () => {
    if (!boards.length) {
      return <BoardOnboarding />;
    }

    return (
      <div className="flex flex-col gap-10">
        {activeBoards.length === 0 && archivedBoards.length > 0 && (
          <div className="my-[100px] flex flex-col items-center justify-center">
            <img
              src="https://cdn.gooo.ai/assets/task-complete.png"
              alt="All done"
              className="mb-3 h-[128px] w-[128px]"
            />
            <h2 className="mb-2 text-xl font-medium">{t('allDone')}</h2>
            <p className="text-sm text-caption-fg">{t('allDoneTips')}</p>
          </div>
        )}
        {activeBoards.length > 0 && (
          <div className="flex flex-col gap-10">
            {activeBoards.map((board) => (
              <div key={board.id} id={`board-${board.id}`}>
                <BoardPreview
                  key={board.id}
                  board={board}
                  sortBy={sortBy}
                  onArchiveChange={(status) => handleArchiveChange(board, status)}
                />
              </div>
            ))}
          </div>
        )}

        {archivedBoards.length > 0 && (
          <div ref={archivedRef}>
            <CollapsePane
              open={isArchiveOpen}
              onOpenChange={setIsArchiveOpen}
              title={
                <div className="flex items-center ml-7 text-xl">
                  Archived
                  <span className="ml-3 mt-[2px] text-sm text-secondary-fg">
                    {archivedBoards.length}
                  </span>
                </div>
              }
              classNames={{
                content: 'pt-4 pb-5',
              }}
            >
              <div className="flex flex-col gap-10">
                {archivedBoards.map((board) => (
                  <div key={board.id} id={`board-${board.id}`}>
                    <BoardPreview
                      board={board}
                      sortBy={sortBy}
                      onArchiveChange={(status) => handleArchiveChange(board, status)}
                    />
                  </div>
                ))}
              </div>
            </CollapsePane>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="flex flex-col pb-5 w-full h-full">
      <div className="flex-shrink-0 w-full bg-card">
        <div className="mx-auto">
          <BoardListHeader sortBy={sortBy} onSortChange={setSortBy} />
        </div>
      </div>

      <div
        className={cn('h-0 flex-grow overflow-auto', {
          'pb-5': archivedBoards.length === 0,
        })}
      >
        <div className="mx-auto">{renderContent()}</div>
      </div>
    </div>
  );
}
