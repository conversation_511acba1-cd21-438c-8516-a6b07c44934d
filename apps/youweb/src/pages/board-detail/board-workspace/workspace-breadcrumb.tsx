import { BoardStatusEnum } from '@repo/common/types/board/types';
import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import { FavoriteEntityTypeEnum } from '@repo/common/types/favorite/types';
import { toast } from '@repo/ui/components/ui/sonner';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { Loader2 } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { deleteBoard as deleteBoardAPI } from '@/apis/board';
import { MoreMenu } from '@/components/board/board-item-draggable-tree/components/BoardItem/MoreMenu';
import { FavoriteStar } from '@/components/favorite/FavoriteStar';
import { Boards } from '@/components/icon/sidebar/boards';
import { useBoardItemTree } from '@/hooks/useBoardItemTree';
import { getActiveBoardTreeItemAtom } from '@/hooks/useBoardMenuTree';
import { changePanelData<PERSON>tom } from '@/hooks/useBoardState';
import {
  boardDetailAtom,
  deleteBoardAtom,
  getActiveBoardItemAtom,
  getBoardItemByIdAtom,
  renamingBreadcrumbItemIdAtom,
} from '@/hooks/useBoards';
import { useTranslation } from '@/hooks/useTranslation';
import { Board as BoardType } from '@/typings/board';
import type { BoardItem, BoardTreeItem } from '@/typings/board-item';
import {
  getImageAndTitleFromBoardItem,
  IMAGE_ICON,
} from '@/utils/board/getImageAndTitleFromBoardItem';
import { cn } from '@/utils/utils';
import BoardModifyButton from '../board-modify-button';

// 计算文本宽度的函数
function calculateTextWidth(text: string): number {
  let width = 0;
  for (const char of text) {
    // 判断是否为中文字符（包括中文标点符号）
    if (/[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff\u3000-\u303f\uff00-\uffef]/.test(char)) {
      width += 14; // 中文字符 14px
    } else {
      width += 8; // 英文字符和空格 8px
    }
  }
  return width;
}

export interface BreadcrumbItem {
  id: string;
  name: string;
  icon?: React.ReactNode;
  type: 'material' | 'board';
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

function Breadcrumb({ items, className }: BreadcrumbProps) {
  const [board, setBoardDetail] = useAtom(boardDetailAtom);
  const [, deleteBoard] = useAtom(deleteBoardAtom);
  const changePanelData = useSetAtom(changePanelDataAtom);
  const getBoardItemById = useAtomValue(getBoardItemByIdAtom);

  const [renamingBreadcrumbItemId, setRenamingBreadcrumbItemId] = useAtom(
    renamingBreadcrumbItemIdAtom,
  );

  const activeBoardTreeItem = useAtomValue(getActiveBoardTreeItemAtom);
  const { t } = useTranslation('Library.Board');

  const navigate = useNavigate();

  const [inputTitle, setInputTitle] = useState('');
  const [loading, setLoading] = useState(false);
  const [isComposing, setIsComposing] = useState(false);
  const { saveRenamedBoardItem } = useBoardItemTree();
  const titleInputRef = useRef<HTMLInputElement>(null);
  const selectionStyleRef = useRef<HTMLStyleElement | null>(null);
  const mountedTimeRef = useRef<number>(0);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    mountedTimeRef.current = Date.now();
  }, []);

  useEffect(() => {
    if (renamingBreadcrumbItemId) {
      const item = getBoardItemById(renamingBreadcrumbItemId);
      if (item) {
        const { title } = getImageAndTitle(item);
        setInputTitle(title);

        setTimeout(() => {
          titleInputRef.current?.focus();
          titleInputRef.current?.select();
          const selection = window.getSelection();
          if (selection && !selectionStyleRef.current) {
            const style = document.createElement('style');
            style.textContent = `
            ::selection {
              background: #02041A0F;
            }
            `;
            document.head.appendChild(style);
            selectionStyleRef.current = style;
          }
        }, 100);
      }
    }
  }, [renamingBreadcrumbItemId, getBoardItemById]);

  const handleSaveTitle = async (itemName: string) => {
    if (!renamingBreadcrumbItemId) return;

    selectionStyleRef.current?.remove();
    selectionStyleRef.current = null;

    if (isSaving) return;
    setIsSaving(true);

    const newTitle = inputTitle.trim();
    const item = getBoardItemById(renamingBreadcrumbItemId) as BoardTreeItem;

    if (!item || !newTitle || newTitle === itemName) {
      setRenamingBreadcrumbItemId(null);
      setIsSaving(false);
      return;
    }

    setLoading(true);
    await saveRenamedBoardItem(item, newTitle);
    setLoading(false);
    setIsSaving(false);
    setRenamingBreadcrumbItemId(null);
  };

  const onBoardDelete = async () => {
    if (!board?.id) {
      return;
    }
    try {
      const { error } = await deleteBoardAPI(board.id);
      if (error) {
        return;
      }
      deleteBoard(board?.id);
      navigate('/boards', { replace: true });
    } catch (e) {
      toast((e as Error).message);
    }
  };

  const onBoardEditFinish = (data: Partial<BoardType>) => {
    if (!data.name || !data.icon) {
      return;
    }
    setBoardDetail({
      ...(board as BoardType),
      name: data.name,
      icon: data.icon,
    });
  };

  const onBoardPinChange = (pin: Date | undefined) => {
    setBoardDetail({
      ...(board as BoardType),
      pinned_at: pin,
    });
  };
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <>
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        const isFirst = index === 0;
        const type = item.type;
        const isRenaming = renamingBreadcrumbItemId === item.id;

        let lastElement: React.ReactNode = <span className="mx-2 text-caption-fg">/</span>;
        if (isLast) {
          if (type === 'material' && activeBoardTreeItem) {
            lastElement = (
              <div className="ml-2 flex items-center gap-[2px]">
                <FavoriteStar
                  entityId={activeBoardTreeItem.entity.id}
                  entityType={activeBoardTreeItem.entity_type}
                  entity={activeBoardTreeItem.entity}
                  className="w-6 h-6 p-1 rounded-full cursor-pointer hover:bg-muted"
                />
                <MoreMenu
                  showWordCount
                  item={activeBoardTreeItem}
                  openWithAtom={false}
                  showOnHover={false}
                  className="text-sm rounded-full"
                  showCopyLink
                  showCopyAndExport
                  variant="board-breadcrumb"
                />
              </div>
            );
          } else if (type === 'board') {
            if (!board?.id) {
              return null;
            }
            lastElement = (
              <div className="ml-2 flex items-center gap-[2px]">
                <FavoriteStar
                  entityId={board?.id}
                  entityType={FavoriteEntityTypeEnum.BOARD}
                  entity={board}
                  className="w-6 h-6 p-1 rounded-full cursor-pointer hover:bg-muted"
                />
                <BoardModifyButton
                  board={board}
                  itemsCount={board?.items.length}
                  onBoardEditFinish={onBoardEditFinish}
                  onBoardDelete={onBoardDelete}
                  onBoardPinChange={onBoardPinChange}
                  onArchiveChange={(status) => {
                    toast(
                      status === BoardStatusEnum.OTHER
                        ? t('archivedLongDesc', { boardName: board.name })
                        : t('unarchivedLongDesc', { boardName: board.name }),
                      {
                        position: 'bottom-center',
                        duration: 5000,
                      },
                    );
                  }}
                />
              </div>
            );
          }
        }

        return (
          <React.Fragment key={item.id}>
            <button
              type="button"
              className={cn(
                'flex items-center border-none bg-transparent p-0',
                'group cursor-pointer overflow-hidden text-caption-fg hover:text-foreground',
                isRenaming && 'box-border rounded-[8px] border !bg-card px-[7px]',
                isFirst ? 'shrink-0' : isLast ? 'flex-shrink-1 flex-2' : 'flex-shrink-2 flex-1',
                // 根据文本宽度决定是否设置最小宽度, 如果文本宽度不超过80px，设置flex-shrink-0防止被压缩
                calculateTextWidth(item.name) + 16 > 80 ? 'min-w-[80px]' : 'flex-shrink-0',
              )}
              style={{
                minWidth:
                  calculateTextWidth(item.name) + 16 <= 80
                    ? calculateTextWidth(item.name) + 16
                    : undefined,
              }}
              onClick={() => {
                if (isRenaming) return;
                if (type === 'board') {
                  changePanelData();
                } else {
                  changePanelData(getBoardItemById(item.id)!);
                }
              }}
            >
              {item.icon && (
                <div
                  className={cn(
                    'mr-1 flex-shrink-0',
                    type !== 'board' && 'opacity-50 group-hover:opacity-100',
                  )}
                >
                  {item.icon}
                </div>
              )}

              {isRenaming ? (
                <div className="relative flex-1 min-w-0">
                  <input
                    ref={titleInputRef}
                    type="text"
                    value={inputTitle}
                    className="relative bottom-[0.5px] w-full rounded-[8px] border-foreground bg-transparent py-0.5 text-sm text-foreground"
                    disabled={loading}
                    onChange={(e) => {
                      setInputTitle(e.target.value);
                    }}
                    onBlur={() => {
                      if (Date.now() - mountedTimeRef.current < 500) {
                        return;
                      }
                      handleSaveTitle(item.name);
                    }}
                    onCompositionStart={() => {
                      setIsComposing(true);
                    }}
                    onCompositionEnd={() => {
                      setIsComposing(false);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !isComposing) {
                        handleSaveTitle(item.name);
                      }
                    }}
                  />

                  {loading && (
                    <Loader2
                      size={16}
                      className="absolute right-1 top-[4px] animate-spin text-secondary-fg"
                    />
                  )}
                </div>
              ) : (
                <div
                  className={cn(
                    'flex-1 truncate flex-shrink-1',
                    // isLast ? "flex-shrink-0 max-w-[600px]" : "flex-shrink-1",
                  )}
                >
                  {item.name}
                </div>
              )}
            </button>

            {lastElement}
          </React.Fragment>
        );
      })}
    </>
  );
}

function getImageAndTitle(item: BoardItem) {
  const defaultTitleForThought = 'New thought';
  const defaultTitleForGroup = 'New group';
  const defaultTitleForSnip = 'New snip';

  const getDefaultTitle = () => {
    if (item.entity_type === BoardItemTypeEnum.BOARD_GROUP) {
      return defaultTitleForGroup;
    }
    if (item.entity_type === BoardItemTypeEnum.THOUGHT) {
      return defaultTitleForThought;
    }
    return defaultTitleForSnip;
  };
  return getImageAndTitleFromBoardItem(item, {
    defaultTitle: getDefaultTitle(),
  });
}

export function WorkspaceBreadcrumb() {
  const [board] = useAtom(boardDetailAtom);

  const getBoardItemById = useAtomValue(getBoardItemByIdAtom);
  const activeItem = useAtomValue(getActiveBoardItemAtom) as BoardTreeItem;

  const items: BreadcrumbItem[] = [];
  let currentItem = activeItem;
  while (currentItem) {
    const { title, imageUrl } = getImageAndTitle(currentItem);
    // 向上递归，往前添加
    items.unshift({
      id: currentItem?.id,
      name: title,
      icon: currentItem.entity_type === BoardItemTypeEnum.BOARD_GROUP && (
        <img
          src={imageUrl}
          alt="icon"
          className="h-4 w-4 flex-shrink-0 rounded-[4px] object-cover"
          onError={(e) => {
            e.currentTarget.src = IMAGE_ICON;
          }}
        />
      ),
      type: 'material',
    });
    if (currentItem.parentId) {
      currentItem = getBoardItemById(currentItem?.parentId) as BoardTreeItem;
    } else {
      break;
    }
  }
  if (!board?.id) {
    return null;
  }
  items.unshift({
    id: board?.id,
    name: board?.name,
    icon: <Boards className="leading-[20px]" size={16} />,
    type: 'board',
  });
  return (
    <div className="pointer-events-none fixed left-0 right-0 top-0 z-[10] flex w-full justify-center">
      <div className="pointer-events-auto flex h-[52px] min-w-0 max-w-[70%] flex-row items-center overflow-hidden pl-[100px] pr-[160px] text-sm">
        <Breadcrumb items={items} />
      </div>
    </div>
  );
}
