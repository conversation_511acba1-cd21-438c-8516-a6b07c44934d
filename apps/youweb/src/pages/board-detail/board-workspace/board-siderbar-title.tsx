import { BoardStatusEnum } from '@repo/common/types/board/types';
import { toast } from '@repo/ui/components/ui/sonner';
import * as boardIcons from '@youmindinc/youicon';
import { useAtom, useSetAtom } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { deleteBoard as deleteBoardAPI } from '@/apis/board';
import { PALETTE } from '@/components/board/board-creator';
import { changePanelDataAtom } from '@/hooks/useBoardState';
import { boardDetailAtom, deleteBoardAtom } from '@/hooks/useBoards';
import { useTranslation } from '@/hooks/useTranslation';
import type { Board } from '@/typings/board';
import BoardModifyButton from '../board-modify-button';

interface BoardSidebarTitleProps {
  hideModifyButton?: boolean;
}

export default function BoardSidebarTitle({ hideModifyButton = false }: BoardSidebarTitleProps) {
  const [board, setBoardDetail] = useAtom(boardDetailAtom);
  const Icon = boardIcons[board?.icon?.name as keyof typeof boardIcons] || boardIcons.Planet;

  // 添加state来跟踪文本是否溢出
  const [isTextOverflowing, setIsTextOverflowing] = useState(false);
  // 创建ref来引用文本元素和父元素
  const textRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const iconColor = `var(${board?.icon?.color || PALETTE[0]})`;
  const [, deleteBoard] = useAtom(deleteBoardAtom);
  const changePanelData = useSetAtom(changePanelDataAtom);

  const navigate = useNavigate();
  const { t } = useTranslation('Library.Board');

  useEffect(() => {
    if (!board?.name) return;

    // 使用setTimeout确保DOM完全渲染
    const timer = setTimeout(() => {
      if (textRef.current && containerRef.current) {
        // 获取父容器宽度
        const containerWidth = containerRef.current.clientWidth;
        // 计算文本可用宽度（减去按钮占用空间，24px）
        const availableWidth = containerWidth - 24;
        // 获取文本实际宽度
        const textWidth = textRef.current.scrollWidth;

        // 判断是否溢出
        const isOverflowing = textWidth >= availableWidth;
        setIsTextOverflowing(isOverflowing);
      }
    }, 0);

    return () => clearTimeout(timer);
  }, [board?.name]); // 仅在board.name变化时重新检测

  const onBoardDelete = async () => {
    if (!board?.id) {
      return;
    }
    try {
      const { error } = await deleteBoardAPI(board.id);
      if (error) {
        return;
      }
      deleteBoard(board.id);
      navigate('/boards', { replace: true });
    } catch (e) {
      toast((e as Error).message);
    }
  };

  const onBoardEditFinish = (data: Partial<Board>) => {
    setBoardDetail({
      ...(board as Board),
      name: data.name!,
      icon: data.icon!,
    });
  };

  const onBoardPinChange = (pin: Date | undefined) => {
    setBoardDetail({
      ...(board as Board),
      pinned_at: pin,
    });
  };

  if (!board) {
    return null;
  }

  // 根据文本是否溢出动态设置样式
  const gradientStyle = isTextOverflowing
    ? {
        maskImage: 'linear-gradient(to right, var(--primary) calc(100% - 40px), transparent 100%)',
        WebkitMaskImage:
          'linear-gradient(to right, var(--primary) calc(100% - 40px), transparent 100%)',
      }
    : {};

  return (
    <div className="flex flex-row items-center px-4 py-3 pt-5">
      <Icon
        size={28}
        style={{
          color: iconColor,
        }}
        className="mr-1 cursor-pointer shrink-0"
        onClick={() => {
          changePanelData();
        }}
      />
      <div ref={containerRef} className="flex flex-row items-center flex-grow min-w-0">
        <div
          ref={textRef}
          className="relative min-w-0 overflow-hidden text-base font-medium cursor-pointer whitespace-nowrap"
          onClick={() => {
            changePanelData();
          }}
          style={gradientStyle}
        >
          {board.name}
        </div>
        {hideModifyButton ? null : (
          <BoardModifyButton
            dialogAlign="start"
            className="ml-2"
            board={board}
            itemsCount={board.items.length}
            onBoardEditFinish={onBoardEditFinish}
            onBoardDelete={onBoardDelete}
            onBoardPinChange={onBoardPinChange}
            onArchiveChange={(status) => {
              toast(
                status === BoardStatusEnum.OTHER
                  ? t('archivedLongDesc', { boardName: board.name })
                  : t('unarchivedLongDesc', { boardName: board.name }),
                {
                  position: 'bottom-center',
                  duration: 5000,
                },
              );
            }}
          />
        )}
      </div>
    </div>
  );
}
