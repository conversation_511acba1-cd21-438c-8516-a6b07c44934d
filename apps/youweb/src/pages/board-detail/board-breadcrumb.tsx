import { BoardStatusEnum } from '@repo/common/types/board';
import { toast } from '@repo/ui/components/ui/sonner';
import * as boardIcons from '@youmindinc/youicon';
import { useAtom } from 'jotai';
import { Pin } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { deleteBoard as deleteBoardAPI } from '@/apis/board';
import { PALETTE } from '@/components/board/board-creator';
import { boardDetailAtom, deleteBoardAtom } from '@/hooks/useBoards';
import { useTranslation } from '@/hooks/useTranslation';
import type { Board } from '@/typings/board';
import BoardModifyButton from './board-modify-button';

export default function BoardBreadcrumb() {
  const [board, setBoardDetail] = useAtom(boardDetailAtom);
  const Icon = boardIcons[board?.icon?.name as keyof typeof boardIcons] || boardIcons.Planet;
  const { t } = useTranslation('Library.Board');

  const iconColor = `var(${board?.icon?.color || PALETTE[0]})`;
  const [, deleteBoard] = useAtom(deleteBoardAtom);

  const navigate = useNavigate();

  const onBoardDelete = async () => {
    if (!board) {
      return;
    }
    try {
      const { error } = await deleteBoardAPI(board.id);
      if (error) {
        return;
      }
      deleteBoard(board.id);
      navigate('/boards', { replace: true });
    } catch (e) {
      toast((e as Error).message);
    }
  };

  const onBoardEditFinish = (data: Partial<Board>) => {
    setBoardDetail({
      ...(board as Board),
      name: data.name!,
      icon: data.icon!,
    });
  };

  const onBoardPinChange = (pin: Date | undefined) => {
    setBoardDetail({
      ...(board as Board),
      pinned_at: pin,
    });
  };

  return (
    <div className="relative flex items-center text-sm">
      <div className="relative mr-2">
        <Icon
          size={16}
          style={{
            color: iconColor,
          }}
        />
        {board?.pinned_at && (
          <div className="absolute bottom-0 left-[13px]">
            <Pin size={7} fill={iconColor} style={{ color: iconColor }} />
          </div>
        )}
      </div>
      <span className="max-w-[300px] overflow-hidden text-ellipsis whitespace-nowrap">
        {board?.name}
      </span>
      {board && (
        <BoardModifyButton
          board={board}
          dialogAlign="start"
          buttonWidth={20}
          itemsCount={board.items.length}
          onBoardEditFinish={onBoardEditFinish}
          onBoardDelete={onBoardDelete}
          onBoardPinChange={onBoardPinChange}
          onArchiveChange={(status) => {
            toast(
              status === BoardStatusEnum.OTHER
                ? t('archivedLongDesc', { boardName: board.name })
                : t('unarchivedLongDesc', { boardName: board.name }),
              {
                position: 'bottom-center',
                duration: 5000,
              },
            );
          }}
          className="ml-2 text-secondary-fg"
        />
      )}
    </div>
  );
}
