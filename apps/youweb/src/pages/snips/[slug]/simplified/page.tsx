import { SimpleLoading } from '@repo/ui/components/custom/simple-loading';
import { Snip } from '@repo/ui-business-snip/typings/snip';
import { methodRegistry } from '@youmindinc/jsbridge';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { ExportImagePreviewClean, ExportImagePreviewRef } from '@/components/export-image-preview';
import { SnipRegisterToNativeMethodName } from '@/components/snip/const';
import { callHTTP } from '@/utils/callHTTP';
import SimpleView from './simple-view';

export default function SnipSimplifiedPage() {
  const { slug } = useParams<{ slug: string }>();
  const [snip, setSnip] = useState<Snip | null>(null);
  const [loading, setLoading] = useState(true);
  const exportRef = useRef<ExportImagePreviewRef>(null);
  // const loading = false;
  const exportImageSignal = useMemo(() => new AbortController(), []);

  useEffect(() => {
    const fetchSnip = async () => {
      if (!slug) return;

      try {
        const response = await callHTTP('/api/v1/snip/getSnip', {
          method: 'POST',
          body: {
            id: slug,
          },
        });
        setSnip(response.data);
      } catch (error) {
        console.error('Error fetching snip:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSnip();
  }, [slug]);

  // 当 material.id 变化时，清理上一个 AbortController
  useEffect(() => {
    return () => {
      exportImageSignal.abort();
    };
  }, [exportImageSignal]);

  useEffect(() => {
    methodRegistry.reexportMethod(SnipRegisterToNativeMethodName.EXPORT_AS_IMAGE, async () => {
      return exportRef?.current?.exportImage({
        needDownload: false,
      });
    });
  }, []);

  if (loading) {
    return <SimpleLoading className="h-[100vh]" />;
  }

  if (!snip) {
    return <div>Snip not found</div>;
  }

  return (
    <>
      {/* <Button
        onClick={() => {
          exportRef?.current?.exportImage({
            needDownload: true,
            filename: 'test.png',
          });
        }}
      >
        test
      </Button> */}
      <SimpleView snip={snip} />
      <ExportImagePreviewClean
        rerenderContentBeforeExport={{
          enable: true,
          wait: 300,
        }}
        offscreen
        ref={exportRef}
        selector={() => {
          return document.querySelector('.ym-reader-container') as HTMLDivElement;
        }}
        title={snip?.title}
        header={<div>&nbsp;</div>}
      />
    </>
  );
}
