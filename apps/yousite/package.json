{"name": "yousite", "version": "0.1.0", "private": true, "scripts": {"dev": "YOUMIND_ENV=preview ../../devops/scripts/with-doppler next dev -p 3003", "dev:prod": "YOUMIND_ENV=production ../../devops/scripts/with-doppler next dev -p 3003", "build": "../../devops/scripts/with-doppler next build", "build:cf": "../../devops/scripts/with-doppler npx @opennextjs/cloudflare build", "test-echo": "echo 'Test echo works!'", "start": "YOUMIND_ENV=production ../../devops/scripts/with-doppler next start", "build:worker": "pnpm opennextjs-cloudflare build --env preview", "preview:worker": "pnpm opennextjs-cloudflare preview", "preview": "pnpm build:worker && pnpm preview:worker", "deploy": "wrangler deploy --env production", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format .", "typecheck": "tsc --noEmit", "env": "node ../../devops/scripts/doppler-pull.js yousite", "env:prod": "node ../../devops/scripts/doppler-pull.js yousite --env=production", "config": "node ../../devops/scripts/doppler-pull-config.js", "config:prod": "node ../../devops/scripts/doppler-pull-config.js --env=production", "analyze": "ANALYZE=true ../../devops/scripts/with-doppler next build"}, "dependencies": {"@repo/api": "workspace:*", "@repo/common": "workspace:*", "@repo/server-common": "workspace:*", "@repo/ui": "workspace:*", "@repo/ui-business-editor": "workspace:*", "@repo/ui-business-snip": "workspace:*", "@sentry/nextjs": "^9.37.0", "@youmindinc/youcommon": "^0.1.22", "@youmindinc/youicon": "catalog:", "ahooks": "catalog:", "clsx": "^2.1.1", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "drizzle-orm": "^0.44.4", "franc-min": "^6.2.0", "iso-639-3": "^3.0.1", "jotai": "catalog:", "lodash-es": "catalog:", "lucide-react": "catalog:", "next": "catalog:", "next-intl": "^4.3.4", "next-themes": "^0.4.6", "postgres": "^3.4.7", "qs-esm": "^7.0.2", "react": "catalog:", "react-dom": "catalog:", "react-headroom": "^3.2.1", "react-hook-form": "catalog:", "server-only": "^0.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuidv7": "catalog:", "zod": "^3.24.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.6", "@opennextjs/cloudflare": "^1.3.0", "@types/lodash-es": "catalog:", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@types/react-headroom": "^3.2.3", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "15.1.3", "postcss": "^8.5.1", "tailwindcss": "catalog:", "typescript": "catalog:", "wrangler": "catalog:"}}