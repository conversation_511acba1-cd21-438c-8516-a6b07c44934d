'use client';

import { ImagePreview, type ImagePreviewProps } from '@repo/ui/components/custom/image-preview';
import { useIsMobile } from '@repo/ui/hooks/useIsMobile';
import { createConfig, SnipContainer, SnipProvider, snipDetail<PERSON>tom } from '@repo/ui-business-snip';
import { Languages } from '@repo/ui-business-snip/i18n';
import { SnipArticle, Snip as SnipVO } from '@repo/ui-business-snip/typings/snip';
import { useSetAtom } from 'jotai';
// import { useHydrateAtoms } from 'jotai/utils';
import { ArrowUpRightFromSquare } from 'lucide-react';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { useLocale } from 'next-intl';
import { useEffect, useMemo } from 'react';
import { Link } from '@/i18n/navigation';
import { apiClientOld, callHTTPStream } from '@/lib/request/client';
import SharedHeader from './header';

const domainWhitelist = ['gooo.ai', 'youmind.com', 'youmind.ai', 'youmind.site'];

function ImagePreviewForNextjs(props: ImagePreviewProps) {
  const isWhitelisted = (input?: string) => {
    if (!input) return false;
    // Allow relative paths by default
    try {
      const { hostname } = new URL(input);
      return domainWhitelist.some(
        (domain) => hostname === domain || hostname.endsWith(`.${domain}`),
      );
    } catch {
      return false;
    }
  };

  return (
    <ImagePreview
      {...props}
      imgRender={({ src, alt, width, height, className, ...rest }) => {
        // Not in whitelist: use plain img
        if (!isWhitelisted(src)) {
          return <img src={src} alt={alt} className={className} {...rest} />;
        }

        return (
          <Image
            src={src!}
            alt={alt!}
            fill
            width={width as number}
            height={height as number}
            className={className}
            {...rest}
          />
        );
      }}
    />
  );
}

export function Snip({ snip }: { snip: SnipVO }) {
  // useHydrateAtoms([[snipDetailAtom, snip]]);
  const setSnip = useSetAtom(snipDetailAtom);

  const { webpage } = snip as SnipArticle;
  const url = webpage?.url;
  const searchParams = useSearchParams();
  const { isMobile } = useIsMobile();

  useEffect(() => {
    if (snip) {
      setSnip(snip);
    }
  }, [snip, setSnip]);

  const linkNode = url ? (
    <Link
      href={url}
      target="_blank"
      className="footnote flex flex-nowrap items-center text-caption-fg"
    >
      <ArrowUpRightFromSquare size={14} className="mr-[2px]" />
      <span className="max-w-[320px] overflow-hidden text-ellipsis whitespace-nowrap">
        {webpage?.site?.host || webpage?.site?.name || url}
      </span>
    </Link>
  ) : null;

  const operationArea = (
    <div>
      {linkNode}
      {/* <SaveMaterial short_id={short_id} hasLoggedIn={!!user} /> */}
    </div>
  );

  const config = useMemo(() => {
    return createConfig({
      apiClient: apiClientOld,
      searchParams,
      readonly: true,
      events: {},
      callHTTPStream: callHTTPStream,
      onlineVideo: {
        options: {
          showViewButton: !isMobile,
        },
      },
      components: {
        ImagePreview: ImagePreviewForNextjs,
      },
    });
  }, [isMobile, searchParams]);

  const locale = useLocale() as Languages;

  return (
    <>
      <SharedHeader extra={operationArea} />
      <main className="mx-auto max-w-[800px] px-6 py-5">
        <div className="snip-content">
          <SnipProvider config={config} locale={locale}>
            <SnipContainer />
          </SnipProvider>
        </div>
      </main>
    </>
  );
}
