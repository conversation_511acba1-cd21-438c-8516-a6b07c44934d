import withBundleAnalyzer from '@next/bundle-analyzer';
import type { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const isLocal = process.env.NODE_ENV === 'development';
const isCloudflare = process.env.DEPLOYMENT_TARGET === 'cloudflare';
const isVercel = process.env.DEPLOYMENT_TARGET === 'vercel' || process.env.VERCEL === '1';

const withNextIntl = createNextIntlPlugin();
const bundleAnalyzer = withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

const nextConfig: NextConfig = {
  eslint: {
    dirs: ['src'],
    // 在 CI 环境中，如果设置了 NEXT_ESLINT_IGNORE，则忽略 ESLint 错误
    ignoreDuringBuilds: process.env.NEXT_ESLINT_IGNORE === 'true' || process.env.CI === 'true',
  },
  // 在 CI 环境中跳过 TypeScript 类型检查
  typescript: {
    ignoreBuildErrors: process.env.NEXT_TYPE_CHECK === 'false' || process.env.CI === 'true',
  },
  transpilePackages: ['@repo/ui', '@repo/ui-business-editor', '@repo/ui-business-snip'],
  poweredByHeader: false,
  images: {
    // Cloudflare 使用自定义 loader，Vercel 使用默认
    ...(isCloudflare && {
      loader: 'custom',
      loaderFile: './image-loader.ts',
    }),
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.gooo.ai',
        port: '',
        pathname: '/**',
      },
    ],
  },
  webpack: (config) => {
    config.resolve.fallback = {
      fs: false,
      net: false,
      dns: false,
      child_process: false,
      tls: false,
      canvas: false,
    };

    // 使用 IgnorePlugin 完全忽略这些模块
    const webpack = require('webpack');
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^(canvas|jsdom)$/,
        contextRegExp: /node_modules/,
      }),
    );

    // 优化代码分割，将大型依赖单独打包
    // config.optimization = config.optimization || {};
    // config.optimization.splitChunks = {
    //   ...config.optimization.splitChunks,
    //   chunks: 'all',
    //   cacheGroups: {
    //     ...config.optimization.splitChunks?.cacheGroups,
    //     // 将 highlight.js 相关包单独分割
    //     highlight: {
    //       test: /[\\/]node_modules[\\/](highlight\.js|lowlight)[\\/]/,
    //       name: 'highlight',
    //       priority: 30,
    //       chunks: 'all',
    //     },
    //     // 将 UI 业务包单独分割
    //     uiBusiness: {
    //       test: /[\\/]packages[\\/]ui-business-/,
    //       name: 'ui-business',
    //       priority: 20,
    //       chunks: 'all',
    //     },
    //   },
    // };

    return config;
  },
  // 开发环境代理配置
  ...(isLocal && {
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: 'http://localhost:4000/api/:path*',
        },
        {
          source: '/auth/:path*',
          destination: 'http://localhost:4000/auth/:path*',
        },
        {
          source: '/innerapi/:path*',
          destination: 'http://localhost:4000/innerapi/:path*',
        },
      ];
    },
    async redirects() {
      return [
        {
          source: '/boards/:path*',
          destination: 'http://localhost:2000/boards/:path*',
          permanent: true,
        },
      ];
    },
  }),
};

export default bundleAnalyzer(withNextIntl(nextConfig));

// Initialize OpenNext for local development
if (process.env.NODE_ENV === 'development') {
  try {
    const { initOpenNextCloudflareForDev } = require('@opennextjs/cloudflare');
    initOpenNextCloudflareForDev();
  } catch (error: any) {
    console.warn('OpenNext Cloudflare dev initialization failed:', error.message);
  }
}
