'use client';

import sparkSmall from '@public/login/spark-small.svg';
import youmindLogo from '@public/logo/youmind.svg';
import youmindSmallLogo from '@public/logo/youmind-small.svg';
import { useHydrateAtoms } from 'jotai/utils';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { userAtom } from '@/hooks/useUser';
import type { UserWithPreferenceVO } from '@/schema/userSchema';

export const ElementClassMap = {
  button:
    'w-full rounded-3xl disabled:text focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 login-button',
  input:
    'w-full rounded-xl border border-input px-3 py-2 font-normal placeholder:text-disabled-fg invalid:border-error hover:border-input focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 login-input',
  textarea:
    'w-full rounded-md border border-input px-3 py-1 font-normal placeholder:text-disabled-fg invalid:border-error hover:border-input focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
  withBrand:
    'text-primary-fg hover:text-primary-fg bg-primary hover:bg-primary-hover disabled:bg-primary-disabled',
};

export function Footnote() {
  const t = useTranslations('Auth.Footnote');

  return (
    <div className="footnote mt-6">
      {t('desc')}
      <a href="/terms" className="underline" target="_blank" rel="noopener">
        {t('terms')}
      </a>
      ,{' '}
      <a href="/privacy" className="underline" target="_blank" rel="noopener">
        {t('privacy')}
      </a>
    </div>
  );
}

export function Sparks() {
  return (
    <>
      {/* BEGIN SPARKLING STARS */}
      <div className="spark-small">
        <Image src={sparkSmall} width={20} height={23} alt={'Sparkling Thought'} />
      </div>
    </>
  );
}

export function Logo() {
  return (
    <Link href="/overview" className="logo-container">
      <Image
        src={youmindLogo}
        width={118}
        height={20}
        alt={'YouMind logo'}
        className="full-size inverted-logo"
      />
      <Image src={youmindSmallLogo} width={33} height={20} alt={'YouMind logo'} className="mini" />
    </Link>
  );
}

export function IllustrationLeft() {
  return (
    <>
      {/* BEGIN LEFT ILLUSTRATION */}
      <div className="illustration-left">
        <Image
          src={'https://cdn.gooo.ai/assets/illustration-left.png'}
          width={191}
          height={709}
          alt={'Bush'}
        />
      </div>
    </>
  );
}

export function IllustrationRight() {
  return (
    <>
      {/* BEGIN RIGHT ILLUSTRATION */}
      <div className="illustration-right">
        <Image
          src={'https://cdn.gooo.ai/assets/illustration-right-v3-compressed.png'}
          width={635}
          height={709}
          alt={'Thinker under moonlight'}
          className="thinker"
        />
      </div>
    </>
  );
}

export function SetupUser({ user }: { user: UserWithPreferenceVO | null }) {
  useHydrateAtoms([[userAtom, user]]);
  return null;
}
