import Image from 'next/image';
import { GameStartButton } from '../GameStartButton';
// import heroImage from "../../assets/images/hero-image.png";
import heroIllustration from './images/hero-illustration.png';
import HeroVideo from './Video';

export default function Hero() {
  return (
    <section className="relative mx-auto w-full pb-10 pt-20 md:pb-0 md:pt-24 lg:pt-32">
      <div className="mx-auto flex max-w-7xl flex-col items-center px-6 text-center lg:px-8">
        <h1 className="mb-4 font-sans-title text-4xl font-bold md:mb-6 md:text-5xl lg:text-[56px]">
          Write something good.
        </h1>
        <p className="mx-auto mb-8 max-w-3xl font-sans-title text-base text-secondary-fg md:mb-10 md:max-w-4xl md:text-lg lg:text-xl">
          YouMind is the reimagined AI writing tool that can help anyone start creating. Capture
          ideas, gather materials, write drafts, and turn them into polished articles, podcasts,
          videos, and more.
        </p>
        <GameStartButton />
      </div>

      {/* 图片部分 */}
      <div className="relative -top-11 mx-auto mt-10 w-full max-w-[1600px] origin-top scale-125 md:mt-0 md:scale-100 md:px-8 lg:-top-14">
        <div className="relative aspect-[1204/796] w-full overflow-hidden">
          <div
            className="absolute inset-0 left-[calc(220/1204*100%)] top-[calc(148/796*100%)] aspect-[760/480] w-[calc(760/1204*100%)]"
            // placeholder="blur"
            // blurDataURL={mainImageBlurDataURL}
          >
            <HeroVideo
              className="h-full w-full object-cover"
              style={{
                borderRadius: '9px',
                boxShadow: '0px 4.606px 23.03px 0px rgba(2, 4, 26, 0.16)',
              }}
            />
          </div>
          <Image
            priority
            quality={80}
            src={heroIllustration}
            alt="Hero Illustration Image"
            className="pointer-events-none absolute inset-0 h-full w-full object-contain"
          />
        </div>
      </div>

      {/* <div className="relative -top-5 flex justify-center">
        <Image src={heroImage} alt="Hero Image" width={1000} height={1000} />
      </div> */}
    </section>
  );
}
