'use client';

import Image from 'next/image';
import bgGradientBlue from './images/bg-gradient-blue.png';
import bgGradientYellow from './images/bg-gradient-yellow.png';
import FeatureChatImage from './images/feature-chat.png';
import FeatureOrganizeImage from './images/feature-organize.png';
import FeatureResearchNewImage from './images/feature-research-new.png';
import FeatureSaveImage from './images/feature-save.png';
import FeatureShareImage from './images/feature-share.png';
import FeatureWriteImage from './images/feature-write.png';

const Features = () => {
  return (
    <section className="relative mx-auto mt-10 w-full max-w-7xl md:mt-0 lg:px-8">
      <div className="mx-auto px-4">
        <h2 className="mb-12 text-center font-sans-title text-3xl font-bold md:mb-14 md:text-[42px]">
          One place for you
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-12">
          {/* Save Card */}
          <div className="h-[420px] rounded-3xl border border-snip-card bg-[#F7F7F8] px-9 py-6 transition-all duration-300 hover:-translate-y-1 md:h-[460px] lg:col-span-6">
            <h3 className="mb-2 text-2xl font-medium">Save</h3>
            <p className="mb-4 text-secondary-fg md:mb-6">
              Save youtube, podcasts, articles and webpages to YouMind with our browser extension.
              Upload PDFs, m4a files and other documents all in one board.
            </p>
            <div className="relative flex h-[240px] items-center justify-center overflow-hidden rounded-xl md:h-[260px]">
              <div className="relative flex h-full w-full justify-center">
                <Image
                  src={FeatureSaveImage}
                  className="h-full w-full object-contain"
                  alt="Save Feature Image"
                />
              </div>
            </div>
          </div>

          {/* Research Card */}
          <div className="relative flex h-[420px] flex-col rounded-3xl border border-snip-card bg-[#F7F7F8] py-6 !pb-0 transition-all duration-300 hover:-translate-y-1 md:h-[460px] lg:col-span-6">
            <h3 className="mb-2 px-9 text-2xl font-medium">Research</h3>
            <p className="mb-4 px-9 text-secondary-fg md:mb-6">
              Dive deep into materials: convert youtube and podcasts to text, highlight key points,
              take notes, and generate summaries with our AI assistants – all to deepen your
              understanding.
            </p>
            <div className="relative flex h-0 flex-grow items-end justify-center overflow-hidden rounded-xl px-4">
              <Image
                src={FeatureResearchNewImage}
                className="relative -bottom-[2px] h-full w-full object-contain object-bottom"
                alt="Research Feature Image"
              />
            </div>
            {/* <div className="absolute bottom-0 left-0 flex w-full justify-center">

            </div> */}
          </div>

          {/* Organize Card */}
          <div className="relative flex h-[420px] flex-col overflow-hidden rounded-3xl border border-snip-card bg-[#F7F7F8] py-6 !pb-0 transition-all duration-300 hover:-translate-y-1 md:h-[460px] lg:col-span-4">
            <h3 className="relative z-10 mb-2 px-9 text-2xl font-medium">Organize</h3>
            <p className="relative z-10 mb-4 px-9 text-secondary-fg md:mb-6">
              Gather all the materials, thoughts and notes of a project in one board, and organize
              the project within this board. Bring everything together and create something new at
              ease. In YouMind, each board is a project.
            </p>
            <div className="relative z-0 flex flex-grow flex-col justify-end overflow-hidden align-bottom">
              <Image
                src={FeatureOrganizeImage}
                className="relative bottom-0 z-0 flex h-full w-full rounded-b-3xl object-contain object-right-bottom"
                alt="Organize Feature Image"
              />
            </div>
          </div>

          {/* Ask Feature Card - 黄色背景 */}
          <div className="relative rounded-3xl py-6 transition-all duration-300 hover:-translate-y-1 lg:col-span-8">
            <div className="-z-1 absolute inset-0 overflow-hidden rounded-3xl">
              <Image
                src={bgGradientYellow}
                alt="Yellow gradient background"
                fill
                sizes="(max-width: 768px) 100vw, 66vw"
                priority
                className="object-cover"
                quality={85}
              />
            </div>
            <h3 className="relative mb-2 px-9 text-2xl font-medium">Ask</h3>
            <p className="relative mb-4 px-9 text-secondary-fg md:mb-6">
              Ask AI whenever you have doubts or questions. Easily reference multiple materials with
              top AI models powered by OpenAI, Anthropic, Google, and DeepSeek. Enjoy seamless
              human-AI collaboration at your fingertips.
            </p>
            <div className="relative flex h-[240px] items-center justify-center overflow-hidden rounded-xl px-4 md:h-[260px]">
              <div className="relative flex h-full w-full justify-center">
                <Image
                  src={FeatureChatImage}
                  className="h-full w-full object-contain"
                  alt="Ask Feature Image"
                />
              </div>
            </div>
          </div>

          {/* Write Feature Card - 蓝色背景 */}
          <div className="relative rounded-3xl py-6 text-white transition-all duration-300 hover:-translate-y-1 lg:col-span-8">
            <div className="-z-1 absolute inset-0 overflow-hidden rounded-3xl">
              <Image
                src={bgGradientBlue}
                alt="Blue gradient background"
                fill
                sizes="(max-width: 768px) 100vw, 66vw"
                priority
                className="object-cover"
                quality={80}
              />
            </div>
            <h3 className="relative mb-2 px-9 text-2xl font-medium">Write</h3>
            <p className="relative mb-4 px-9 text-white/80 md:mb-6">
              Focus on writing: create custom prompts, add your notes, and work with AI to turn
              ideas into content of good quality.
            </p>
            <div className="relative flex h-[240px] items-center justify-center overflow-hidden rounded-xl px-4 md:h-[360px]">
              <div className="relative flex h-full w-full justify-center">
                <Image
                  src={FeatureWriteImage}
                  className="h-full w-full object-contain"
                  alt="Write Feature Image"
                />
              </div>
            </div>
          </div>

          {/* Share Feature Card */}
          <div className="flex flex-col rounded-3xl border border-snip-card bg-[#F7F7F8] px-9 py-6 !pb-0 transition-all duration-300 hover:-translate-y-1 lg:col-span-4">
            <h3 className="mb-2 text-2xl font-medium">Share</h3>
            <p className="mb-4 flex-grow text-secondary-fg md:mb-6">
              Share your work with one click, and use the link anywhere at will.
            </p>
            <div className="relative flex h-[240px] items-center justify-center overflow-hidden rounded-xl md:h-[360px]">
              <div className="relative flex h-full w-full justify-center">
                <Image
                  src={FeatureShareImage}
                  className="h-full w-full object-contain"
                  alt="Share Feature Image"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
