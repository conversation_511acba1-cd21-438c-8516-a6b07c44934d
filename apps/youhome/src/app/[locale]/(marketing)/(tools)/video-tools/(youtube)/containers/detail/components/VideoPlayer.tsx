'use client';

import { Button } from '@repo/ui/components/ui/button';
import { Play } from 'lucide-react';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

interface VideoPlayerProps {
  thumbnail: string;
  playUrl: string;
  title: string;
  videoId: string;
}

export function VideoPlayer({ thumbnail, playUrl, title, videoId }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [_currentTime, _setCurrentTime] = useState(0);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 监听视频跳转事件
  useEffect(() => {
    const handleVideoSeek = (event: CustomEvent) => {
      const { seconds, videoId: targetVideoId } = event.detail;

      // 确保是当前视频
      if (targetVideoId === videoId && iframeRef.current) {
        const iframe = iframeRef.current;
        const iframeWindow = iframe.contentWindow;

        if (iframeWindow) {
          // 发送消息给 YouTube iframe 跳转到指定时间
          iframeWindow.postMessage(
            JSON.stringify({
              event: 'command',
              func: 'seekTo',
              args: [seconds, true],
            }),
            '*',
          );
        }
      }
    };

    window.addEventListener('video-seek', handleVideoSeek as EventListener);

    return () => {
      window.removeEventListener('video-seek', handleVideoSeek as EventListener);
    };
  }, [videoId]);

  // 监听 iframe 消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data && typeof event.data === 'object') {
        if (event.data.event === 'onStateChange') {
          // 视频状态变化
          if (event.data.info === 1) {
            setIsPlaying(true);
          } else if (event.data.info === 2) {
            setIsPlaying(false);
          }
        } else if (event.data.event === 'onReady') {
          // iframe 准备就绪
          console.log('YouTube iframe ready');
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const getYouTubeEmbedUrl = () => {
    return `https://www.youtube.com/embed/${videoId}?enablejsapi=1&origin=${window.location.origin}&rel=0&modestbranding=1`;
  };

  return (
    <div className="group relative aspect-video cursor-pointer overflow-hidden rounded-xl bg-gray-900">
      {!isPlaying ? (
        <>
          <Image src={thumbnail} alt={title} className="h-full w-full object-cover" />
          <div className="absolute inset-0 flex items-center justify-center bg-black/30 transition-colors group-hover:bg-black/40">
            <Button
              onClick={handlePlay}
              size="lg"
              className="h-16 w-16 rounded-full bg-red-600 p-0 hover:bg-red-700"
            >
              <Play className="ml-1 h-6 w-6 text-white" />
            </Button>
          </div>
        </>
      ) : (
        <iframe
          ref={iframeRef}
          src={getYouTubeEmbedUrl()}
          title={title}
          className="h-full w-full"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      )}
    </div>
  );
}
