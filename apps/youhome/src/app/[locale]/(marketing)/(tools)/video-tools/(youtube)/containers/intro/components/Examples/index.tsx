'use client';

import { useRouter } from '@i18n/navigation';
import Image from 'next/image';
import { useState } from 'react';
import { UpgradeModal } from '../../../../credits/components';
import { YouTubeCreditsManager } from '../../../../credits/manager';
import { ExampleVideo, getExampleVideos } from '../../../../preset-videos';
import { YouTubePageConfig } from '../../config';

interface ExamplesProps {
  config?: YouTubePageConfig;
  onTryExample?: (url: string) => void;
  className?: string;
}

export default function Examples({
  config,
  onTryExample: _onTryExample,
  className = '',
}: ExamplesProps) {
  const [creditsManager] = useState(() => new YouTubeCreditsManager());
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeMessage, setUpgradeMessage] = useState('');
  const router = useRouter();
  const exampleVideos = getExampleVideos();

  const handleVideoClick = (video: ExampleVideo) => {
    if (!config?.pageType) {
      // 如果没有pageType，使用原有逻辑
      router.push(`/youtube-transcript-generator-detail?url=${encodeURIComponent(video.url)}`);
      return;
    }

    const pageType = config.pageType as 'transcript' | 'summary';

    // 检查是否可以访问视频详情页
    const accessCheck = creditsManager.canAccessVideoDetail(video.url, pageType);

    if (!accessCheck.canAccess) {
      // 显示升级提示
      setUpgradeMessage(accessCheck.upgradePrompt || '已达到使用限制');
      setShowUpgradeModal(true);
      return;
    }

    // 记录视频访问
    const recorded = creditsManager.recordVideoAccess(video.url, pageType);

    if (recorded) {
      // 跳转到对应的详情页面
      if (pageType === 'transcript') {
        router.push(`/youtube-transcript-generator-detail?url=${encodeURIComponent(video.url)}`);
      } else if (pageType === 'summary') {
        router.push(`/youtube-summary-detail?url=${encodeURIComponent(video.url)}`);
      }
    } else {
      setUpgradeMessage('无法记录视频访问，请重试或升级账户');
      setShowUpgradeModal(true);
    }
  };

  const handleUpgrade = () => {
    console.log('跳转到升级页面');
    setShowUpgradeModal(false);
  };

  return (
    <div className={`${className}`}>
      <div className="mb-3 pl-6 text-left">
        {/* eslint-disable-next-line i18next/no-literal-string */}
        <p className="text-lg text-secondary-fg">Examples</p>
      </div>

      <div className="mx-auto grid max-w-6xl grid-cols-1 gap-6 md:grid-cols-4">
        {exampleVideos.map((video: ExampleVideo) => (
          <div
            key={video.id}
            className="group relative cursor-pointer overflow-hidden rounded-2xl border bg-card transition-all duration-300 hover:shadow-lg"
            onClick={() => handleVideoClick(video)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleVideoClick(video);
              }
            }}
            tabIndex={0}
            role="button"
            aria-label={`观看视频: ${video.title}`}
          >
            <div className="relative aspect-video overflow-hidden">
              <Image
                src={video.thumbnail}
                alt={video.title}
                className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
              {/* 播放按钮覆盖层 */}
              <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-white/90">
                  <svg className="ml-1 h-6 w-6 text-black" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z" />
                  </svg>
                </div>
              </div>
            </div>
            {/* 视频标题 */}
            <div className="p-3">
              <h3 className="truncate text-sm font-medium text-foreground">{video.title}</h3>
            </div>
          </div>
        ))}
      </div>

      {/* 升级模态框 */}
      <UpgradeModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        message={upgradeMessage}
        onUpgrade={handleUpgrade}
      />
    </div>
  );
}
