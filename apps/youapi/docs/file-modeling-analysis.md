# File 建模分析与设计讨论

## 文档概述

本文档用于分析当前 YouAPI 项目中的 file 建模方案，记录设计决策的讨论过程，并提出潜在的改进方向。

## 当前架构分析

### 1. 文件类型抽象层次

#### 1.1 FileMeta 联合类型设计

```typescript
// 当前设计
export type FileMeta = UploadFileMeta | TransferredFileMeta | UntransferredFileMeta;

// UploadFileMeta - 用户直接上传的文件
interface UploadFileMeta {
  name: string;
  mimeType: string;
  size: number;
  storageUrl: string;
  url?: string;
  hash?: string;
  isPublic?: boolean;
  directory?: string;
}

// TransferredFileMeta - 网络文件转存后
interface TransferredFileMeta {
  originalUrl: string;
  storageUrl: string;
  name: string;
  mimeType: string;
  size: number;
  width?: number;
  height?: number;
  blurhash?: string;
  average?: { r: number; g: number; b: number };
}

// UntransferredFileMeta - 未转存的网络文件
interface UntransferredFileMeta {
  originalUrl: string;
}
```

**优点：**
- 类型安全，TypeScript 可以进行类型推断
- 明确区分了文件来源（上传 vs 网络转存）
- 支持渐进式数据填充（转存前后状态不同）

**潜在问题：**
- 联合类型在某些场景下类型判断复杂
- 公共字段重复定义
- 扩展新的文件来源类型时需要修改联合类型

#### 1.2 Snip 多态实体设计

当前基于 Snip 的文件实体设计：

```
SnipEntity (基类)
├── ImageEntity
├── TextFileEntity  
├── PDFEntity
├── OfficeEntity
├── VoiceEntity
└── VideoEntity
```

**优点：**
- 统一的持久化机制
- 共享通用字段和行为
- 便于统一查询和管理

**潜在问题：**
- 单表继承可能导致字段冗余
- 不同文件类型的特殊需求难以优雅处理
- 查询性能可能受影响（需要类型过滤）

### 2. 存储架构

#### 2.1 双存储桶策略

```
youmind-user-files-private/    # 私有文件，需签名访问
├── user-files/                # 用户上传文件
├── gen-audio/                 # 生成的音频
└── gen-images/                # 生成的图片

youmind-internet-files-public/ # 公开文件，直接访问
└── web-images/                # 网络图片转存
```

**优点：**
- 安全性分离（私有 vs 公开）
- 访问性能优化（公开文件无需签名）
- 清晰的目录结构

**潜在问题：**
- 存储桶切换逻辑分散在代码中
- 难以动态调整文件的公开/私有状态
- 跨桶操作复杂度较高

#### 2.2 文件去重机制

基于 SHA256 哈希的文件去重：

```typescript
// 当前实现
const hash = await this.fileHashService.calculateSha256(buffer);
// 检查是否已存在相同文件
```

**优点：**
- 节省存储空间
- 提高上传效率
- 天然的内容校验

**潜在问题：**
- 哈希计算开销
- 不同用户的相同文件权限处理复杂
- 删除策略需要引用计数

### 3. 业务集成设计

#### 3.1 实体关联关系

```
File/Snip ←→ Space   (权限控制)
File/Snip ←→ User    (所有权)
File/Snip ←→ Board   (展示位置)
File/Snip ←→ Chat    (对话绑定)
```

#### 3.2 配额管理

通过 UsageRecordDomainService 集成：
- 存储用量统计
- 配额检查
- 计费系统对接

## 设计讨论要点

### 讨论点 1: FileMeta 类型设计优化

**当前问题：**
- 联合类型的类型收窄复杂
- 公共字段重复定义
- 扩展性有限

**可能的改进方向：**
- 基类 + 特化类设计
- 使用泛型减少重复
- 策略模式处理不同来源

### 讨论点 2: Snip 多态 vs 独立 File 实体

**当前基于 Snip 的设计：**
- 统一持久化
- 共享业务逻辑
- 单表存储

**独立 File 实体的考虑：**
- 更清晰的职责分离
- 更好的查询性能
- 更灵活的扩展能力

### 讨论点 3: 存储策略优化

**当前双桶策略的考虑：**
- 安全性 vs 性能平衡
- 运维复杂度
- 成本优化空间

### 讨论点 4: 文件生命周期管理

**当前缺失的考虑：**
- 文件版本管理
- 删除和回收策略
- 归档和冷存储
- 数据迁移策略

### 讨论点 5: 扩展性需求

**未来可能的需求：**
- 多云存储支持
- 文件加密需求
- 更复杂的权限模型
- 文件处理管道扩展

## 后续改进方向

### 短期改进
- [ ] FileMeta 类型系统重构
- [ ] 文件权限模型优化
- [ ] 存储配置抽象化

### 中期改进
- [ ] 文件生命周期管理
- [ ] 性能监控和优化
- [ ] 多存储后端支持

### 长期规划
- [ ] 分布式文件系统集成
- [ ] AI 驱动的文件处理
- [ ] 边缘存储和 CDN 优化

## 讨论记录

### 讨论会议 1 - 初始分析 (2025-08-18)

**参与者：** [待填写]

**讨论内容：**
- 当前架构分析完成
- 确定了 5 个主要讨论点
- 建立了改进优先级框架

**决策：**
- [待记录具体决策]

**后续行动：**
- [待分配具体任务]

---

*本文档将持续更新，记录 file 建模的演进过程和设计决策。*