import { INestApplication } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import { HttpExceptionFilter } from '@/common/filters/http-exception.filter';
import { YouapiClsService } from '@/common/services/cls.service';
import { TestExceptionController } from './test-exception.controller';

// Mock YouapiClsService
const mockClsService = {
  getUserId: jest.fn(),
  getSpaceId: jest.fn(),
};

// Mock OpenTelemetry
const mockSpan = {
  setAttributes: jest.fn(),
  spanContext: jest.fn().mockReturnValue({
    traceId: 'mock-trace-id-12345',
    spanId: 'mock-span-id-67890',
  }),
};

jest.mock('@opentelemetry/api', () => ({
  trace: {
    getActiveSpan: jest.fn(() => mockSpan),
  },
}));

describe('HttpExceptionFilter E2E Tests', () => {
  let app: INestApplication;

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();
    mockClsService.getUserId.mockReturnValue(null);
    mockClsService.getSpaceId.mockReturnValue(null);

    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [TestExceptionController],
      providers: [
        {
          provide: APP_FILTER,
          useClass: HttpExceptionFilter,
        },
        {
          provide: YouapiClsService,
          useValue: mockClsService,
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();

    // 手动应用 HttpExceptionFilter
    // const httpExceptionFilter = app.get(HttpExceptionFilter);
    // app.useGlobalFilters(httpExceptionFilter);

    await app.init();
  });

  afterEach(async () => {
    await app.close();
    jest.restoreAllMocks();
  });

  describe('自定义 RestError 异常处理', () => {
    it('应该正确处理 InvalidArguments (400)', async () => {
      const response = await request(app.getHttpServer()).get(
        '/test-exception/rest-error/invalid-arguments',
      );

      expect(response.body).toEqual({
        code: 'InvalidArguments',
        status: 400,
        message: 'Invalid input parameters provided',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 Unauthenticated (401)', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/rest-error/unauthenticated')
        .expect(401);

      expect(response.body).toEqual({
        code: 'Unauthenticated',
        status: 401,
        message: 'User authentication required',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 PermissionDenied (403)', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/rest-error/permission-denied')
        .expect(403);

      expect(response.body).toEqual({
        code: 'PermissionDenied',
        status: 403,
        message: 'Access denied to this resource',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 NotFound (404)', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/rest-error/not-found')
        .expect(404);

      expect(response.body).toEqual({
        code: 'NotFound',
        status: 404,
        message: expect.any(String),
        detail: {
          resource: 'thought',
          id: 'non-existent-id',
        },
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 UnknownError (500)', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/rest-error/unknown-error')
        .expect(500);

      expect(response.body).toEqual({
        code: 'UnknownError',
        status: 500,
        message: 'Internal server error.',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });
  });

  describe('NestJS HttpException 处理', () => {
    it('应该正确处理 BadRequestException', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/http-exception/bad-request')
        .expect(400);

      expect(response.body).toEqual({
        code: 'BadRequestException',
        status: 400,
        message: 'This is a bad request',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 UnauthorizedException', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/http-exception/unauthorized')
        .expect(401);

      expect(response.body).toEqual({
        code: 'UnauthorizedException',
        status: 401,
        message: 'Unauthorized access',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 ForbiddenException', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/http-exception/forbidden')
        .expect(403);

      expect(response.body).toEqual({
        code: 'ForbiddenException',
        status: 403,
        message: 'Forbidden resource',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 NotFoundException', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/http-exception/not-found')
        .expect(404);

      expect(response.body).toEqual({
        code: 'NotFoundException',
        status: 404,
        message: 'Resource not found',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理复杂响应的 HttpException', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/http-exception/complex-response')
        .expect(400);

      expect(response.body).toEqual({
        code: 'BadRequestException',
        status: 400,
        message: 'Complex validation failed',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });
  });

  describe('未知异常处理', () => {
    it('应该正确处理标准 JavaScript Error', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/javascript-error')
        .expect(500);

      expect(response.body).toEqual({
        code: 'UnknownError',
        status: 500,
        message: 'Internal server error.',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理字符串异常', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/non-error-exception')
        .expect(500);

      expect(response.body).toEqual({
        code: 'UnknownError',
        status: 500,
        message: 'Internal server error.',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 null 异常', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/null-exception')
        .expect(500);

      expect(response.body).toEqual({
        code: 'UnknownError',
        status: 500,
        message: 'Internal server error.',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理 undefined 异常', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/undefined-exception')
        .expect(500);

      expect(response.body).toEqual({
        code: 'UnknownError',
        status: 500,
        message: 'Internal server error.',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理数字异常', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/number-exception')
        .expect(500);

      expect(response.body).toEqual({
        code: 'UnknownError',
        status: 500,
        message: 'Internal server error.',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });

    it('应该正确处理对象异常', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/object-exception')
        .expect(500);

      expect(response.body).toEqual({
        code: 'UnknownError',
        status: 500,
        message: 'Internal server error.',
        detail: undefined,
        trace_id: 'mock-trace-id-12345',
      });
    });
  });

  describe('IAM 装饰器异常处理', () => {
    describe('InsufficientCreditsException (402)', () => {
      it('应该正确处理 FREE 用户积分不足', async () => {
        const response = await request(app.getHttpServer())
          .get('/test-exception/rest-error/insufficient-credits')
          .expect(402);

        expect(response.body).toEqual({
          code: 'InsufficientCreditsException',
          status: 402,
          message: 'Credits exhausted. Available: 0 (free tier)',
          trace_id: 'mock-trace-id-12345',
        });
      });

      it('应该正确处理 PRO 用户积分不足', async () => {
        const response = await request(app.getHttpServer())
          .get('/test-exception/rest-error/insufficient-credits-pro')
          .expect(402);

        expect(response.body).toEqual({
          code: 'InsufficientCreditsException',
          status: 402,
          message: 'Not enough credits for this operation. Available: 5 (pro tier)',
          trace_id: 'mock-trace-id-12345',
        });
      });

      it('应该正确处理 MAX 用户积分不足', async () => {
        const response = await request(app.getHttpServer())
          .get('/test-exception/rest-error/insufficient-credits-max')
          .expect(402);

        expect(response.body).toEqual({
          code: 'InsufficientCreditsException',
          status: 402,
          message: 'Insufficient credits for premium operation. Available: 100 (max tier)',
          trace_id: 'mock-trace-id-12345',
        });
      });
    });

    describe('QuotaExceededException (402)', () => {
      it('应该正确处理存储配额超出', async () => {
        const response = await request(app.getHttpServer())
          .get('/test-exception/iam-decorator/quota-exceeded-storage')
          .expect(402);

        expect(response.body).toEqual({
          code: 'QuotaExceededException',
          status: 402,
          message: 'storage quota exceeded. Used 1.0 GB, limit 512.0 MB (free tier)',
          trace_id: 'mock-trace-id-12345',
        });
      });

      it('应该正确处理新建 Snip 配额超出', async () => {
        const response = await request(app.getHttpServer())
          .get('/test-exception/iam-decorator/quota-exceeded-snip')
          .expect(402);

        expect(response.body).toEqual({
          code: 'QuotaExceededException',
          status: 402,
          message: 'new_snip quota exceeded. Used 105 items, limit 100 items (pro tier)',
          trace_id: 'mock-trace-id-12345',
        });
      });
    });

    describe('LimitExceededException (402)', () => {
      it('应该正确处理文件大小限制超出', async () => {
        const response = await request(app.getHttpServer())
          .get('/test-exception/iam-decorator/limit-exceeded-file-size')
          .expect(402);

        expect(response.body).toEqual({
          code: 'LimitExceededException',
          status: 402,
          message: 'file_size limit exceeded. Used 20.0 MB, limit 10.0 MB (free tier)',
          trace_id: 'mock-trace-id-12345',
        });
      });

      it('应该正确处理文档页数限制超出', async () => {
        const response = await request(app.getHttpServer())
          .get('/test-exception/iam-decorator/limit-exceeded-document-pages')
          .expect(402);

        expect(response.body).toEqual({
          code: 'LimitExceededException',
          status: 402,
          message: 'document_pages limit exceeded. Used 55 pages, limit 50 pages (pro tier)',
          trace_id: 'mock-trace-id-12345',
        });
      });

      it('应该正确处理音频时长限制超出', async () => {
        const response = await request(app.getHttpServer())
          .get('/test-exception/iam-decorator/limit-exceeded-audio-duration')
          .expect(402);

        expect(response.body).toEqual({
          code: 'LimitExceededException',
          status: 402,
          message:
            'audio_duration limit exceeded. Used 120.0 minutes, limit 60.0 minutes (max tier)',
          trace_id: 'mock-trace-id-12345',
        });
      });
    });
  });

  describe('正常请求不应触发异常处理', () => {
    it('应该正常处理成功请求', async () => {
      const response = await request(app.getHttpServer())
        .get('/test-exception/success')
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Everything is working fine',
      });
    });
  });
});
