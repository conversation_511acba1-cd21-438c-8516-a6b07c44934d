import {
  BadRequestException,
  Controller,
  ForbiddenException,
  Get,
  NotFoundException,
  Query,
  UnauthorizedException,
} from '@nestjs/common';

import {
  InvalidArguments,
  NotFound,
  PermissionDenied,
  ResourceEnum,
  Unauthenticated,
  UnknownError,
} from '@/common/errors';
import { InsufficientCreditsException } from '@/modules/iam/decorators/check-credits.decorator';
import { QuotaExceededException } from '@/modules/iam/decorators/check-quota.decorator';
import { LimitExceededException } from '@/modules/iam/decorators/limit.decorator';

@Controller('test-exception')
export class TestExceptionController {
  @Get('rest-error/invalid-arguments')
  throwInvalidArguments() {
    throw new InvalidArguments('Invalid input parameters provided');
  }

  @Get('rest-error/unauthenticated')
  throwUnauthenticated() {
    throw new Unauthenticated('User authentication required');
  }

  @Get('rest-error/permission-denied')
  throwPermissionDenied() {
    throw new PermissionDenied('Access denied to this resource');
  }

  @Get('rest-error/not-found')
  throwNotFound() {
    throw new NotFound({
      resource: ResourceEnum.THOUGHT,
      id: 'non-existent-id',
    });
  }

  @Get('rest-error/unknown-error')
  throwUnknownError() {
    throw new UnknownError('Something went wrong internally');
  }

  @Get('rest-error/insufficient-credits')
  throwInsufficientCreditsException() {
    throw new InsufficientCreditsException('Credits exhausted. Available: 0 (free tier)');
  }

  @Get('rest-error/insufficient-credits-pro')
  throwInsufficientCreditsExceptionPro() {
    throw new InsufficientCreditsException(
      'Not enough credits for this operation. Available: 5 (pro tier)',
    );
  }

  @Get('rest-error/insufficient-credits-max')
  throwInsufficientCreditsExceptionMax() {
    throw new InsufficientCreditsException(
      'Insufficient credits for premium operation. Available: 100 (max tier)',
    );
  }

  @Get('iam-decorator/quota-exceeded-storage')
  throwQuotaExceededStorage() {
    throw new QuotaExceededException(
      'storage quota exceeded. Used 1.0 GB, limit 512.0 MB (free tier)',
    );
  }

  @Get('iam-decorator/quota-exceeded-snip')
  throwQuotaExceededSnip() {
    throw new QuotaExceededException(
      'new_snip quota exceeded. Used 105 items, limit 100 items (pro tier)',
    );
  }

  @Get('iam-decorator/limit-exceeded-file-size')
  throwLimitExceededFileSize() {
    throw new LimitExceededException(
      'file_size limit exceeded. Used 20.0 MB, limit 10.0 MB (free tier)',
    );
  }

  @Get('iam-decorator/limit-exceeded-document-pages')
  throwLimitExceededDocumentPages() {
    throw new LimitExceededException(
      'document_pages limit exceeded. Used 55 pages, limit 50 pages (pro tier)',
    );
  }

  @Get('iam-decorator/limit-exceeded-audio-duration')
  throwLimitExceededAudioDuration() {
    throw new LimitExceededException(
      'audio_duration limit exceeded. Used 120.0 minutes, limit 60.0 minutes (max tier)',
    );
  }

  @Get('http-exception/bad-request')
  throwBadRequestException() {
    throw new BadRequestException('This is a bad request');
  }

  @Get('http-exception/unauthorized')
  throwUnauthorizedException() {
    throw new UnauthorizedException('Unauthorized access');
  }

  @Get('http-exception/forbidden')
  throwForbiddenException() {
    throw new ForbiddenException('Forbidden resource');
  }

  @Get('http-exception/not-found')
  throwNotFoundHttpException() {
    throw new NotFoundException('Resource not found');
  }

  @Get('http-exception/complex-response')
  throwHttpExceptionWithComplexResponse() {
    throw new BadRequestException({
      message: 'Complex validation failed',
      error: 'Bad Request',
      statusCode: 400,
      details: {
        field: 'username',
        issue: 'already exists',
      },
    });
  }

  @Get('javascript-error')
  throwJavaScriptError() {
    const error = new Error('Standard JavaScript error occurred');
    error.stack =
      'Error: Standard JavaScript error occurred\n    at TestExceptionController.throwJavaScriptError';
    throw error;
  }

  @Get('non-error-exception')
  throwNonErrorException() {
    // 抛出非 Error 类型的异常
    throw 'This is a string exception';
  }

  @Get('null-exception')
  throwNullException() {
    throw null;
  }

  @Get('undefined-exception')
  throwUndefinedException() {
    throw undefined;
  }

  @Get('number-exception')
  throwNumberException() {
    throw 42;
  }

  @Get('object-exception')
  throwObjectException() {
    throw {
      message: 'Object exception',
      code: 'CUSTOM_ERROR',
      data: { test: true },
    };
  }

  @Get('success')
  getSuccess() {
    return { success: true, message: 'Everything is working fine' };
  }

  @Get('with-query-params')
  getWithQueryParams(@Query('cause') cause?: string) {
    if (cause === 'error') {
      throw new Error('Query param triggered error');
    }
    return { success: true, cause };
  }
}
