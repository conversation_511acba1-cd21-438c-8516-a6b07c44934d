import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { OverviewPageData } from '@/modules/material-mng/domain/shared/value-objects/page-data.vo';
import { SnipType } from '@/modules/material-mng/domain/snip/models/snip.entity';
import { CreateBlockContentParam } from '@/modules/material-mng/services/commands/snip/create-snip-blocks.command';
import { GenerateOptionsDto } from './explain.dto';

export class OverviewRequestDto {
  @ApiProperty({
    description: 'Primary AI response language',
    example: 'English',
  })
  @IsString()
  aiLanguage: string;

  @ApiPropertyOptional({
    description: 'Optional secondary language for bilingual support',
    example: 'Chinese',
  })
  @IsOptional()
  @IsString()
  aiSecondLanguage?: string;

  @ApiPropertyOptional({
    description: 'Enable/disable bilingual responses',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  enableBilingual?: boolean;

  @ApiPropertyOptional({ description: 'Whether to stream the response' })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;

  @ApiPropertyOptional({
    description: 'Additional options for the request',
    type: GenerateOptionsDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => GenerateOptionsDto)
  options?: GenerateOptionsDto;
}

export class OverviewRequestPageDataDto extends OverviewRequestDto {
  @ApiProperty({
    description: 'Content data to be analyzed for overview generation',
  })
  pageData: OverviewPageData;
}

export class OverviewRequestPlainDto extends OverviewRequestDto {
  @ApiProperty({
    description: 'Content data to be analyzed for overview generation',
  })
  @IsObject()
  pageData: {
    type: SnipType;
    title: string;
    content: CreateBlockContentParam;
    transcript: CreateBlockContentParam;
    showNotes: CreateBlockContentParam;
    speakerLabels: string;
  };
}
