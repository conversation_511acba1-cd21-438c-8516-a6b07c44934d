import { Logger } from '@nestjs/common';
import { AggregateRoot } from '@nestjs/cqrs';
import { CoreMessage } from 'ai';
import { ChatPromptClient, LangfuseGenerationClient } from 'langfuse';
import { isEqual } from 'lodash-es';
import { ChatCompletionToolChoiceOption } from 'openai/resources/index.js';
import { uuidv7 } from 'uuidv7';
import {
  CompletionBlockTypeEnum,
  DEFAULT_AI_CHAT_MODEL,
  GenerationStatusEnum,
  LLMProviders,
  LLMs,
  MODEL_DEFINITION,
  ToolNames,
} from '@/common/types';
import { SafeParse } from '@/common/utils';
import {
  CompletionBlock,
  ToolCompletionBlock,
} from '@/modules/ai/domain/models/completion-block.entity';
import { generations } from '@/shared/db/public.schema';
import { blocksToMessages } from '../../utils/blocksToMessages';

export type GenerationDO = typeof generations.$inferSelect & {
  status: GenerationStatusEnum;
  toolChoice: string;
};

export class Generation extends AggregateRoot {
  protected readonly logger = new Logger(Generation.name);

  public id: string;
  public createdAt: Date;
  public updatedAt: Date;
  public deletedAt: Date | null;
  public isNew: boolean = true;
  public isModified: boolean = false;

  public model: LLMs;
  public readonly tools: ToolNames[];
  public readonly toolChoice: ChatCompletionToolChoiceOption;
  public modelOptions: Record<string, any>;
  public traceMetadata: Record<string, string>;
  public bizArgs: Record<string, any>;
  public readonly prediction: string[];
  public promptName: string;
  public promptVersion: number;
  public promptMessages: CoreMessage[];
  public generatedMessages: CoreMessage[];
  public blocks: CompletionBlock[];
  public traceId: string | null = null;
  public chatMessageId: string | null = null;

  protected status: GenerationStatusEnum = GenerationStatusEnum.PENDING;
  protected trace: LangfuseGenerationClient | null = null;
  protected prompt: ChatPromptClient;

  constructor(
    param: {
      id?: string;
      createdAt?: Date;
      updatedAt?: Date;
      deletedAt?: Date | null;
      model?: LLMs;
      tools?: ToolNames[];
      toolChoice?: ChatCompletionToolChoiceOption;
      bizArgs?: Record<string, any>;
      modelOptions?: Record<string, any>;
      traceMetadata?: Record<string, string>;
      promptName?: string;
      promptVersion?: number;
      promptMessages?: CoreMessage[];
      prediction?: string[];
      traceId?: string;
      status?: GenerationStatusEnum;
      chatMessageId?: string;
    },
    isNew: boolean = true,
  ) {
    super();

    const now = new Date();
    this.id = param.id || uuidv7();
    this.createdAt = param.createdAt || now;
    this.updatedAt = param.updatedAt || now;
    this.deletedAt = param.deletedAt || null;
    this.isNew = isNew;
    this.model = param.model || DEFAULT_AI_CHAT_MODEL;
    this.tools = param.tools || [];
    this.toolChoice = param.toolChoice || (this.tools.length > 0 ? 'auto' : 'none');

    const temperature = param.modelOptions?.temperature || 0.5;
    const topP = param.modelOptions?.topP || 1;
    const frequencyPenalty = param.modelOptions?.frequencyPenalty || 0;
    const presencePenalty = param.modelOptions?.presencePenalty || 0;
    const config = MODEL_DEFINITION.get(this.model);
    const maxTokens = param.modelOptions?.maxTokens || config?.output_token_limit - 1 || 4096;
    const provider = param.modelOptions?.provider || null;

    this.bizArgs = param.bizArgs || {};
    this.prediction = param.prediction || [];
    this.traceMetadata = param.traceMetadata || {};

    // Filter out model from modelOptions to prevent overriding the LanguageModelV1 object
    const { model: _, ...filteredModelOptions } = param.modelOptions || {};
    this.modelOptions = {
      maxTokens,
      ...filteredModelOptions,
      ...(temperature ? { temperature } : {}),
      ...(topP ? { topP } : {}),
      ...(frequencyPenalty ? { frequencyPenalty } : {}),
      ...(presencePenalty ? { presencePenalty } : {}),
      ...(provider ? { provider } : {}),
    };
    this.promptName = param.promptName || '';
    this.promptVersion = param.promptVersion || 1;
    this.promptMessages = param.promptMessages || [];
    this.blocks = [];
    this.generatedMessages = [];
    this.traceId = param.traceId || null;
    this.chatMessageId = param.chatMessageId || null;
    this.status = param.status || GenerationStatusEnum.PENDING;
  }

  public setModel(model: LLMs) {
    this.model = model;
    this.updatedAt = new Date();
    this.isModified = true;
    return this;
  }

  public setPrompt(prompt: ChatPromptClient, modelOptions?: Record<string, any>) {
    this.prompt = prompt;
    this.promptName = prompt.name;
    this.promptVersion = prompt.version;

    const promptConfig = prompt.config as {
      model?: LLMs;
      temperature?: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
      provider?: LLMProviders;
    };

    const temperature = modelOptions?.temperature || promptConfig?.temperature || 0.5;
    const topP = modelOptions?.topP || promptConfig?.topP || 1;
    const frequencyPenalty = modelOptions?.frequencyPenalty || promptConfig?.frequencyPenalty || 0;
    const presencePenalty = modelOptions?.presencePenalty || promptConfig?.presencePenalty || 0;
    const config = MODEL_DEFINITION.get(this.model);
    const maxTokens = modelOptions?.maxTokens || config?.output_token_limit - 1 || 4096;
    const provider = modelOptions?.provider || promptConfig?.provider || null;

    // Filter out model from modelOptions to prevent overriding the LanguageModelV1 object
    const { model: _, ...filteredModelOptions } = modelOptions || {};
    this.modelOptions = {
      maxTokens,
      ...filteredModelOptions,
      ...(topP ? { topP } : {}),
      ...(temperature ? { temperature } : {}),
      ...(presencePenalty ? { presencePenalty } : {}),
      ...(frequencyPenalty ? { frequencyPenalty } : {}),
      ...(provider ? { provider } : {}),
    };
    this.updatedAt = new Date();
    this.isModified = true;

    return this;
  }

  public getStatus(): GenerationStatusEnum {
    return this.status;
  }

  public setStatus(status: GenerationStatusEnum) {
    if (this.status !== status) {
      this.status = status;
      this.updatedAt = new Date();
      this.isModified = true;
    }
  }

  public setChatMessageId(messageId: string) {
    if (this.chatMessageId !== messageId) {
      this.chatMessageId = messageId;
      this.updatedAt = new Date();
      this.isModified = true;
    }
    return this;
  }

  public setBizArgs(bizArgs: Record<string, any>) {
    this.bizArgs = {
      ...this.bizArgs,
      ...bizArgs,
    };
    this.updatedAt = new Date();
    this.isModified = true;
    return this;
  }

  public setTraceId(traceId: string) {
    this.traceId = traceId;
    this.updatedAt = new Date();
    this.isModified = true;
    return this;
  }

  public setTraceMetadata(traceMetadata: Record<string, string>) {
    this.traceMetadata = {
      ...this.traceMetadata,
      ...traceMetadata,
    };
    this.updatedAt = new Date();
    this.isModified = true;
    return this;
  }

  /**
   * Mark as existing (no longer new)
   */
  public markAsExisting(): void {
    this.isNew = false;
    this.isModified = false;
  }

  /**
   * Mark as unmodified
   */
  public markAsUnmodified(): void {
    this.isModified = false;
  }

  public updatePromptMessages(promptMessages: CoreMessage[]): void {
    // Use lodash isEqual for deep comparison
    if (!isEqual(this.promptMessages, promptMessages)) {
      this.promptMessages = promptMessages;
      this.updatedAt = new Date();
      this.isModified = true;
    }
  }

  /**
   * Convert blocks to generated messages and mark as modified if changed
   */
  public updateGeneratedMessages(): void {
    if (!this.blocks?.length) return;
    const newMessages = blocksToMessages(this.blocks);

    // Use lodash isEqual for deep comparison
    if (!isEqual(this.generatedMessages, newMessages)) {
      this.generatedMessages = newMessages;
      this.updatedAt = new Date();
      this.isModified = true;
    }
  }

  public getAllMessages(): CoreMessage[] {
    return [...this.promptMessages, ...this.generatedMessages];
  }

  /**
   * Convert to data object for database persistence
   */
  public toDO(): GenerationDO {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
      model: this.model,
      tools: this.tools,
      toolChoice:
        typeof this.toolChoice === 'string' ? this.toolChoice : JSON.stringify(this.toolChoice),
      modelOptions: this.modelOptions,
      traceMetadata: this.traceMetadata,
      bizArgs: this.bizArgs,
      prediction: this.prediction,
      status: this.status,
      promptName: this.promptName,
      promptVersion: this.promptVersion,
      promptMessages: this.promptMessages,
      generatedMessages: this.generatedMessages,
      chatMessageId: this.chatMessageId,
      traceId: this.traceId,
    };
  }

  public getToolBlockByToolId(toolId: string): ToolCompletionBlock | null {
    return (
      (this.blocks?.find(
        (block) =>
          block.type === CompletionBlockTypeEnum.TOOL &&
          (block as ToolCompletionBlock).toolId === toolId,
      ) as ToolCompletionBlock) || null
    );
  }

  static createFromGeneration(current: Generation): Generation {
    const generation = new Generation(
      {
        model: current.model,
        tools: current.tools,
        toolChoice: current.toolChoice as ChatCompletionToolChoiceOption,
        bizArgs: current.bizArgs,
        modelOptions: current.modelOptions,
        traceMetadata: current.traceMetadata,
        promptName: current.promptName,
        promptVersion: current.promptVersion,
        promptMessages: current.getAllMessages(),
        prediction: current.prediction,
        traceId: current.traceId,
        chatMessageId: current.chatMessageId,
      },
      true,
    ); // New generation created from current one

    return generation;
  }

  static createFromPrompt(param: {
    model?: LLMs;
    tools?: ToolNames[];
    toolChoice?: ChatCompletionToolChoiceOption;
    bizArgs?: Record<string, any>;
    modelOptions?: Record<string, any>;
    traceMetadata?: Record<string, string>;
    prompt?: ChatPromptClient;
    promptMessages?: CoreMessage[];
    prediction?: string[];
    traceId?: string;
    chatMessageId?: string;
  }): Generation {
    const generation = new Generation(
      {
        model: param.model || (param.prompt?.config as { model?: LLMs })?.model,
        tools: param.tools,
        toolChoice: param.toolChoice,
        bizArgs: param.bizArgs,
        modelOptions: param.modelOptions,
        traceMetadata: param.traceMetadata,
        promptName: param.prompt?.name,
        promptVersion: param.prompt?.version,
        promptMessages: param.promptMessages,
        prediction: param.prediction,
        chatMessageId: param.chatMessageId,
        traceId: param.traceId,
      },
      true,
    );
    generation.setPrompt(param.prompt, param.modelOptions);

    return generation;
  }

  static fromDO(data: GenerationDO): Generation {
    const generation = new Generation(
      {
        id: data.id,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt,
        deletedAt: !data.deletedAt ? null : data.deletedAt,
        model: data.model as LLMs,
        tools: data.tools as ToolNames[],
        toolChoice: SafeParse(
          data.toolChoice,
          true,
          data.toolChoice,
        ) as ChatCompletionToolChoiceOption,
        bizArgs: data.bizArgs,
        modelOptions: data.modelOptions,
        traceMetadata: data.traceMetadata as Record<string, string>,
        promptName: data.promptName,
        promptVersion: data.promptVersion,
        promptMessages: data.promptMessages as CoreMessage[],
        prediction: data.prediction as string[],
        traceId: data.traceId,
        chatMessageId: data.chatMessageId,
        status: data.status,
      },
      false,
    ); // Loaded from database, so not new

    generation.generatedMessages = (data.generatedMessages as CoreMessage[]) || [];

    return generation;
  }
}
