import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  ContentFormatEnum,
  ContentHandler,
  ContentVO,
  LanguageEnum,
  ProcessStatusEnum,
} from '@repo/common';
import { YouapiSse } from '@/common/sse';
import { LangfuseService } from '@/infra/langfuse';
import { BlockContent } from '@/modules/material-mng/domain/block/models';
import { OverviewPageData } from '@/modules/material-mng/domain/shared/value-objects/page-data.vo';
import { SnipType } from '@/modules/material-mng/domain/snip/models/snip.entity';
import { BaseController } from '@/shared/base.controller';
import { CreateLLMFeedbackDto } from '../dto/create-llm-feedback.dto';
import { CreateTingwuTaskDto } from '../dto/create-tingwu-task.dto';
import { EditImageDto } from '../dto/edit-image.dto';
import { ExplainRequestDto } from '../dto/explain.dto';
import { GetSuggestionByWebPageDto } from '../dto/get-suggestion-by-webpage.dto';
import { GetTingwuTaskInfoDto } from '../dto/get-tingwu-task-info.dto';
import { OverviewRequestPlainDto } from '../dto/overview.dto';
import { SelfDescByFeedsRequestDto } from '../dto/self-desc-by-feeds.dto';
import { SimpleSummaryRequestDto } from '../dto/simple-summary.dto';
import { StopTingwuTaskDto } from '../dto/stop-tingwu-task.dto';
import { UpdateCompletionBlockDto } from '../dto/update-completion-block.dto';
import { UpdateLLMFeedbackDto } from '../dto/update-llm-feedback.dto';
import { UpdateCompletionBlockCommand } from '../services/commands/update-completion-block.command';
import { ExplainService } from '../services/explain.service';
import { ImageService } from '../services/image.service';
import { OverviewService } from '../services/overview.service';
import { SelfDescService } from '../services/self-desc.service';
import { SimpleSummaryService } from '../services/simple-summary.service';
import { SuggestionService } from '../services/suggestion.service';
import { TingwuService } from '../services/tingwu.service';

@ApiTags('AI')
@Controller('api/v1')
export class AIController extends BaseController {
  constructor(
    private readonly explainService: ExplainService,
    private readonly simpleSummaryService: SimpleSummaryService,
    private readonly imageService: ImageService,
    private readonly overviewService: OverviewService,
    private readonly langfuseService: LangfuseService,
    private readonly tingwuService: TingwuService,
    private readonly selfDescService: SelfDescService,
    private readonly suggestionService: SuggestionService,
  ) {
    super();
  }

  @Post('createLLMFeedback')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建LLM反馈',
    description: '为LLM生成创建用户反馈评分',
  })
  @ApiResponse({
    status: 200,
    description: '成功创建反馈',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
      },
    },
  })
  async createLLMFeedback(@Body() dto: CreateLLMFeedbackDto) {
    const feedback = await this.langfuseService.createScore(dto.traceId, {
      name: dto.name,
      value: dto.value,
      comment: dto.comment,
    });
    return { id: feedback.id };
  }

  @Post('updateLLMFeedback')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新LLM反馈',
    description: '更新LLM生成的用户反馈评分',
  })
  @ApiResponse({
    status: 200,
    description: '成功更新反馈',
  })
  async updateLLMFeedback(@Body() dto: UpdateLLMFeedbackDto): Promise<void> {
    await this.langfuseService.updateScore(dto.scoreId, {
      name: dto.name,
      value: dto.value,
      comment: dto.comment,
    });
  }

  @Post('createTingwuTask')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建 tingwu 任务用于实时转写',
    description: '创建 tingwu 任务用于实时转写',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
  })
  async createTingwuTask(@Body() dto: CreateTingwuTaskDto) {
    return await this.tingwuService.createTask(dto);
  }

  @Post('stopTingwuTask')
  @HttpCode(200)
  @ApiOperation({
    summary: '停止 tingwu 任务',
    description: '停止 tingwu 任务',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
  })
  async stopTingwuTask(@Body() dto: StopTingwuTaskDto) {
    return await this.tingwuService.stopTask(dto.taskId);
  }

  @Post('getTingwuTaskInfo')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取当前 tingwu 任务状态',
    description: '获取当前 tingwu 任务状态，可以用来轮询',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
  })
  async getTingwuTaskInfo(@Body() dto: GetTingwuTaskInfoDto) {
    return await this.tingwuService.getTaskInfo(dto.taskId);
  }

  @Post('editImage')
  @HttpCode(200)
  @ApiOperation({
    summary: '编辑图片',
    description: '编辑图片',
  })
  @ApiResponse({
    status: 200,
    description: 'OK',
  })
  async editImage(@Body() dto: EditImageDto) {
    return await this.imageService.editImage({
      url: dto.url,
      prompt: dto.prompt,
      size: dto.size,
      mask: dto.mask,
      quality: dto.quality === 'auto' ? 'medium' : dto.quality || 'medium',
    });
  }

  @Post('getSelfDescByFeeds')
  @HttpCode(200)
  @ApiOperation({
    summary: '通过 feed 流来获取用户的爱好分析',
    description: '通过 feed 流来获取用户的爱好分析',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @YouapiSse()
  async getSelfDescByFeeds(@Body() dto: SelfDescByFeedsRequestDto) {
    return await this.selfDescService.getSelfDescByFeeds(dto);
  }

  @Post('getExplain/chat/completions')
  @HttpCode(200)
  @ApiOperation({
    summary: '插件 Explain 功能',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @YouapiSse()
  async explainCompletions(@Body() dto: ExplainRequestDto) {
    return await this.explainService.explain(dto);
  }

  @Post('getSimpleSummary')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Simple Summary 功能，当前用于插件的 Batch Reading',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @YouapiSse()
  async simpleSummaryCompletions(@Body() dto: SimpleSummaryRequestDto) {
    return await this.simpleSummaryService.run(dto);
  }

  @Post('getOverview/chat/completions')
  @HttpCode(200)
  @ApiOperation({
    summary: '生成 Overview，给插件用',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @ApiBody({ type: OverviewRequestPlainDto })
  @YouapiSse()
  async overviewCompletions(@Body() dto: OverviewRequestPlainDto) {
    const pageData = new OverviewPageData({
      type: dto.pageData?.type || SnipType.UNKNOWN_WEBPAGE,
      title: dto.pageData?.title || '',
      content: dto.pageData?.content
        ? ContentHandler.fromVO(dto.pageData.content as unknown as ContentVO)
        : undefined,
      transcript: dto.pageData?.transcript
        ? BlockContent.create({
            blockId: '',
            language: dto.aiLanguage as LanguageEnum,
            format: ContentFormatEnum.READER_HTML,
            raw: dto.pageData?.transcript?.raw || '',
            plain: dto.pageData?.transcript?.plain || '',
            status: ProcessStatusEnum.DONE,
          })
        : undefined,
      showNotes: (dto.pageData?.showNotes as unknown as ContentVO)?.plain,
      speakerLabels: dto.pageData?.speakerLabels,
    });
    const runner = await this.overviewService.overview({
      pageData,
      aiLanguage: dto.aiLanguage,
      aiSecondLanguage: dto.aiSecondLanguage,
      enableBilingual: dto.enableBilingual,
      options: dto.options,
      stream: dto.stream,
    });
    return await runner.generateStream({ streamFormat: 'openai' });
  }

  @Post('updateCompletionBlock')
  @HttpCode(200)
  @ApiOperation({ summary: 'Update tool result' })
  @ApiBody({ type: UpdateCompletionBlockDto })
  @ApiResponse({ status: 200, description: 'Block updated successfully' })
  async updateCompletionBlock(@Body() dto: UpdateCompletionBlockDto) {
    const command = new UpdateCompletionBlockCommand(
      dto.blockId,
      dto.toolResult,
      dto.toolResponse,
      {},
    );
    return await this.commandBus.execute(command);
  }

  @Post(['chat/getSuggestionByWebPage', 'chat/getSuggestionsByWebPage'])
  @HttpCode(200)
  @ApiOperation({
    summary: '生成 Suggestions，给插件用，多语言',
  })
  @ApiBody({ type: GetSuggestionByWebPageDto })
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        suggestions: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async getSuggestionsByWebPage(@Body() dto: GetSuggestionByWebPageDto) {
    const userId = this.getUserId();
    const suggestions = await this.suggestionService.generate({
      userId,
      content: dto.content,
      suggestionCount: 3,
    });
    return { suggestions };
  }
}
