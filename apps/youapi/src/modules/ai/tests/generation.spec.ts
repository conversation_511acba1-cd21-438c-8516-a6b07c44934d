import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { CoreMessage } from 'ai';
import { uuidv7 } from 'uuidv7';
import { DEFAULT_AI_CHAT_MODEL, GenerationStatusEnum } from '@/common/types';
// Import real services for database integration testing
import { DatabaseService } from '@/shared/db/database.service';
import {
  ContentCompletionBlock,
  ToolCompletionBlock,
} from '../domain/models/completion-block.entity';
import { Generation } from '../domain/models/generation.entity';
import { GenerationRepository } from '../repositories/generation.repository';

/**
 * Generation Entity Unit Tests (No Database Dependencies)
 *
 * These tests focus on entity behavior, validation, and data transformations
 * without requiring database connectivity.
 */
describe('Generation Entity Unit Tests', () => {
  const mockChatMessageId = uuidv7();

  describe('Entity Methods and Flag Management', () => {
    it('should handle setBizArgs correctly', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        bizArgs: { original: 'value' },
      });

      expect(generation.bizArgs).toEqual({ original: 'value' });
      expect(generation.isModified).toBe(false);

      // Set new bizArgs (should merge)
      generation.setBizArgs({ new: 'data' });

      expect(generation.bizArgs).toEqual({
        original: 'value',
        new: 'data',
      });
      expect(generation.isModified).toBe(true);
    });

    it('should handle setTraceMetadata correctly', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        traceMetadata: { original: 'metadata' },
      });

      expect(generation.traceMetadata).toEqual({ original: 'metadata' });

      // Set new metadata (should merge)
      generation.setTraceMetadata({ new: 'meta' });

      expect(generation.traceMetadata).toEqual({
        original: 'metadata',
        new: 'meta',
      });
      expect(generation.isModified).toBe(true);
    });

    it('should handle status changes correctly', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      expect(generation.getStatus()).toBe(GenerationStatusEnum.PENDING);
      expect(generation.isModified).toBe(false);

      // Change status
      generation.setStatus(GenerationStatusEnum.GENERATING as any);
      expect(generation.getStatus()).toBe(GenerationStatusEnum.GENERATING as any);
      expect(generation.isModified).toBe(true);

      generation.markAsUnmodified();

      // Set same status again - should not modify
      generation.setStatus(GenerationStatusEnum.GENERATING as any);
      expect(generation.isModified).toBe(false);
    });
  });

  describe('toDO Method Schema Compliance - Unit Tests', () => {
    it('should produce data that matches expected schema structure', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        model: 'gpt-4' as any,
        tools: ['webSearch'] as any[],
        toolChoice: { type: 'function', function: { name: 'webSearch' } } as any,
        modelOptions: { temperature: 0.8, maxTokens: 2048 },
        traceMetadata: { key: 'value' },
        bizArgs: { userId: 'test-user' },
        prediction: ['test', 'prediction'],
        promptName: 'test-prompt',
        promptVersion: 2,
        promptMessages: [{ role: 'user', content: 'test' }] as CoreMessage[],
        traceId: 'trace-123',
      });

      const generationDO = generation.toDO();

      // Verify all required schema fields are present and correctly typed
      expect(typeof generationDO.id).toBe('string');
      expect(generationDO.createdAt).toBeInstanceOf(Date);
      expect(generationDO.updatedAt).toBeInstanceOf(Date);
      expect(generationDO.deletedAt).toBeNull();

      // Verify string fields
      expect(typeof generationDO.model).toBe('string');
      expect(typeof generationDO.promptName).toBe('string');
      expect(typeof generationDO.traceId).toBe('string');
      expect(typeof generationDO.chatMessageId).toBe('string');

      // Verify integer field
      expect(typeof generationDO.promptVersion).toBe('number');
      expect(Number.isInteger(generationDO.promptVersion)).toBe(true);

      // Verify jsonb fields (should be objects/arrays that can be JSON serialized)
      expect(Array.isArray(generationDO.tools)).toBe(true);
      expect(typeof generationDO.modelOptions).toBe('object');
      expect(typeof generationDO.traceMetadata).toBe('object');
      expect(typeof generationDO.bizArgs).toBe('object');
      expect(Array.isArray(generationDO.prediction)).toBe(true);
      expect(Array.isArray(generationDO.promptMessages)).toBe(true);
      expect(Array.isArray(generationDO.generatedMessages)).toBe(true);

      // Verify toolChoice is properly serialized
      expect(typeof generationDO.toolChoice).toBe('string');

      // Verify JSON serialization works
      expect(() => JSON.stringify(generationDO)).not.toThrow();
    });
  });

  describe('getToolBlockByToolId Functionality - Unit Tests', () => {
    it('should find correct ToolCompletionBlock by toolId in blocks array', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      // Create mock blocks with different types and toolIds
      const contentBlock = ContentCompletionBlock.createNew({
        messageId: mockChatMessageId,
        data: 'Some content',
      });

      const toolBlock1 = ToolCompletionBlock.createNew({
        messageId: mockChatMessageId,
        generationId: generation.id,
        toolId: 'tool-id-1',
        toolName: 'webSearch',
        toolArguments: { query: 'test' },
      });

      const toolBlock2 = ToolCompletionBlock.createNew({
        messageId: mockChatMessageId,
        generationId: generation.id,
        toolId: 'tool-id-2',
        toolName: 'calculator',
        toolArguments: { expression: '2+2' },
      });

      // Add blocks to generation
      generation.blocks = [contentBlock, toolBlock1, toolBlock2];

      // Test finding existing tool blocks
      const foundTool1 = generation.getToolBlockByToolId('tool-id-1');
      expect(foundTool1).toBe(toolBlock1);
      expect(foundTool1?.toolName).toBe('webSearch');
      expect(foundTool1?.toolArguments).toEqual({ query: 'test' });

      const foundTool2 = generation.getToolBlockByToolId('tool-id-2');
      expect(foundTool2).toBe(toolBlock2);
      expect(foundTool2?.toolName).toBe('calculator');
      expect(foundTool2?.toolArguments).toEqual({ expression: '2+2' });

      // Test not finding non-existent tool
      const notFound = generation.getToolBlockByToolId('non-existent-tool');
      expect(notFound).toBeNull();
    });

    it('should return null when blocks array is empty', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      const result = generation.getToolBlockByToolId('any-tool-id');
      expect(result).toBeNull();
    });

    it('should return null when only non-tool blocks exist', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      const contentBlock = ContentCompletionBlock.createNew({
        messageId: mockChatMessageId,
        data: 'Content only',
      });

      generation.blocks = [contentBlock];

      const result = generation.getToolBlockByToolId('any-tool-id');
      expect(result).toBeNull();
    });
  });
});

/**
 * Generation Entity and Repository Integration Tests
 *
 * These tests use REAL database connections to verify:
 * 1. Generation creation with modelOption initialization
 * 2. Model/temperature/prompt configuration priority tests
 * 3. Database persistence and roundtrip consistency (REAL DB SAVE/LOAD)
 * 4. toDO method schema compliance
 * 5. getToolBlockByToolId functionality
 * 6. Repository save/find operations with actual database
 */
describe('Generation Entity and Repository Integration Tests', () => {
  let app: TestingModule;
  let repository: GenerationRepository;
  let databaseService: DatabaseService;

  const mockChatMessageId = uuidv7();
  const testGenerationIds: string[] = []; // Track created generations for cleanup

  // Mock ChatPromptClient for testing
  const mockPrompt = {
    name: 'test-prompt',
    version: 2,
    config: {
      model: 'gpt-3.5-turbo' as any,
      temperature: 0.7,
      topP: 0.9,
      frequencyPenalty: 0.1,
      presencePenalty: 0.2,
    },
  };

  beforeAll(async () => {
    console.log('🚀 Setting up Generation Integration Tests with REAL DATABASE');

    // Create a real NestJS testing module with real database connection
    app = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          envFilePath: ['.env.local', '.env'],
          isGlobal: true,
        }),
      ],
      providers: [DatabaseService, GenerationRepository],
    }).compile();

    await app.init(); // Important: This ensures onModuleInit is called

    repository = app.get<GenerationRepository>(GenerationRepository);
    databaseService = app.get<DatabaseService>(DatabaseService);

    // Verify database connection
    console.log('✅ Database service initialized');
    console.log('✅ GenerationRepository initialized');
  });

  afterAll(async () => {
    console.log('🧹 Cleaning up test data...');

    // Clean up all test generations
    if (testGenerationIds.length > 0) {
      try {
        // Import generations table and inArray from drizzle
        const { generations } = await import('@/shared/db/public.schema');
        const { inArray } = await import('drizzle-orm');
        await databaseService.db
          .delete(generations)
          .where(inArray(generations.id, testGenerationIds));
        console.log(`🗑️ Cleaned up ${testGenerationIds.length} test generations`);
      } catch (error) {
        console.warn('⚠️ Error during cleanup:', error);
      }
    }

    // Close database connection to prevent hanging
    try {
      await databaseService.closeConnection();
      console.log('🔌 Database connection closed');
    } catch (error) {
      console.warn('⚠️ Error closing database connection:', error);
    }

    await app?.close();
    console.log('✅ Test cleanup completed');
  });

  describe('1. Generation Creation and modelOption Initialization', () => {
    describe('1.1 Basic modelOption initialization', () => {
      it('should initialize modelOptions correctly with default values', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        // Verify default model
        expect(generation.model).toBe(DEFAULT_AI_CHAT_MODEL);

        // Verify default modelOptions initialization (only fields that are actually set)
        expect(generation.modelOptions).toMatchObject({
          maxTokens: expect.any(Number),
          temperature: 0.5,
          topP: 1,
        });

        // Verify other defaults
        expect(generation.tools).toEqual([]);
        expect(generation.toolChoice).toBe('none');
        expect(generation.bizArgs).toEqual({});
        expect(generation.prediction).toEqual([]);
        expect(generation.traceMetadata).toEqual({});
        expect(generation.promptName).toBe('');
        expect(generation.promptVersion).toBe(1);
        expect(generation.promptMessages).toEqual([]);
        expect(generation.blocks).toEqual([]);
        expect(generation.generatedMessages).toEqual([]);
        expect(generation.getStatus()).toBe(GenerationStatusEnum.PENDING);
      });

      it('should initialize modelOptions correctly with custom values', () => {
        const customModelOptions = {
          temperature: 0.8,
          topP: 0.95,
          frequencyPenalty: 0.5,
          presencePenalty: 0.3,
          maxTokens: 2048,
        };

        const generation = new Generation({
          chatMessageId: mockChatMessageId,
          modelOptions: customModelOptions,
        });

        expect(generation.modelOptions).toEqual({
          maxTokens: 2048,
          temperature: 0.8,
          topP: 0.95,
          frequencyPenalty: 0.5,
          presencePenalty: 0.3,
        });
      });

      it('should filter out model from modelOptions to prevent LanguageModelV1 override', () => {
        const modelOptionsWithModel = {
          model: 'some-language-model-object' as any,
          temperature: 0.6,
          maxTokens: 1024,
        };

        const generation = new Generation({
          chatMessageId: mockChatMessageId,
          model: 'gpt-4' as any,
          modelOptions: modelOptionsWithModel,
        });

        // Model should be set from constructor param, not from modelOptions
        expect(generation.model).toBe('gpt-4');

        // modelOptions should not contain the model field
        expect(generation.modelOptions).not.toHaveProperty('model');
        expect(generation.modelOptions).toMatchObject({
          maxTokens: 1024,
          temperature: 0.6,
          topP: 1,
        });
      });
    });

    describe('1.2 Model setting verification', () => {
      it('should set model correctly and update flags', async () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
          model: 'gpt-3.5-turbo' as any,
        });

        expect(generation.model).toBe('gpt-3.5-turbo');
        expect(generation.isModified).toBe(false);

        const originalUpdatedAt = generation.updatedAt;

        // Add small delay to ensure timestamp difference
        await new Promise((resolve) => setTimeout(resolve, 10));

        // Set new model
        generation.setModel('claude-3' as any);

        expect(generation.model).toBe('claude-3');
        expect(generation.isModified).toBe(true);
        expect(generation.updatedAt).toBeInstanceOf(Date);
        expect(generation.updatedAt.getTime()).toBeGreaterThanOrEqual(originalUpdatedAt.getTime());
      });
    });

    describe('1.3 Temperature setting verification', () => {
      it('should set temperature correctly in modelOptions', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
          modelOptions: {
            temperature: 0.9,
          },
        });

        expect(generation.modelOptions.temperature).toBe(0.9);
      });

      it('should default to 0.5 when temperature is not provided', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        expect(generation.modelOptions.temperature).toBe(0.5);
      });
    });

    describe('1.4 Prompt setting verification', () => {
      it('should set prompt configuration correctly', async () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        expect(generation.isModified).toBe(false);
        const originalUpdatedAt = generation.updatedAt;

        // Add small delay to ensure timestamp difference
        await new Promise((resolve) => setTimeout(resolve, 10));

        // Set prompt
        generation.setPrompt(mockPrompt as any);

        expect(generation.promptName).toBe('test-prompt');
        expect(generation.promptVersion).toBe(2);
        expect(generation.model).toBe(DEFAULT_AI_CHAT_MODEL); // setPrompt should NOT change the model
        expect(generation.modelOptions.temperature).toBe(0.7);
        expect(generation.modelOptions.topP).toBe(0.9);
        expect(generation.modelOptions.frequencyPenalty).toBe(0.1);
        expect(generation.modelOptions.presencePenalty).toBe(0.2);
        expect(generation.isModified).toBe(true);
        expect(generation.updatedAt.getTime()).toBeGreaterThanOrEqual(originalUpdatedAt.getTime());
      });
    });

    describe('1.5 Model parameter priority over prompt setting', () => {
      it('should prioritize modelOptions.model over prompt.config.model', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        // Set prompt with explicit model override in modelOptions
        generation.setPrompt(mockPrompt as any, {
          model: 'claude-3' as any,
        });

        // setPrompt should NOT change the model, even if modelOptions contains one
        expect(generation.model).toBe(DEFAULT_AI_CHAT_MODEL);
        expect(generation.promptName).toBe('test-prompt');
        expect(generation.promptVersion).toBe(2);
      });
    });

    describe('1.6 Temperature parameter priority over prompt setting', () => {
      it('should prioritize modelOptions.temperature over prompt.config.temperature', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        // Set prompt with explicit temperature override in modelOptions
        generation.setPrompt(mockPrompt as any, {
          temperature: 0.2,
        });

        // modelOptions.temperature should take priority over prompt.config.temperature
        expect(generation.modelOptions.temperature).toBe(0.2);
        expect(generation.model).toBe(DEFAULT_AI_CHAT_MODEL); // setPrompt should NOT change the model
        expect(generation.promptName).toBe('test-prompt');
      });

      it('should use prompt.config.temperature when modelOptions.temperature is not provided', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        generation.setPrompt(mockPrompt as any);

        expect(generation.modelOptions.temperature).toBe(0.7); // From prompt config
      });
    });

    describe('1.7 Provider parameter initialization and priority', () => {
      it('should initialize Generation with provider from modelOptions in constructor', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
          modelOptions: {
            provider: 'openai' as any,
            temperature: 0.8,
          },
        });

        expect(generation.modelOptions.provider).toBe('openai');
        expect(generation.modelOptions.temperature).toBe(0.8);
      });

      it('should not include provider in modelOptions when not specified', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
          modelOptions: {
            temperature: 0.7,
          },
        });

        expect(generation.modelOptions).not.toHaveProperty('provider');
        expect(generation.modelOptions.temperature).toBe(0.7);
      });

      it('should prioritize modelOptions.provider over prompt.config.provider in setPrompt', () => {
        const promptWithProvider = {
          name: 'test-prompt',
          version: 2,
          config: {
            model: 'gpt-3.5-turbo' as any,
            temperature: 0.7,
            provider: 'anthropic' as any,
          },
        };

        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        // Set prompt with explicit provider override in modelOptions
        generation.setPrompt(promptWithProvider as any, {
          provider: 'openai' as any,
          temperature: 0.9,
        });

        // modelOptions.provider should take priority over prompt.config.provider
        expect(generation.modelOptions.provider).toBe('openai');
        expect(generation.modelOptions.temperature).toBe(0.9);
      });

      it('should use prompt.config.provider when modelOptions.provider is not provided', () => {
        const promptWithProvider = {
          name: 'test-prompt',
          version: 2,
          config: {
            model: 'gpt-4' as any,
            temperature: 0.6,
            provider: 'anthropic' as any,
          },
        };

        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        generation.setPrompt(promptWithProvider as any);

        // Should use provider from prompt config
        expect(generation.modelOptions.provider).toBe('anthropic');
        expect(generation.modelOptions.temperature).toBe(0.6);
      });

      it('should not include provider in modelOptions when neither constructor nor prompt provides it', () => {
        const generation = new Generation({
          chatMessageId: mockChatMessageId,
        });

        generation.setPrompt(mockPrompt as any); // mockPrompt doesn't have provider

        expect(generation.modelOptions).not.toHaveProperty('provider');
        expect(generation.modelOptions.temperature).toBe(0.7);
      });

      it('should handle multiple providers correctly across different initialization methods', () => {
        // Test 1: Constructor with provider
        const gen1 = new Generation({
          chatMessageId: mockChatMessageId,
          modelOptions: {
            provider: 'openai' as any,
          },
        });
        expect(gen1.modelOptions.provider).toBe('openai');

        // Test 2: setPrompt with provider in prompt config
        const gen2 = new Generation({
          chatMessageId: mockChatMessageId,
        });
        gen2.setPrompt({
          name: 'test',
          version: 1,
          config: { provider: 'anthropic' as any },
        } as any);
        expect(gen2.modelOptions.provider).toBe('anthropic');

        // Test 3: setPrompt with provider override in modelOptions
        const gen3 = new Generation({
          chatMessageId: mockChatMessageId,
        });
        gen3.setPrompt(
          {
            name: 'test',
            version: 1,
            config: { provider: 'anthropic' as any },
          } as any,
          { provider: 'openai' as any },
        );
        expect(gen3.modelOptions.provider).toBe('openai');
      });
    });
  });

  describe('2. Database Persistence and Roundtrip Consistency - REAL DATABASE', () => {
    it('should save new Generation to database and maintain data consistency after retrieval', async () => {
      console.log('💾 Testing REAL database save and roundtrip...');

      // Create new Generation
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        model: 'gpt-4' as any,
        tools: ['webSearch', 'calculator'] as any[],
        toolChoice: 'auto' as any,
        bizArgs: { userId: 'user-123', sessionId: 'session-456' },
        modelOptions: { temperature: 0.7, maxTokens: 2048 },
        traceMetadata: { traceType: 'chat', version: '1.0' },
        promptName: 'chat-prompt',
        promptVersion: 3,
        promptMessages: [{ role: 'user', content: 'Hello, how are you?' }] as CoreMessage[],
        prediction: ['greeting', 'casual'],
        traceId: 'trace-789',
      });

      // Track for cleanup
      testGenerationIds.push(generation.id);

      // Verify initial state
      expect(generation.isNew).toBe(true);
      expect(generation.isModified).toBe(false);
      expect(generation.model).toBe('gpt-4');

      // REAL DATABASE SAVE
      console.log('💾 Saving to database...');
      const savedGeneration = await repository.save(generation);

      // Verify flags after save
      expect(savedGeneration.isNew).toBe(false);
      expect(savedGeneration.isModified).toBe(false);
      expect(savedGeneration).toBe(generation); // Same instance

      // REAL DATABASE RETRIEVAL
      console.log('🔍 Retrieving from database...');
      const retrievedGenerations = await repository.findByChatMessageId(mockChatMessageId);

      expect(retrievedGenerations.length).toBeGreaterThan(0);
      const retrievedGeneration = retrievedGenerations.find((g) => g.id === generation.id);
      expect(retrievedGeneration).toBeDefined();

      // Verify complete data consistency after database roundtrip
      expect(retrievedGeneration!.id).toBe(generation.id);
      expect(retrievedGeneration!.model).toBe('gpt-4');
      expect(retrievedGeneration!.tools).toEqual(['webSearch', 'calculator']);
      expect(retrievedGeneration!.toolChoice).toBe('auto');
      expect(retrievedGeneration!.bizArgs).toEqual({
        userId: 'user-123',
        sessionId: 'session-456',
      });
      expect(retrievedGeneration!.modelOptions).toEqual(generation.modelOptions);
      expect(retrievedGeneration!.traceMetadata).toEqual({ traceType: 'chat', version: '1.0' });
      expect(retrievedGeneration!.promptName).toBe('chat-prompt');
      expect(retrievedGeneration!.promptVersion).toBe(3);
      expect(retrievedGeneration!.promptMessages).toEqual([
        { role: 'user', content: 'Hello, how are you?' },
      ]);
      expect(retrievedGeneration!.prediction).toEqual(['greeting', 'casual']);
      expect(retrievedGeneration!.traceId).toBe('trace-789');
      expect(retrievedGeneration!.chatMessageId).toBe(mockChatMessageId);
      expect(retrievedGeneration!.isNew).toBe(false); // Should be false after DB retrieval
      expect(retrievedGeneration!.isModified).toBe(false);

      console.log('✅ Database roundtrip test passed!');
    });

    it('should persist modifications to database and maintain consistency after retrieval', async () => {
      console.log('🔄 Testing database update and roundtrip...');

      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        model: 'gpt-3.5-turbo' as any,
        bizArgs: { original: 'value' },
        traceMetadata: { original: 'metadata' },
      });

      // Track for cleanup
      testGenerationIds.push(generation.id);

      // Save initial version
      await repository.save(generation);

      // Modify various fields
      const newMessageId = uuidv7(); // Generate valid UUID for new message ID
      generation.setModel('claude-3' as any);
      generation.setBizArgs({ new: 'bizArgs', additional: 'data' });
      generation.setTraceMetadata({ new: 'traceMetadata' });
      generation.setTraceId('new-trace-id');
      generation.setChatMessageId(newMessageId);
      generation.setStatus(GenerationStatusEnum.GENERATING as any);

      expect(generation.isModified).toBe(true);

      // Save modifications to database
      console.log('💾 Saving modifications to database...');
      await repository.save(generation);

      expect(generation.isModified).toBe(false); // Should be reset after save

      // Retrieve from database
      console.log('🔍 Retrieving modified data from database...');
      const retrievedGenerations = await repository.findByChatMessageId(newMessageId);
      const retrievedGeneration = retrievedGenerations.find((g) => g.id === generation.id);

      expect(retrievedGeneration).toBeDefined();

      // Verify all modifications persisted to database
      expect(retrievedGeneration!.model).toBe('claude-3');
      expect(retrievedGeneration!.bizArgs).toEqual({
        original: 'value',
        new: 'bizArgs',
        additional: 'data',
      });
      expect(retrievedGeneration!.traceMetadata).toEqual({
        original: 'metadata',
        new: 'traceMetadata',
      });
      expect(retrievedGeneration!.traceId).toBe('new-trace-id');
      expect(retrievedGeneration!.chatMessageId).toBe(newMessageId);
      expect(retrievedGeneration!.getStatus()).toBe(GenerationStatusEnum.GENERATING as any);

      console.log('✅ Database update test passed!');
    });
  });

  describe('3. toDO Method Schema Compliance', () => {
    it('should produce data that matches public.schema generations table definition', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        model: 'gpt-4' as any,
        tools: ['webSearch'] as any[],
        toolChoice: { type: 'function', function: { name: 'webSearch' } } as any,
        modelOptions: { temperature: 0.8, maxTokens: 2048 },
        traceMetadata: { key: 'value' },
        bizArgs: { userId: 'test-user' },
        prediction: ['test', 'prediction'],
        promptName: 'test-prompt',
        promptVersion: 2,
        promptMessages: [{ role: 'user', content: 'test' }] as CoreMessage[],
        traceId: 'trace-123',
      });

      const generationDO = generation.toDO();

      // Verify all required schema fields are present and correctly typed
      expect(typeof generationDO.id).toBe('string');
      expect(generationDO.createdAt).toBeInstanceOf(Date);
      expect(generationDO.updatedAt).toBeInstanceOf(Date);
      expect(generationDO.deletedAt).toBeNull();

      // Verify enum field
      expect(Object.values(GenerationStatusEnum)).toContain(generationDO.status);

      // Verify string fields
      expect(typeof generationDO.model).toBe('string');
      expect(typeof generationDO.promptName).toBe('string');
      expect(typeof generationDO.traceId).toBe('string');
      expect(typeof generationDO.chatMessageId).toBe('string');

      // Verify integer field
      expect(typeof generationDO.promptVersion).toBe('number');
      expect(Number.isInteger(generationDO.promptVersion)).toBe(true);

      // Verify jsonb fields (should be objects/arrays that can be JSON serialized)
      expect(Array.isArray(generationDO.tools)).toBe(true);
      expect(typeof generationDO.modelOptions).toBe('object');
      expect(typeof generationDO.traceMetadata).toBe('object');
      expect(typeof generationDO.bizArgs).toBe('object');
      expect(Array.isArray(generationDO.prediction)).toBe(true);
      expect(Array.isArray(generationDO.promptMessages)).toBe(true);
      expect(Array.isArray(generationDO.generatedMessages)).toBe(true);

      // Verify toolChoice is properly serialized
      expect(typeof generationDO.toolChoice).toBe('string');

      // Verify JSON serialization works
      expect(() => JSON.stringify(generationDO)).not.toThrow();
    });

    it('should handle complex toolChoice serialization correctly', () => {
      const complexToolChoice = {
        type: 'function',
        function: { name: 'calculator', strict: true },
      };

      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        toolChoice: complexToolChoice as any,
      });

      const generationDO = generation.toDO();

      expect(typeof generationDO.toolChoice).toBe('string');
      expect(JSON.parse(generationDO.toolChoice)).toEqual(complexToolChoice);
    });

    it('should handle string toolChoice without additional serialization', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        toolChoice: 'auto' as any,
      });

      const generationDO = generation.toDO();

      expect(generationDO.toolChoice).toBe('auto');
    });
  });

  describe('4. getToolBlockByToolId Functionality', () => {
    it('should find correct ToolCompletionBlock by toolId in blocks array', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      // Create mock blocks with different types and toolIds
      const contentBlock = ContentCompletionBlock.createNew({
        messageId: mockChatMessageId,
        data: 'Some content',
      });

      const toolBlock1 = ToolCompletionBlock.createNew({
        messageId: mockChatMessageId,
        generationId: generation.id,
        toolId: 'tool-id-1',
        toolName: 'webSearch',
        toolArguments: { query: 'test' },
      });

      const toolBlock2 = ToolCompletionBlock.createNew({
        messageId: mockChatMessageId,
        generationId: generation.id,
        toolId: 'tool-id-2',
        toolName: 'calculator',
        toolArguments: { expression: '2+2' },
      });

      // Add blocks to generation
      generation.blocks = [contentBlock, toolBlock1, toolBlock2];

      // Test finding existing tool blocks
      const foundTool1 = generation.getToolBlockByToolId('tool-id-1');
      expect(foundTool1).toBe(toolBlock1);
      expect(foundTool1?.toolName).toBe('webSearch');
      expect(foundTool1?.toolArguments).toEqual({ query: 'test' });

      const foundTool2 = generation.getToolBlockByToolId('tool-id-2');
      expect(foundTool2).toBe(toolBlock2);
      expect(foundTool2?.toolName).toBe('calculator');
      expect(foundTool2?.toolArguments).toEqual({ expression: '2+2' });

      // Test not finding non-existent tool
      const notFound = generation.getToolBlockByToolId('non-existent-tool');
      expect(notFound).toBeNull();
    });

    it('should return null when blocks array is empty', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      const result = generation.getToolBlockByToolId('any-tool-id');
      expect(result).toBeNull();
    });

    it('should return null when only non-tool blocks exist', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      const contentBlock = ContentCompletionBlock.createNew({
        messageId: mockChatMessageId,
        data: 'Content only',
      });

      generation.blocks = [contentBlock];

      const result = generation.getToolBlockByToolId('any-tool-id');
      expect(result).toBeNull();
    });
  });

  describe('5. Generation Repository REAL Database Operations', () => {
    it('should handle new generation save correctly with REAL database', async () => {
      console.log('💾 Testing repository save with new generation...');

      const newGeneration = new Generation({
        chatMessageId: mockChatMessageId,
        model: 'gpt-4' as any,
      });

      // Track for cleanup
      testGenerationIds.push(newGeneration.id);

      expect(newGeneration.isNew).toBe(true);
      expect(newGeneration.isModified).toBe(false);

      const result = await repository.save(newGeneration);

      // Should mark as existing immediately to prevent race conditions
      expect(newGeneration.isNew).toBe(false);
      expect(newGeneration.isModified).toBe(false);
      expect(result).toBe(newGeneration);

      // Verify it's actually in the database
      const retrieved = await repository.findByChatMessageId(mockChatMessageId);
      const foundGeneration = retrieved.find((g) => g.id === newGeneration.id);
      expect(foundGeneration).toBeDefined();
      expect(foundGeneration!.model).toBe('gpt-4');

      console.log('✅ Repository save test passed!');
    });

    it('should handle modified generation save correctly with REAL database', async () => {
      console.log('🔄 Testing repository save with modified generation...');

      // First create and save a generation
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        model: 'gpt-3.5-turbo' as any,
      });

      // Track for cleanup
      testGenerationIds.push(generation.id);

      await repository.save(generation);
      expect(generation.isNew).toBe(false);
      expect(generation.isModified).toBe(false);

      // Modify the generation
      generation.setModel('gpt-4' as any);
      expect(generation.isModified).toBe(true);

      const result = await repository.save(generation);

      // Should mark as unmodified after save
      expect(generation.isNew).toBe(false);
      expect(generation.isModified).toBe(false);
      expect(result).toBe(generation);

      // Verify the modification persisted to database
      const retrieved = await repository.findByChatMessageId(mockChatMessageId);
      const foundGeneration = retrieved.find((g) => g.id === generation.id);
      expect(foundGeneration).toBeDefined();
      expect(foundGeneration!.model).toBe('gpt-4'); // Should be updated

      console.log('✅ Repository update test passed!');
    });

    it('should skip database operation for unchanged generation', async () => {
      console.log('⏭️ Testing repository skip for unchanged generation...');

      // Create and save generation
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
        model: 'gpt-4' as any,
      });

      // Track for cleanup
      testGenerationIds.push(generation.id);

      await repository.save(generation);
      expect(generation.isNew).toBe(false);
      expect(generation.isModified).toBe(false);

      // Save again without changes - should be no-op
      const result = await repository.save(generation);

      expect(result).toBe(generation);
      expect(generation.isNew).toBe(false);
      expect(generation.isModified).toBe(false);

      // Should still be in database unchanged
      const retrieved = await repository.findByChatMessageId(mockChatMessageId);
      const foundGeneration = retrieved.find((g) => g.id === generation.id);
      expect(foundGeneration).toBeDefined();

      console.log('✅ Repository skip test passed!');
    });

    it('should retrieve and reconstruct generations by chatMessageId from REAL database', async () => {
      console.log('🔍 Testing repository findByChatMessageId...');

      const testMessageId = uuidv7();

      // Create multiple generations with same chatMessageId
      const generation1 = new Generation({
        chatMessageId: testMessageId,
        model: 'gpt-4' as any,
        tools: ['webSearch'] as any[],
        toolChoice: 'auto' as any,
        modelOptions: { temperature: 0.7 },
        traceMetadata: { type: 'chat' },
        bizArgs: { userId: 'user-1' },
        prediction: ['response'],
        promptName: 'chat-prompt',
        promptVersion: 1,
        promptMessages: [{ role: 'user', content: 'Hello' }] as CoreMessage[],
        // generatedMessages: [{ role: 'assistant', content: 'Hi there!' }] as CoreMessage[], // Not in constructor
        traceId: 'trace-1',
      });

      const generation2 = new Generation({
        chatMessageId: testMessageId,
        model: 'gpt-3.5-turbo' as any,
        tools: [] as any[],
        toolChoice: 'none' as any,
        modelOptions: { temperature: 0.5 },
        traceMetadata: {},
        bizArgs: {},
        prediction: [],
        promptName: 'follow-up',
        promptVersion: 2,
        promptMessages: [],
        // generatedMessages: [], // Not in constructor
        traceId: 'trace-2',
      });

      // Track for cleanup
      testGenerationIds.push(generation1.id, generation2.id);

      // Save both to database
      await repository.save(generation1);
      await repository.save(generation2);

      // Retrieve from database
      const result = await repository.findByChatMessageId(testMessageId);

      expect(result.length).toBeGreaterThanOrEqual(2);

      // Find our specific generations
      const found1 = result.find((g) => g.id === generation1.id);
      const found2 = result.find((g) => g.id === generation2.id);

      expect(found1).toBeDefined();
      expect(found2).toBeDefined();

      // Verify data integrity
      expect(found1!.model).toBe('gpt-4');
      expect(found1!.tools).toEqual(['webSearch']);
      expect(found1!.getStatus()).toBe(GenerationStatusEnum.PENDING);
      expect(found1!.chatMessageId).toBe(testMessageId);
      expect(found1!.isNew).toBe(false);
      expect(found1!.isModified).toBe(false);

      expect(found2!.model).toBe('gpt-3.5-turbo');
      expect(found2!.tools).toEqual([]);
      expect(found2!.getStatus()).toBe(GenerationStatusEnum.PENDING);
      expect(found2!.chatMessageId).toBe(testMessageId);
      expect(found2!.isNew).toBe(false);
      expect(found2!.isModified).toBe(false);

      console.log('✅ Repository findByChatMessageId test passed!');
    });

    it('should return empty array when no generations found', async () => {
      // Use a valid UUID format for non-existent message
      const nonExistentMessageId = uuidv7();
      const result = await repository.findByChatMessageId(nonExistentMessageId);
      expect(result).toEqual([]);
    });
  });

  describe('Additional Generation Methods', () => {
    it('should handle isNew and isModified flags correctly', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      expect(generation.isNew).toBe(true);
      expect(generation.isModified).toBe(false);

      // Modify generation
      generation.setModel('gpt-4' as any);
      expect(generation.isNew).toBe(true);
      expect(generation.isModified).toBe(true);

      // Mark as existing
      generation.markAsExisting();
      expect(generation.isNew).toBe(false);
      expect(generation.isModified).toBe(false);

      // Modify again
      generation.setBizArgs({ key: 'value' });
      expect(generation.isNew).toBe(false);
      expect(generation.isModified).toBe(true);

      // Mark as unmodified
      generation.markAsUnmodified();
      expect(generation.isNew).toBe(false);
      expect(generation.isModified).toBe(false);
    });

    it('should handle status changes correctly', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      expect(generation.getStatus()).toBe(GenerationStatusEnum.PENDING);
      expect(generation.isModified).toBe(false);

      // Change status
      generation.setStatus(GenerationStatusEnum.GENERATING as any);
      expect(generation.getStatus()).toBe(GenerationStatusEnum.GENERATING as any);
      expect(generation.isModified).toBe(true);

      generation.markAsUnmodified();

      // Set same status again - should not modify
      generation.setStatus(GenerationStatusEnum.GENERATING as any);
      expect(generation.isModified).toBe(false);
    });

    it('should handle prompt message updates with deep comparison', () => {
      const generation = new Generation({
        chatMessageId: mockChatMessageId,
      });

      const initialMessages: CoreMessage[] = [{ role: 'user', content: 'Hello' }];

      generation.updatePromptMessages(initialMessages);
      expect(generation.promptMessages).toEqual(initialMessages);
      expect(generation.isModified).toBe(true);

      generation.markAsUnmodified();

      // Update with same messages - should not modify due to deep comparison
      generation.updatePromptMessages([...initialMessages]);
      expect(generation.isModified).toBe(false);

      // Update with different messages - should modify
      const newMessages: CoreMessage[] = [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' },
      ];

      generation.updatePromptMessages(newMessages);
      expect(generation.isModified).toBe(true);
      expect(generation.promptMessages).toEqual(newMessages);
    });
  });
});
