import { Injectable } from '@nestjs/common';
import { Generation } from '../../domain/models/generation.entity';
import { PromptService } from '../../prompt/index.service';
import { TextRunner } from '../text';

@Injectable()
export class TextRunnerService {
  constructor(private readonly promptService: PromptService) {}

  getSuggestionRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('suggestion-prompt', variables);
  }

  getGenerateTitleRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('generate-title-prompt', variables);
  }

  getThoughtAutoGenerateTitleRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('thought-auto-generate-title-prompt', variables);
  }

  getExplainRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('simple-explain-text-prompt', variables);
  }

  getSelfDescByFeedsRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('self-desc-by-feeds-prompt', variables);
  }

  getGeneratePunctuationRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('subtitle-punctuation-prompt', variables);
  }

  getDetectSpeakersByTranscriptRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('detect-speakers-by-transcript-prompt', variables);
  }

  getAudioOverviewRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('audio-overview-prompt', variables);
  }

  getAskRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('ai-ask-chat-prompt', variables);
  }

  getQueryAnalyzeRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('query-analyze-prompt', variables);
  }

  getAnalyzePromptRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('analyze-prompt', variables);
  }

  getInstantApplyRunner(variables: Record<string, any>, prediction: string[]): TextRunner {
    const { prompt, promptMessages } = this.promptService.getPromptAndMessages(
      'instant-apply-prompt',
      variables,
    );
    const generation = Generation.createFromPrompt({
      prompt,
      promptMessages,
      prediction,
    });
    const runner = new TextRunner();
    runner.addGeneration(generation);
    return runner;
  }

  getDetectLanguageRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('detect-language', variables);
  }

  getSVGDiagramRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('svg-generator-prompt', variables);
  }

  getMindmapRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('generate-mindmap', variables);
  }

  getEntityNameRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('extract-entity-names', variables);
  }

  getSimpleSummaryOfAContentRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('simple-summary-of-a-content', variables);
  }

  getTranslateTranscriptRunner(variables: Record<string, any>): TextRunner {
    return TextRunner.fromPrompt('translate-transcript-prompt', variables);
  }
}
