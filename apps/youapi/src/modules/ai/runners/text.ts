import { Logger } from '@nestjs/common';
import { GenerateTextResult, generateText, streamText, TextStreamPart, Tool } from 'ai';
import OpenAI from 'openai';
import { Observable } from 'rxjs';
import { CompletionStreamChunk, StreamChunkUnion } from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { CheckCredits } from '@/modules/iam/decorators/check-credits.decorator';
import { Generation } from '../domain/models/generation.entity';
import { PromptService } from '../prompt/index.service';
import { streamToObservable, toOpenaiCompatibleObservable } from '../utils/toObservable';
import { BaseRunner, type GenerateOptions, type StreamGenerateOptions } from './base';

/**
 * TextRunner wraps the Generation domain model with execution logic
 * Tracing is handled by decorators and subscriptions
 */
export class TextRunner extends BaseRunner {
  protected readonly logger = new Logger(TextRunner.name);

  @CheckCredits()
  public async generateOnce(options?: GenerateOptions): Promise<GenerateTextResult<any, any>> {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is required');
    }

    const model = this.getModel(useCache);
    const traceName = this.promptName || 'text-generation';
    const traceNode = await this.startTrace(traceName, { ...options, model });

    // Start generation tracking - use prompt name if available
    try {
      const result = await generateText(this.assembleApiParameters(model));

      // End generation with success
      await this.reportTextUsage(result);
      traceNode.end({
        output: result.text,
        metadata: {
          finishReason: result.finishReason,
          success: true,
        },
      });

      this.processTextResult(result);

      return result;
    } catch (error) {
      traceNode.end({
        metadata: {
          success: false,
          error: error.message,
          errorStack: error.stack,
        },
      });
      throw error;
    }
  }

  public async generateStream(
    options: StreamGenerateOptions & { streamFormat: 'openai' },
  ): Promise<Observable<OpenAI.ChatCompletionChunk>>;
  public async generateStream(
    options: StreamGenerateOptions & { streamFormat: 'completion-stream' },
  ): Promise<Observable<CompletionStreamChunk<StreamChunkUnion>>>;
  public async generateStream(
    options: StreamGenerateOptions & { streamFormat: 'ai-sdk' },
  ): Promise<Observable<TextStreamPart<Record<string, Tool>>>>;
  public async generateStream(
    options?: StreamGenerateOptions,
  ): Promise<Observable<TextStreamPart<Record<string, Tool>>>>;

  @CheckCredits()
  public async generateStream(
    options?: StreamGenerateOptions,
  ): Promise<
    | Observable<OpenAI.ChatCompletionChunk>
    | Observable<CompletionStreamChunk<StreamChunkUnion>>
    | Observable<TextStreamPart<Record<string, Tool>>>
  > {
    const { useCache = true, streamFormat = 'ai-sdk' } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is required');
    }

    const model = this.getModel(useCache);
    const traceName = this.promptName || 'stream-text-generation';
    const traceNode = await this.startTrace(traceName, { ...options, model });

    let ttft = false;
    const autoToolCall = true;
    const generator = streamText({
      ...this.assembleApiParameters(model, autoToolCall),
      onChunk: () => {
        if (ttft) return;
        ttft = true;
        traceNode.update({
          completionStartTime: new Date(),
        });
      },
      onFinish: async (result) => {
        const { finishReason } = result;
        await this.reportTextUsage(result);
        traceNode.end({
          metadata: {
            success: true,
            finishReason,
          },
        });
      },
      onError: ({ error }) => {
        traceNode.end({
          metadata: {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
          },
        });
      },
    });

    const observable = streamToObservable(generator);
    const completionStreamSubject = this.processTextStream(observable, autoToolCall);

    if (streamFormat === 'openai') {
      return toOpenaiCompatibleObservable(observable);
    } else if (streamFormat === 'completion-stream') {
      return completionStreamSubject.asObservable();
    } else {
      return observable;
    }
  }

  static fromPrompt(promptName: string, variables: Record<string, any>) {
    const promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    const { prompt, promptMessages } = promptService.getPromptAndMessages(promptName, variables);
    const generation = Generation.createFromPrompt({
      prompt,
      promptMessages,
    });
    const runner = new TextRunner();
    runner.addGeneration(generation);
    return runner;
  }
}
