import { Logger } from '@nestjs/common';
import type { GenerateObjectResult } from 'ai';
import { generateObject, streamObject } from 'ai';
import z, { ZodRawShape, ZodSchema } from 'zod';
import { ApplicationContext } from '@/common/utils/application-context';
import { Generation } from '../domain/models/generation.entity';
import { PromptService } from '../prompt/index.service';
import { streamToObservable } from '../utils/toObservable';
import type { GenerateOptions } from './base';
import { BaseRunner } from './base';

/**
 * 生成对象（单次）以及生成对象（多次）
 */
export class ObjectRunner extends BaseRunner {
  protected readonly logger = new Logger(ObjectRunner.name);
  protected schema: ZodSchema;

  setSchema(schema: ZodSchema) {
    this.schema = schema;
    return this;
  }

  public async generateOnce<T>(options?: GenerateOptions): Promise<GenerateObjectResult<T>> {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is not set');
    }
    if (!this.schema) {
      throw new Error('Schema is not set');
    }

    const model = this.getModel(useCache);
    const traceNode = await this.startTrace(this.promptName || 'object-generation', {
      ...options,
      model,
    });

    try {
      const params = this.assembleApiParameters(model);
      const result = await generateObject({
        ...params,
        output: 'object',
        schema: this.schema,
      });

      await this.reportTextUsage(result);
      // Update the traceNode directly instead of using updateGeneration
      traceNode.end({
        output: result.object,
        metadata: {
          finishReason: result.finishReason,
          success: true,
        },
      });

      return result;
    } catch (error) {
      // Update the traceNode directly for error case
      traceNode.end({
        metadata: {
          success: false,
          error: error.message,
          errorStack: error.stack,
        },
        level: 'ERROR',
      });
      throw error;
    }
  }

  public async generateStream(options?: GenerateOptions) {
    const { useCache = true } = options || {};
    if (!this.currentGeneration) {
      throw new Error('Generation is not set');
    }
    if (!this.schema) {
      throw new Error('Schema is not set');
    }

    const model = this.getModel(useCache);
    const traceNode = await this.startTrace(this.promptName || 'stream-object-generation', options);

    const params = this.assembleApiParameters(model);

    const generator = await streamObject({
      ...params,
      output: 'object',
      schema: this.schema,
      onFinish: async (result) => {
        const { object } = result;
        await this.reportTextUsage(result);
        traceNode.end({
          output: object,
          metadata: {
            success: true,
          },
        });
      },
      onError: ({ error }) => {
        traceNode.end({
          metadata: {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
          },
        });
      },
    });

    const streamObservable = streamToObservable(generator);
    // Wrap the observable to preserve trace context throughout the stream
    return this.traceService.wrapObservable(streamObservable);
  }

  static fromPrompt(
    promptName: string,
    variables: Record<string, any>,
    schema?: ZodRawShape | ZodSchema, // JSON Schema object
  ) {
    const promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    const { prompt, promptMessages } = promptService.getPromptAndMessages(promptName, variables);
    const generation = Generation.createFromPrompt({
      prompt,
      promptMessages,
    });
    const runner = new ObjectRunner();
    if (schema) {
      runner.setSchema(schema instanceof ZodSchema ? schema : z.object(schema));
    } else if ((prompt.config as { response_format?: ZodRawShape }).response_format) {
      runner.setSchema(
        z.object((prompt.config as { response_format?: ZodRawShape }).response_format),
      );
    }
    runner.addGeneration(generation);
    return runner;
  }
}
