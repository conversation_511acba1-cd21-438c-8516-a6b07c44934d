import { LanguageModelV1, SpeechModelV1 } from '@ai-sdk/provider';
import { Logger } from '@nestjs/common';
import { CommandBus, EventBus, QueryBus } from '@nestjs/cqrs';
import {
  CoreMessage,
  EmbeddingModelUsage,
  GenerateTextResult,
  generateText,
  LanguageModelUsage,
  ProviderMetadata,
  TextStreamPart,
  ToolCall,
  ToolExecutionError,
  ToolResultUnion,
} from 'ai';
import { LangfuseGenerationClient, LangfuseSpanClient } from 'langfuse-core';
import { ImagesResponse } from 'openai/resources/images.js';
import { concatMap, interval, Observable, Subject, takeUntil } from 'rxjs';
import { RestError } from '@/common/errors';
import { LangfuseTraceService } from '@/common/services/langfuse-trace.service';
import {
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  GenerationStatusEnum,
  LLMProviders,
  LLMs,
  MODEL_DEFINITION,
  StreamChunkUnion,
  ToolCallResult,
  ToolCallResultWithToolCallId,
  ToolNames,
} from '@/common/types';
import { SafeParse } from '@/common/utils';
import { ApplicationContext } from '@/common/utils/application-context';
import { TokensUsedEvent } from '@/modules/iam/domain/credit/events/tokens-used.event';
import {
  LanguageModelTransactionCategory,
  TokensUsage,
} from '@/modules/iam/domain/credit/models/credit-account.types';
import {
  CompletionBlock,
  ContentCompletionBlock,
  ReasoningCompletionBlock,
  ToolCompletionBlock,
} from '../domain/models/completion-block.entity';
import { Generation } from '../domain/models/generation.entity';
import { ModelProviderService } from '../providers/index.service';
import { CompletionBlockRepository } from '../repositories/completion-block.repository';
import { GenerationRepository } from '../repositories/generation.repository';
import { ToolCallService } from '../tools/tool-call.service';

export interface OpenAIProviderMetadata {
  reasoningTokens?: number;
  acceptedPredictionTokens?: number;
  rejectedPredictionTokens?: number;
  cachedPromptTokens?: number;
}

export interface AnthropicProviderMetadata {
  cacheCreationInputTokens?: number;
  cacheReadInputTokens?: number;
}

export interface MinimaxProviderMetadata {
  audio_length: number;
  audio_sample_rate: number;
  audio_size: number;
  bitrate: number;
  audio_format: string;
  audio_channel: number;
  invisible_character_ratio: number;
  usage_characters: number;
}

export interface GenerateOptions {
  useCache?: boolean;
  inheritTrace?: boolean;
}
export interface StreamGenerateOptions extends GenerateOptions {
  streamFormat?: 'openai' | 'ai-sdk' | 'completion-stream';
}

export class BaseRunner {
  public currentTrace: LangfuseGenerationClient;
  protected readonly logger = new Logger(BaseRunner.name);
  protected generations: Generation[] = [];
  public currentGeneration: Generation;
  protected traceService: LangfuseTraceService;
  protected eventBus: EventBus;
  protected queryBus: QueryBus;
  protected commandBus: CommandBus;

  constructor() {
    this.traceService = ApplicationContext.getProvider<LangfuseTraceService>(LangfuseTraceService);
    this.commandBus = ApplicationContext.getProvider<CommandBus>(CommandBus);
    this.queryBus = ApplicationContext.getProvider<QueryBus>(QueryBus);
    this.eventBus = ApplicationContext.getProvider<EventBus>(EventBus);
  }

  /**
   * Set trace context on this runner with proper fallback
   */
  setTraceContext(parentSpan?: LangfuseSpanClient | LangfuseGenerationClient): void {
    if (parentSpan) {
      this.currentTrace = parentSpan;
    } else {
      const currentGeneration = this.traceService.getCurrentGeneration();
      if (currentGeneration) {
        this.currentTrace = currentGeneration;
      }
    }
  }

  addGeneration(generation: Generation) {
    this.generations.push(generation);
    this.currentGeneration = generation;
    return this;
  }

  get provider(): LLMProviders {
    return this.currentGeneration.modelOptions.provider;
  }

  setProvider(provider: LLMProviders) {
    if (!this.currentGeneration.modelOptions) {
      this.currentGeneration.modelOptions = {};
    }
    this.currentGeneration.modelOptions.provider = provider;
    return this;
  }

  get model(): LLMs {
    return this.currentGeneration.model;
  }

  setModel(model: LLMs) {
    this.currentGeneration.model = model;
    return this;
  }

  get temperature(): number {
    return this.currentGeneration.modelOptions.temperature;
  }

  setTemperature(temperature: number) {
    this.currentGeneration.modelOptions.temperature = temperature;
    return this;
  }

  setRegenerate(regenerate: boolean) {
    if (regenerate) {
      this.currentGeneration.modelOptions.temperature = Math.min(
        this.currentGeneration.modelOptions.temperature * 2,
        1,
      );
    }
    return this;
  }

  get tools(): ToolNames[] {
    return this.currentGeneration.tools;
  }

  get toolChoice() {
    return this.currentGeneration.toolChoice;
  }

  get topP(): number {
    return this.currentGeneration.modelOptions.topP || 1;
  }

  get frequencyPenalty(): number {
    return this.currentGeneration.modelOptions.frequencyPenalty || 0;
  }

  get presencePenalty(): number {
    return this.currentGeneration.modelOptions.presencePenalty || 0;
  }

  get modelOptions(): {
    topP?: number;
    temperature?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  } {
    return this.currentGeneration.modelOptions;
  }

  get traceMetadata(): Record<string, string> {
    return this.currentGeneration.traceMetadata;
  }

  updateMetadata(metadata: Record<string, string>) {
    this.currentGeneration.traceMetadata = {
      ...this.currentGeneration.traceMetadata,
      ...metadata,
    };
    return this;
  }

  get promptName(): string {
    return this.currentGeneration.promptName;
  }

  get promptMessages(): CoreMessage[] {
    return this.currentGeneration.promptMessages;
  }

  protected getModel(useCache: boolean, model?: LLMs, provider?: LLMProviders): LanguageModelV1 {
    const targetModel = model || this.model;
    const targetProvider = provider || this.provider;
    return ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(targetModel, { useCache, provider: targetProvider });
  }

  protected getSpeechModel(model?: LLMs, provider?: LLMProviders): SpeechModelV1 {
    return ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getSpeechModel(model || this.model, provider);
  }

  protected async startTrace(
    name?: string,
    options?: GenerateOptions & { model?: { provider: string } },
  ): Promise<LangfuseGenerationClient> {
    const input = this.currentGeneration?.promptMessages || this.currentGeneration?.bizArgs;
    const params = {
      name: name || this.promptName || 'text-generation',
      model: this.model,
      input: input,
      modelParameters: {
        provider: options?.model?.provider || this.provider,
        ...this.modelOptions,
        ...(this.tools.length > 0 && {
          tools: this.tools.join(', '),
          toolChoice: typeof this.toolChoice === 'string' ? this.toolChoice : 'required',
        }),
      },
      metadata: {
        ...this.traceMetadata,
        useCache: options?.useCache,
      },
    };

    // Only create a new generation if we don't have a current trace
    const inheritTrace = options?.inheritTrace ?? true;
    const traceNode = await this.traceService.addGeneration(params, inheritTrace);
    this.setTraceContext(traceNode);
    return traceNode;
  }

  /**
   * 上报相关代码
   */
  protected async reportTextUsage(result: {
    usage: LanguageModelUsage;
    providerMetadata: ProviderMetadata;
    toolCalls?: ToolCall<string, any>[];
    toolResults?: ToolResultUnion<any>[];
  }) {
    const { usage, providerMetadata, toolCalls = [], toolResults = [] } = result;
    if (!usage) return undefined;

    let usageInfo: any;
    let usageDetails: any;

    const cachedInputTokens =
      +providerMetadata?.openai?.cachedPromptTokens ||
      +providerMetadata?.anthropic?.cacheReadInputTokens ||
      0;
    const reasoningTokens = +providerMetadata?.openai?.reasoningTokens || 0;
    const openaiPredictionTokens =
      +providerMetadata?.openai?.acceptedPredictionTokens +
        +providerMetadata?.openai?.rejectedPredictionTokens || 0;

    usageInfo = usage;
    const inputTokens = usage.promptTokens + cachedInputTokens;
    const outputTokens = usage.completionTokens + reasoningTokens + openaiPredictionTokens;

    usageDetails = {
      input: inputTokens,
      output: outputTokens,
      input_audio_tokens: 0,
      input_cache_tokens: cachedInputTokens || 0,
      output_audio_tokens: 0,
      output_reasoning_tokens: reasoningTokens || 0,
      output_accepted_prediction_tokens: providerMetadata?.openai?.acceptedPredictionTokens,
      output_rejected_prediction_tokens: providerMetadata?.openai?.rejectedPredictionTokens,
    };

    if (this.currentTrace) {
      this.currentTrace.update({
        usage,
        usageDetails,
      });
    }

    const tokensUsage: TokensUsage = {
      inputDetails: { text: usage.promptTokens },
      outputDetails: { text: usage.completionTokens },
      cacheReadDetails: cachedInputTokens > 0 ? { text: cachedInputTokens } : undefined,
      ...(providerMetadata?.anthropic?.cacheCreationInputTokens && {
        cacheCreationDetails: {
          '5m': providerMetadata.anthropic.cacheCreationInputTokens as number,
        },
      }),
    };

    const isWriter =
      toolResults.some((r) => r.type === 'tool-result' && r.toolName === ToolNames.EDIT_THOUGHT) ||
      toolCalls.some((r) => r.toolName === ToolNames.EDIT_THOUGHT);

    this.eventBus.publish(
      new TokensUsedEvent(
        this.model,
        isWriter ? LanguageModelTransactionCategory.WRITING : LanguageModelTransactionCategory.CHAT,
        tokensUsage,
      ),
    );

    return {
      usageInfo,
      usageDetails,
    };
  }

  protected reportEmbeddingUsage(usage: EmbeddingModelUsage) {
    if (!usage) return;
    if (!this.currentTrace) return;

    this.currentTrace.update({
      usage: {
        total: usage.tokens,
        unit: 'TOKENS',
      },
    });
  }

  protected async reportImageUsage(usage: ImagesResponse.Usage) {
    if (!usage) return;
    if (!this.currentTrace) return;

    this.currentTrace.update({
      usage: {
        input: usage.input_tokens,
        output: usage.output_tokens,
        total: usage.input_tokens + usage.output_tokens,
        unit: 'TOKENS',
      },
    });

    this.eventBus.publish(
      new TokensUsedEvent(this.model, LanguageModelTransactionCategory.IMAGE, {
        inputDetails: {
          image: usage.input_tokens_details.image_tokens,
          text: usage.input_tokens_details.text_tokens,
        },
        outputDetails: { image: usage.output_tokens },
      }),
    );
  }

  protected async reportSpeechUsage(result: Awaited<ReturnType<SpeechModelV1['doGenerate']>>) {
    if (!result) return;
    if (!this.currentTrace) return;

    let input = 0;
    let output = 0;
    let total = 0;
    let creditTransaction;

    const { minimax } = result.providerMetadata || {};
    if (minimax) {
      // @see https://www.minimax.io/platform/document/T2A%20V2?key=66719005a427f0c8a5701643#TJeyxusWAUP0l3tX67brbAyE
      const { usage_characters } = minimax as { usage_characters: number };
      input = 0;
      output = usage_characters;
      total = usage_characters;

      this.eventBus.publish(
        new TokensUsedEvent(this.model, LanguageModelTransactionCategory.AUDIO, {
          outputDetails: { audio: output },
        }),
      );
    } else {
      // @see https://platform.openai.com/docs/api-reference/audio/speech-audio-delta-event
      const { usage } = result.response?.body as any;
      if (usage) {
        input = usage.input_tokens;
        output = usage.output_tokens;
        total = usage.total_tokens;

        this.eventBus.publish(
          new TokensUsedEvent(this.model, LanguageModelTransactionCategory.AUDIO, {
            inputDetails: { audio: input },
            outputDetails: { audio: output },
          }),
        );
      }
    }

    this.currentTrace.update({
      usage: {
        input,
        output,
        total,
      },
    });

    this.logger.log(
      (result.response?.body as any)?.extra_info?.usage_characters,
      'reportSpeechUsage',
    );

    return { creditTransaction };
  }

  protected assembleApiParameters(
    model: LanguageModelV1,
    autoToolCall: boolean = true,
  ): Parameters<typeof generateText>[0] {
    if (!this.promptMessages?.length) {
      throw new Error('Prompt messages are required');
    }

    const toolCallService = ApplicationContext.getProvider<ToolCallService>(ToolCallService);

    const params: Parameters<typeof generateText>[0] = {
      model,
      messages: this.promptMessages,
      ...this.modelOptions,
      ...(this.tools && this.tools.length > 0
        ? {
            // Pass the current trace generation to ensure proper span attachment
            tools: toolCallService.getVercelToolCalls(
              this.currentGeneration.tools,
              autoToolCall,
              this.currentTrace,
            ),
            toolChoice:
              typeof this.currentGeneration.toolChoice === 'string'
                ? this.currentGeneration.toolChoice
                : {
                    type: 'tool',
                    toolName: this.currentGeneration.toolChoice.function.name,
                  },
          }
        : {}),
    };

    const modelDefinition = MODEL_DEFINITION.get(this.model);
    if (modelDefinition.type === 'reasoning') {
      delete params.temperature;
      delete params.maxTokens;
    }

    // 模型参数修改
    if (model.modelId === LLMs.GPT_4O_MINI) {
      params.presencePenalty = 0.8;
      params.frequencyPenalty = 0.8;
    }
    if (!params.providerOptions) {
      params.providerOptions = {};
    }

    if (modelDefinition.from === 'openai') {
      if (model.modelId === LLMs.GPT_5) {
        delete params.maxTokens;
      }

      if (!params.providerOptions.openai) {
        params.providerOptions.openai = {};
      }

      params.providerOptions.openai.parallelToolCalls = false;
      if (modelDefinition.type === 'reasoning') {
        params.providerOptions.openai.reasoningEffort = 'medium';
      }

      if (
        [LLMs.GPT_4O_MINI, LLMs.GPT_4O].includes(this.model) &&
        this.currentGeneration.prediction.length > 0
      ) {
        params.providerOptions.openai.prediction = {
          type: 'content',
          content: this.currentGeneration.prediction.map((p) => ({ type: 'text', text: p })),
        };
      }
    }
    if (modelDefinition.from === 'anthropic') {
      if (!params.providerOptions.anthropic) {
        params.providerOptions.anthropic = {};
      }

      if (modelDefinition.type === 'reasoning') {
        params.providerOptions.anthropic.thinking = {
          type: 'enabled',
          budgetTokens: modelDefinition.output_token_limit / 2,
        };
      }

      // 历史 reasoning 消息没有 Claude 要求的 signature 会出现报错，需要过滤掉
      // 需要过滤伪装思考 [{"type":"reasoning","textDelta":""}] 这种消息
      params.messages = params.messages
        .map((m) => {
          if (m.role === 'assistant') {
            const content = (m.content as any[]).filter((c) => {
              if (c.type === 'reasoning' && !('signature' in c)) return false;
              return true;
            });
            return { ...m, content } as CoreMessage;
          }
          return m as CoreMessage;
        })
        .filter((m) => m.content.length > 0);
    }

    // 加接口失败重试
    params.maxRetries = 1;

    return params;
  }

  /**
   * 保存所有完成块到数据库 (repository 会自动筛选新的或已修改的块)
   */
  protected async save(generation: Generation): Promise<void> {
    generation.updateGeneratedMessages();

    const generationModified = generation.isNew || generation.isModified;
    const blocksToSave = generation.blocks.filter((block) => block.isNew || block.isModified);
    if (!generationModified && !blocksToSave.length) {
      this.logger.debug('Nothing to save');
      return;
    }

    const completionBlockRepository =
      ApplicationContext.getProvider<CompletionBlockRepository>(CompletionBlockRepository);
    if (blocksToSave.length) {
      // 传入原始数组避免 isNew/isModified 状态没有更新成功
      const now = Date.now();
      await completionBlockRepository.saveMany(generation.blocks);
      this.logger.debug(`save ${blocksToSave.length} blocks took ${Date.now() - now}ms`);
    }

    const generationRepository =
      ApplicationContext.getProvider<GenerationRepository>(GenerationRepository);
    if (generationModified) {
      const now = Date.now();
      await generationRepository.save(generation);
      this.logger.debug(`save generation took ${Date.now() - now}ms`);
    }
  }

  public finalizeToolResult(
    generation: Generation,
    toolResult: ToolCallResultWithToolCallId,
    subject: Subject<CompletionStreamChunk<StreamChunkUnion>>,
  ) {
    const toolBlock = generation.getToolBlockByToolId(toolResult.toolCallId);
    if (!toolBlock) {
      throw new Error(`Tool block not found for tool call id: ${toolResult.toolCallId}`);
    }
    if (toolResult.isError) {
      toolBlock.failExecution(toolResult.result, toolResult.response);
    } else {
      toolBlock.completeExecution(toolResult.result, toolResult.response);
    }
    subject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetId: toolBlock.id,
      targetType: 'CompletionBlock',
      path: 'status',
      data: toolBlock.status,
    });

    const allBlocksFinal = generation.blocks.every((block) => block.isFinal());
    if (allBlocksFinal) {
      generation.setStatus(GenerationStatusEnum.SUCCESS);
      // TODO: Generation final
    }
  }

  /**
   * 录入 Completion Block
   * 包含每2秒的定期持久化和完成时的最终持久化
   * @param observable
   */
  public processTextStream(
    observable: Observable<TextStreamPart<any>>,
    autoToolCall: boolean = true,
    parentTrace?: LangfuseGenerationClient,
  ) {
    const traceService = ApplicationContext.getProvider<LangfuseTraceService>(LangfuseTraceService);
    const traceNode = parentTrace || this.currentTrace;
    const spanMap = new Map<string, LangfuseSpanClient>();

    const subject = new Subject<CompletionStreamChunk<StreamChunkUnion>>();
    const generation = this.currentGeneration;
    generation.setStatus(GenerationStatusEnum.GENERATING);

    // Log warning if no trace context is available
    if (!traceNode) {
      this.logger.warn('processTextStream: No trace context available, tracing will be limited');
    }

    // 设置定期保存完成块的定时器 (每2秒)
    // Wrap interval with trace context to preserve AsyncLocalStorage throughout the stream
    const persistenceTimer = traceService
      .wrapObservable(interval(2000))
      .pipe(
        takeUntil(subject), // 当subject完成或错误时停止定时器
      )
      .subscribe(() => {
        // 异步保存，不等待结果，错误处理在 saveBlocks 内部
        this.save(generation);
      });
    const onSuccess = async () => {
      // 清理定时器
      persistenceTimer.unsubscribe();

      // 如果是手动调用 tool call，则不更新 Generation 状态
      if (!autoToolCall) return;

      // 完成 Generation
      generation.setStatus(GenerationStatusEnum.SUCCESS);
      await this.save(generation);
      // 完成流
      subject.complete();
    };
    const onFailure = async (chunk: { type: 'error'; error: unknown }) => {
      this.logger.debug(`onFailure: ${generation.id}`);

      // Check if this is a tool-specific error based on AI SDK error types
      const error = chunk.error;
      let toolBlock: ToolCompletionBlock | null = null;
      if ((error as ToolExecutionError).toolCallId) {
        toolBlock = generation.getToolBlockByToolId((error as ToolExecutionError).toolCallId);
      }
      if (toolBlock) {
        // Tool-specific error - handle at block level only
        toolBlock.failExecution(
          {
            name: (error as Error).name,
            message: (error as Error).message,
            status: (error as RestError<any>)?.status,
          },
          `ERROR: ${error instanceof Error ? error.message : String(error)}`,
        );
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetId: toolBlock.id,
          targetType: 'CompletionBlock',
          path: 'status',
          data: toolBlock.status,
        });
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetId: toolBlock.id,
          targetType: 'CompletionBlock',
          path: 'extra.error',
          data: {
            name: (error as Error).name,
            message: (error as Error).message,
          },
        });

        await this.save(generation);
        return;
      }

      // System/API error - fail the entire generation
      generation.setStatus(GenerationStatusEnum.FAILED);
      await this.save(generation);
      // 清理定时器
      persistenceTimer.unsubscribe();
      // 将异常冒泡 - only for system errors
      subject?.error(error);
    };
    const getOrCreateBlock = (
      type: CompletionBlockTypeEnum,
      toolCallId?: string,
      toolName?: string,
    ) => {
      let current: CompletionBlock | null = null;
      if (toolCallId) {
        current = generation.getToolBlockByToolId(toolCallId);
      } else if (generation.blocks.length) {
        current = generation.blocks[generation.blocks.length - 1];
      }

      const doCreate = !current || current.type !== type;
      const updatePrevious = doCreate && current;

      if (updatePrevious) {
        current.updateStatus(CompletionBlockStatusEnum.DONE);
        if (type === CompletionBlockTypeEnum.REASONING) {
          // {"mode":"replace","targetType":"CompletionBlock","targetId":"0198922c-7ad6-7c1c-9d5d-34a81d04bb25","path":"updated_at","data":"2025-08-10T04:10:47.915Z"}
          subject.next({
            mode: CompletionStreamModeEnum.REPLACE,
            targetId: current.id,
            targetType: 'CompletionBlock',
            path: 'updated_at',
            data: current.updatedAt.toISOString(),
          });
        }
        subject.next({
          mode: CompletionStreamModeEnum.REPLACE,
          targetId: current.id,
          targetType: 'CompletionBlock',
          path: 'status',
          data: current.status,
        });
      }
      if (doCreate) {
        if (type === CompletionBlockTypeEnum.CONTENT) {
          current = ContentCompletionBlock.createNew({
            messageId: generation.chatMessageId,
            generationId: generation.id,
            data: '',
          });
        } else if (type === CompletionBlockTypeEnum.REASONING) {
          current = ReasoningCompletionBlock.createNew({
            messageId: generation.chatMessageId,
            generationId: generation.id,
            data: '',
          });
        } else if (type === CompletionBlockTypeEnum.TOOL) {
          current = ToolCompletionBlock.createNew({
            messageId: generation.chatMessageId,
            generationId: generation.id,
            toolId: toolCallId,
            toolName,
          });
        }

        this.logger.debug(
          `Created block ${current.id}/${current.type} with messageId: ${current.messageId}`,
        );

        generation.blocks.push(current);
        subject.next({
          mode: CompletionStreamModeEnum.INSERT,
          data: current.toCompletionStreamChunk(),
          dataType: 'CompletionBlock',
        });
      }

      return current;
    };

    // 使用 concatMap 因为要等异步处理
    // 使用 subscribe({ complete() {}, error() {} }) 作为最终消息回调
    // subject 在 onSuccess/onFailure 中抵达终态
    // 执行顺序 subscribe.error/subscribe.complete -> catchError -> finalize
    observable
      .pipe(
        concatMap(async (chunk) => {
          if (chunk.type === 'step-start') {
            traceNode.update({
              completionStartTime: new Date(),
            });
            traceNode.span({
              name: 'llm-request-parameters',
              input: {
                ...chunk.request,
                body: SafeParse(chunk.request.body, true, undefined) || chunk.request.body,
              },
            });

            // 伪装模型的 reasoning 消息，避免所有的思考都被过滤了
            const modelDefinition = MODEL_DEFINITION.get(this.model);
            if (modelDefinition.extra?.fake_reasoning) {
              const current = getOrCreateBlock(
                CompletionBlockTypeEnum.REASONING,
              ) as ReasoningCompletionBlock;

              subject.next({
                mode: CompletionStreamModeEnum.APPEND_STRING,
                data: '',
                path: 'data',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            }

            return;
          }

          if (chunk.type === 'error') {
            await onFailure(chunk);
            return;
          }

          if (chunk.type === 'finish') {
            await onSuccess();
            return;
          }

          if (chunk.type === 'text-delta') {
            const current = getOrCreateBlock(
              CompletionBlockTypeEnum.CONTENT,
            ) as ContentCompletionBlock;
            (current as ContentCompletionBlock).appendContent(chunk.textDelta);

            subject.next({
              mode: CompletionStreamModeEnum.APPEND_STRING,
              data: chunk.textDelta,
              path: 'data',
              targetId: current.id,
              targetType: 'CompletionBlock',
            });

            return;
          }

          if (
            chunk.type === 'reasoning' ||
            chunk.type === 'redacted-reasoning' ||
            chunk.type === 'reasoning-signature'
          ) {
            const current = getOrCreateBlock(
              CompletionBlockTypeEnum.REASONING,
            ) as ReasoningCompletionBlock;

            if (chunk.type === 'reasoning') {
              (current as ReasoningCompletionBlock).appendContent(chunk.textDelta);

              subject.next({
                mode: CompletionStreamModeEnum.APPEND_STRING,
                data: chunk.textDelta,
                path: 'data',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            } else if (chunk.type === 'redacted-reasoning') {
              (current as ReasoningCompletionBlock).appendRedactedReasoning(chunk.data);
            } else if (chunk.type === 'reasoning-signature') {
              (current as ReasoningCompletionBlock).setSignature(chunk.signature);
            }

            return;
          }

          if (
            chunk.type === 'tool-call' ||
            chunk.type === 'tool-call-delta' ||
            chunk.type === 'tool-call-streaming-start' ||
            chunk.type === 'tool-result'
          ) {
            const current = getOrCreateBlock(
              CompletionBlockTypeEnum.TOOL,
              chunk.toolCallId,
              chunk.toolName,
            ) as ToolCompletionBlock;

            if (chunk.type === 'tool-call-delta') {
              current.appendPartialToolArguments(chunk.argsTextDelta);

              subject.next({
                mode: CompletionStreamModeEnum.APPEND_JSON,
                data: chunk.argsTextDelta,
                path: 'tool_arguments',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
            } else if (chunk.type === 'tool-call') {
              current.startExecution(chunk.args as Record<string, unknown>);

              subject.next({
                mode: CompletionStreamModeEnum.REPLACE,
                data: current.status,
                path: 'status',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
              spanMap.get(chunk.toolCallId)?.update({
                input: chunk.args,
              });
            } else if (chunk.type === 'tool-result') {
              const toolCallResult: ToolCallResult = chunk.result as ToolCallResult;
              const toolResult = toolCallResult.result as Record<string, unknown>;
              const toolResponse = toolCallResult.response;

              current.completeExecution(toolResult, toolResponse);
              subject.next({
                mode: CompletionStreamModeEnum.REPLACE,
                data: current.status,
                path: 'status',
                targetId: current.id,
                targetType: 'CompletionBlock',
              });
              spanMap.get(chunk.toolCallId)?.update({
                input: current.toolArguments,
                output: {
                  result: current.toolResult,
                  response: current.toolResponse,
                },
                statusMessage: current.status,
              });
            }

            return;
          }

          return;
        }),
      )
      .subscribe({
        error: async (error) => {
          if (generation.getStatus() !== GenerationStatusEnum.GENERATING) {
            return;
          }

          this.logger.error(
            error,
            `uncaught error in processTextStream for generation ${generation.id}`,
          );
          await onFailure({ type: 'error', error });
        },
        complete: async () => {
          if (generation.getStatus() !== GenerationStatusEnum.GENERATING) {
            return;
          }

          await onSuccess();
        },
      });

    return subject;
  }

  public processTextResult(result: GenerateTextResult<any, any>) {
    const subject = new Subject<CompletionStreamChunk<StreamChunkUnion>>();
    if (!this.currentGeneration) return subject.asObservable();

    // add block
    if (result.reasoning) {
      const block = ReasoningCompletionBlock.createNew({
        data: result.reasoning,
        messageId: this.currentGeneration.chatMessageId,
        generationId: this.currentGeneration.id,
      });
      if (result.reasoningDetails[0]?.type === 'text' && result.reasoningDetails[0].signature) {
        block.setSignature(result.reasoningDetails[0].signature);
      }
      if (result.reasoningDetails[0]?.type === 'redacted') {
        block.appendRedactedReasoning(result.reasoningDetails[0].data);
      }
      subject.next({
        mode: CompletionStreamModeEnum.INSERT,
        data: block.toCompletionStreamChunk(),
        dataType: 'CompletionBlock',
      });
      this.currentGeneration.blocks.push(block);
    }
    const block = ContentCompletionBlock.createNew({
      data: result.text,
      messageId: this.currentGeneration.chatMessageId,
      generationId: this.currentGeneration.id,
    });
    subject.next({
      mode: CompletionStreamModeEnum.INSERT,
      data: block.toCompletionStreamChunk(),
      dataType: 'CompletionBlock',
    });
    this.currentGeneration.blocks.push(block);

    // add messages
    this.currentGeneration.updateGeneratedMessages();

    return subject;
  }
}
