import { Injectable } from '@nestjs/common';
import { LLMs } from '@/common/types';
import { purifyObject } from '@/common/utils';
import { OverviewPageData } from '@/modules/material-mng/domain/shared/value-objects/page-data.vo';
import { Generation } from '../domain/models/generation.entity';
import { OverviewRequestPageDataDto } from '../dto/overview.dto';
import { PromptService } from '../prompt/index.service';
import { TextRunner } from '../runners';

@Injectable()
export class OverviewService {
  constructor(private readonly promptService: PromptService) {}

  async overview(dto: OverviewRequestPageDataDto) {
    const { pageData, aiLanguage, aiSecondLanguage, enableBilingual = true, options = {} } = dto;

    const needBilingual =
      enableBilingual === true && !!aiSecondLanguage && aiLanguage !== aiSecondLanguage;

    // Extract content from pageData (similar to youapp implementation)
    const content = this.extractContentFromPageData(pageData);

    // Determine content length for summarize length
    let summarizeLength = '50';
    if (content.length < 200) {
      summarizeLength = '20';
    }

    // Determine which prompt to use based on content type
    const promptName = this.determineOverviewPromptName(pageData);

    // Check if this is a multi-speaker video
    const speakerLabels = pageData.speakerLabels;
    const isMultiSpeakerVideo = promptName === 'overview-video-prompt' && speakerLabels;

    // Build variables for the prompt
    const variables = purifyObject({
      aiLanguage,
      aiSecondLanguage: needBilingual ? aiSecondLanguage : '',
      summarizeLength,
      speakerLabels,
      content,
      title: pageData.title || '',
      summarySecondLanguageInstruction: needBilingual
        ? this.buildSummarySecondLanguageInstruction(aiLanguage, aiSecondLanguage)
        : undefined,
      outlineSecondLanguageInstruction: needBilingual
        ? this.buildOutlineSecondLanguageInstruction(aiLanguage, aiSecondLanguage)
        : undefined,
      aiSecondLanguageInstruction: this.buildAiSecondLanguageInstruction(
        aiLanguage,
        needBilingual ? aiSecondLanguage : undefined,
      ),
    });

    // Adjust prompt name for multi-speaker videos
    const finalPromptName = isMultiSpeakerVideo
      ? 'overview-video-multi-speaker-prompt'
      : promptName;

    // Fetch the appropriate overview prompt
    const { prompt, promptMessages } = this.promptService.getPromptAndMessages(
      finalPromptName,
      variables,
    );

    // Create generation domain model
    const generation = Generation.createFromPrompt({
      model: options.model as LLMs,
      prompt,
      promptMessages,
      modelOptions: {
        temperature: options.regenerate ? 1 : options.temperature || 0.5,
      },
      traceMetadata: {
        promptName: finalPromptName,
        contentType: pageData.type || 'unknown',
        hasMultipleSpeakers: String(isMultiSpeakerVideo),
      },
    });

    // Create and return TextRunner with generation and compiled messages
    const runner = new TextRunner();
    return runner.addGeneration(generation);
  }

  private extractContentFromPageData(pageData: OverviewPageData): string {
    // pageData.content?.plain ||
    // pageData.messages?.plain ||
    // pageData.transcript?.plain ||
    // pageData.show_notes?.plain ||
    // "";

    const content = pageData.content?.toPlainValue() || '';
    if (content) {
      return content;
    }

    const messages = Array.isArray(pageData.messages)
      ? pageData.messages.map((message) => message.content).join('\n')
      : pageData.messages?.plain || '';
    if (messages) {
      return messages;
    }

    const transcript = pageData.transcript?.getText() || '';
    if (transcript) {
      return transcript;
    }

    const showNotes = pageData.showNotes || '';
    if (showNotes) {
      return showNotes;
    }

    return '';
  }

  private determineOverviewPromptName(pageData: OverviewPageData): string {
    // Determine prompt based on content type (matching youapp logic)
    if (pageData.type === 'video' || pageData.type === 'voice') {
      return 'overview-video-prompt';
    }
    return 'overview-article-prompt';
  }

  private buildSummarySecondLanguageInstruction(
    primaryLanguage: string,
    secondaryLanguage: string,
  ): string {
    return `- You should first summarize in ${primaryLanguage}, followed by a translation in ${secondaryLanguage} language`;
  }

  private buildOutlineSecondLanguageInstruction(
    primaryLanguage: string,
    secondaryLanguage: string,
  ): string {
    return `- The bullet points should be ${primaryLanguage} followed by ${secondaryLanguage} in the same sentence or phrase
- The headings should be ${primaryLanguage} followed by ${secondaryLanguage} in the same sentence or phrase`;
  }

  private buildAiSecondLanguageInstruction(
    primaryLanguage?: string,
    secondaryLanguage?: string,
  ): string {
    return `${primaryLanguage ? `${primaryLanguage} ` : ''}${
      secondaryLanguage ? `or ${secondaryLanguage} ` : ''
    }`;
  }
}
