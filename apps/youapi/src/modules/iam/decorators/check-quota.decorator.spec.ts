/**
 * CheckQuota 装饰器单元测试
 * 测试配额限制检查功能
 */

import { YouapiClsService } from '@/common/services/cls.service';
import { ApplicationContext } from '@/common/utils/application-context';
import { ProductTier } from '../domain/subscription/models/subscription.types';
import { CreditAccountRepository } from '../repositories/credit-account.repository';
import { SpaceRepository } from '../repositories/space.repository';
import { UsageRecordRepository } from '../repositories/usage-record.repository';
import { QuotaResource } from '../utils/product-tier.types';
import { CheckQuota, QuotaExceededException } from './check-quota.decorator';

describe('CheckQuota Decorator', () => {
  let clsService: jest.Mocked<YouapiClsService>;
  let creditAccountRepository: jest.Mocked<CreditAccountRepository>;
  let spaceRepository: jest.Mocked<SpaceRepository>;
  let usageRecordRepository: jest.Mocked<UsageRecordRepository>;

  // 创建测试类来验证 CheckQuota 装饰器的不同配额资源
  class TestHandler {
    @CheckQuota(QuotaResource.STORAGE)
    async testStorageQuota(): Promise<void> {
      // 空方法，只用于触发装饰器验证
    }

    @CheckQuota(QuotaResource.NEW_SNIP)
    async testNewSnipQuota(): Promise<void> {
      // 空方法，只用于触发装饰器验证
    }
  }

  beforeEach(async () => {
    // Mock CLS Service
    clsService = {
      getSpaceId: jest.fn(),
      getUserId: jest.fn(),
      getProductTier: jest.fn(),
      setSpaceId: jest.fn(),
      setProductTier: jest.fn(),
    } as any;

    // Mock CreditAccountRepository
    creditAccountRepository = {
      getBySpaceId: jest.fn(),
    } as any;

    // Mock SpaceRepository
    spaceRepository = {
      findByCreatorId: jest.fn(),
    } as any;

    // Mock UsageRecordRepository
    usageRecordRepository = {
      getTotalUsage: jest.fn(),
    } as any;

    // Mock ApplicationContext static methods
    jest.spyOn(ApplicationContext, 'getSpaceId').mockImplementation(async () => {
      return clsService.getSpaceId() || 'space-1';
    });

    jest.spyOn(ApplicationContext, 'getProductTier').mockImplementation(async () => {
      return clsService.getProductTier() || ProductTier.FREE;
    });

    // Mock ApplicationContext to provide required services (for internal use)
    jest.spyOn(ApplicationContext, 'getProvider').mockImplementation((token: any) => {
      if (token === YouapiClsService) return clsService;
      if (token === CreditAccountRepository) return creditAccountRepository;
      if (token === SpaceRepository) return spaceRepository;
      if (token === UsageRecordRepository) return usageRecordRepository;
      throw new Error(`Unknown provider: ${token.name}`);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Storage Quota Limits (STORAGE)', () => {
    describe('FREE tier (limit: 100GB)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.FREE);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
        } as any);
      });

      it('should allow FREE user with storage under limit', async () => {
        // 99GB in bytes
        usageRecordRepository.getTotalUsage.mockResolvedValue(99 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).resolves.toBeUndefined();
      });

      it('should reject FREE user with storage at limit', async () => {
        // 100GB in bytes (exact limit)
        usageRecordRepository.getTotalUsage.mockResolvedValue(100 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).rejects.toThrow(QuotaExceededException);
      });

      it('should reject FREE user with storage over limit', async () => {
        // 150GB in bytes
        usageRecordRepository.getTotalUsage.mockResolvedValue(150 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).rejects.toThrow(QuotaExceededException);
      });
    });

    describe('PRO tier (limit: 100GB)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.PRO);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.PRO,
        } as any);
      });

      it('should allow PRO user with storage under limit', async () => {
        // 99GB in bytes
        usageRecordRepository.getTotalUsage.mockResolvedValue(99 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).resolves.toBeUndefined();
      });

      it('should reject PRO user with storage at limit', async () => {
        // 100GB in bytes (exact limit)
        usageRecordRepository.getTotalUsage.mockResolvedValue(100 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).rejects.toThrow(QuotaExceededException);
      });

      it('should reject PRO user with storage over limit', async () => {
        // 150GB in bytes
        usageRecordRepository.getTotalUsage.mockResolvedValue(150 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).rejects.toThrow(QuotaExceededException);
      });
    });

    describe('MAX tier (limit: 100GB)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.MAX);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.MAX,
        } as any);
      });

      it('should allow MAX user with storage under limit', async () => {
        // 99GB in bytes
        usageRecordRepository.getTotalUsage.mockResolvedValue(99 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).resolves.toBeUndefined();
      });

      it('should reject MAX user with storage at limit', async () => {
        // 100GB in bytes (exact limit)
        usageRecordRepository.getTotalUsage.mockResolvedValue(100 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).rejects.toThrow(QuotaExceededException);
      });

      it('should reject MAX user with storage over limit', async () => {
        // 150GB in bytes
        usageRecordRepository.getTotalUsage.mockResolvedValue(150 * 1024 * 1024 * 1024);

        const handler = new TestHandler();
        await expect(handler.testStorageQuota()).rejects.toThrow(QuotaExceededException);
      });
    });
  });

  describe('New Snip Quota Limits (NEW_SNIP)', () => {
    describe('FREE tier (limit: 100个)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.FREE);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
        } as any);
      });

      it('should allow FREE user with snips under limit', async () => {
        usageRecordRepository.getTotalUsage.mockResolvedValue(99);

        const handler = new TestHandler();
        await expect(handler.testNewSnipQuota()).resolves.toBeUndefined();
      });

      it('should reject FREE user with snips at limit', async () => {
        usageRecordRepository.getTotalUsage.mockResolvedValue(100);

        const handler = new TestHandler();
        await expect(handler.testNewSnipQuota()).rejects.toThrow(QuotaExceededException);
      });

      it('should reject FREE user with snips over limit', async () => {
        usageRecordRepository.getTotalUsage.mockResolvedValue(150);

        const handler = new TestHandler();
        await expect(handler.testNewSnipQuota()).rejects.toThrow(QuotaExceededException);
      });
    });

    describe('PRO tier (no limit)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.PRO);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.PRO,
        } as any);
      });

      it('should allow PRO user with any number of snips', async () => {
        usageRecordRepository.getTotalUsage.mockResolvedValue(500);

        const handler = new TestHandler();
        await expect(handler.testNewSnipQuota()).resolves.toBeUndefined();
      });

      it('should allow PRO user with large number of snips', async () => {
        usageRecordRepository.getTotalUsage.mockResolvedValue(1000);

        const handler = new TestHandler();
        await expect(handler.testNewSnipQuota()).resolves.toBeUndefined();
      });
    });

    describe('MAX tier (no limit)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.MAX);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.MAX,
        } as any);
      });

      it('should allow MAX user with any number of snips', async () => {
        usageRecordRepository.getTotalUsage.mockResolvedValue(2000);

        const handler = new TestHandler();
        await expect(handler.testNewSnipQuota()).resolves.toBeUndefined();
      });

      it('should allow MAX user with very large number of snips', async () => {
        usageRecordRepository.getTotalUsage.mockResolvedValue(5000);

        const handler = new TestHandler();
        await expect(handler.testNewSnipQuota()).resolves.toBeUndefined();
      });
    });
  });

  describe('Error Details', () => {
    it('should provide detailed error information when quota exceeded', async () => {
      // Setup: FREE user testing storage quota
      clsService.getSpaceId.mockReturnValue('space-1');
      clsService.getUserId.mockReturnValue('user-1');
      clsService.getProductTier.mockReturnValue(ProductTier.FREE);

      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.FREE,
      } as any);

      // Mock usage at limit (100GB in bytes)
      const limitInBytes = 100 * 1024 * 1024 * 1024;
      usageRecordRepository.getTotalUsage.mockResolvedValue(limitInBytes);

      const handler = new TestHandler();

      try {
        await handler.testStorageQuota();
        fail('Should have thrown QuotaExceededException');
      } catch (error) {
        expect(error).toBeInstanceOf(QuotaExceededException);

        const quotaError = error as QuotaExceededException;
        expect(quotaError.getStatus()).toBe(402); // Payment Required

        const response = quotaError.getResponse() as any;
        expect(response.code).toBe('QuotaExceededException');
        expect(response.status).toBe(402);
        expect(response.message).toContain('storage quota exceeded');
        expect(response.message).toContain('100.0 GB');
        expect(response.message).toContain('free tier');
      }
    });
  });

  describe('Service Dependencies', () => {
    it('should cache and query spaceId correctly', async () => {
      // Test case: spaceId not in cache, needs query
      const mockGetSpaceId = jest.spyOn(ApplicationContext, 'getSpaceId');
      mockGetSpaceId.mockResolvedValue('space-from-db');

      clsService.getProductTier.mockReturnValue(ProductTier.FREE);
      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.FREE,
      } as any);
      // 50GB in bytes
      usageRecordRepository.getTotalUsage.mockResolvedValue(50 * 1024 * 1024 * 1024);

      const handler = new TestHandler();
      await handler.testStorageQuota();

      // Verify spaceId method was called
      expect(mockGetSpaceId).toHaveBeenCalled();
    });

    it('should cache and query productTier correctly', async () => {
      // Test case: productTier not in cache, needs query
      const mockGetSpaceId = jest.spyOn(ApplicationContext, 'getSpaceId');
      mockGetSpaceId.mockResolvedValue('space-1');

      const mockGetProductTier = jest.spyOn(ApplicationContext, 'getProductTier');
      mockGetProductTier.mockResolvedValue(ProductTier.PRO);

      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.PRO,
      } as any);
      // 50GB in bytes
      usageRecordRepository.getTotalUsage.mockResolvedValue(50 * 1024 * 1024 * 1024);

      const handler = new TestHandler();
      await handler.testStorageQuota();

      // Verify productTier method was called
      expect(mockGetProductTier).toHaveBeenCalled();
    });

    it('should call UsageRecordRepository.getTotalUsage with correct parameters', async () => {
      clsService.getSpaceId.mockReturnValue('space-1');
      clsService.getProductTier.mockReturnValue(ProductTier.FREE);
      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.FREE,
      } as any);
      // 50GB in bytes
      usageRecordRepository.getTotalUsage.mockResolvedValue(50 * 1024 * 1024 * 1024);

      const handler = new TestHandler();
      await handler.testStorageQuota();

      // Verify usage query was called with correct parameters
      expect(usageRecordRepository.getTotalUsage).toHaveBeenCalledWith(
        'space-1',
        QuotaResource.STORAGE,
      );
    });
  });
});
