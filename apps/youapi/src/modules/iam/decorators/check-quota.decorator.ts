import { HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ApplicationContext } from '@/common/utils/application-context';
import { ProductTier } from '../domain/subscription/models/subscription.types';
import { UsageRecordRepository } from '../repositories/usage-record.repository';
import { QuotaResource } from '../utils/product-tier.types';
import { getTierQuota } from '../utils/product-tier.utils';

/**
 * 将存储量格式化为用户友好的显示格式（英文版）
 */
function formatStorageForDisplay(bytes: number): string {
  if (bytes >= 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  }
  if (bytes >= 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
  if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  }
  return `${bytes} bytes`;
}

/**
 * 根据资源类型格式化配额显示（英文版）
 */
function formatQuotaForDisplay(value: number, resource: QuotaResource): string {
  switch (resource) {
    case QuotaResource.STORAGE:
      return formatStorageForDisplay(value);
    case QuotaResource.NEW_SNIP:
      return `${value} items`;
    default:
      return `${value}`;
  }
}

/**
 * 配额超出异常
 */
export class QuotaExceededException extends HttpException {
  constructor(message?: string) {
    super(
      {
        message: message || 'Quota exceeded',
        code: 'QuotaExceededException',
        status: HttpStatus.PAYMENT_REQUIRED,
      },
      HttpStatus.PAYMENT_REQUIRED, // 402 状态码表示需要付费
    );
  }
}

/**
 * 配额检查装饰器 - 基于累积总量限制
 *
 * 使用示例：
 * ```typescript
 * @Injectable()
 * export class SomeService {
 *   @CheckQuota(QuotaResourceType.STORAGE)
 *   async createSnip() {
 *     // 业务逻辑
 *   }
 *
 *   @CheckQuota(QuotaResourceType.NEW_SNIP)
 *   async createNewSnip() {
 *     // 业务逻辑
 *   }
 * }
 * ```
 *
 * 特性：
 * - 简洁 API：只需要一个 resource 参数
 * - 自动获取用户等级和空间信息
 * - 基于累积总量检查配额限制并记录使用量
 * - 缓存用户等级信息避免重复查询
 */
export function CheckQuota(resource: QuotaResource): MethodDecorator {
  return (_target: unknown, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    if (typeof originalMethod !== 'function') {
      throw new Error(`@CheckQuota can only be applied to methods`);
    }

    // 创建 Logger 实例
    const logger = new Logger(CheckQuota.name);

    descriptor.value = async function (...args: unknown[]) {
      try {
        // 获取必要的用户上下文信息
        const spaceId = await ApplicationContext.getSpaceId();
        const productTier = await ApplicationContext.getProductTier();

        // 检查是否有配额限制配置
        const quotaLimit = getTierQuota(productTier as ProductTier, resource);

        if (!quotaLimit) {
          // 没有配置限制，直接放行
          logger.debug(`No quota limit for ${resource} (${productTier}), access granted`);
          return await originalMethod.apply(this, args);
        }

        // 检查累积总量
        const usageRecordRepository =
          ApplicationContext.getProvider<UsageRecordRepository>(UsageRecordRepository);
        const currentUsage = await usageRecordRepository.getTotalUsage(spaceId, resource);

        // 检查是否超出限制
        if (currentUsage >= quotaLimit) {
          const currentDisplay = formatQuotaForDisplay(currentUsage, resource);
          const limitDisplay = formatQuotaForDisplay(quotaLimit, resource);
          throw new QuotaExceededException(
            `${resource} quota exceeded. Used ${currentDisplay}, limit ${limitDisplay} (${productTier} tier)`,
          );
        }

        // 执行原方法
        logger.debug(`Quota check passed for ${resource}: ${currentUsage}/${quotaLimit} (total)`);

        return await originalMethod.apply(this, args);
      } catch (error) {
        // 记录检查失败的情况
        logger.debug(
          `Quota check failed in method ${String(propertyKey)} for resource ${resource}`,
          error instanceof Error ? error.message : error,
        );
        // 所有异常直接向上抛出
        throw error;
      }
    };

    return descriptor;
  };
}
