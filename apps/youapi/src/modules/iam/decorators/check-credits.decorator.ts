import { HttpException, HttpStatus, Logger } from '@nestjs/common';
import { ApplicationContext } from '@/common/utils/application-context';
import { CreditAccountRepository } from '../repositories/credit-account.repository';

/**
 * 积分不足异常
 *
 * 使用 HTTP 402 Payment Required 状态码，表示需要付费/升级才能继续使用服务。
 * 这个状态码专门为积分/配额不足等付费相关场景设计。
 */
export class InsufficientCreditsException extends HttpException {
  constructor(message?: string) {
    super(
      {
        message: message || 'Insufficient credits',
        code: InsufficientCreditsException.name,
        status: HttpStatus.PAYMENT_REQUIRED,
      },
      HttpStatus.PAYMENT_REQUIRED, // 402 状态码表示需要付费
    );
  }
}

/**
 * 积分余额检查装饰器
 *
 * 使用示例：
 * ```typescript
 * @Injectable()
 * export class ClaudeTokensUsedHandler {
 *   @CheckCredits()
 *   async handle(event: ClaudeTokensUsedEvent) {
 *     // 业务逻辑
 *   }
 * }
 * ```
 *
 * 特性：
 * - 轻量级检查：只检查积分余额是否大于 0
 * - 早期拦截：在业务逻辑执行前进行检查，避免无意义的处理
 * - 缓存优化：同一请求中缓存 spaceId，避免重复数据库查询
 * - 异常透传：所有异常直接向上抛出，不做包装转换
 * - 装饰器组合：建议在 @Transactional() 之前使用
 */
export function CheckCredits(): MethodDecorator {
  return (_target: unknown, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    if (typeof originalMethod !== 'function') {
      throw new Error(`@CheckCredits can only be applied to methods`);
    }

    // 创建 Logger 实例
    const logger = new Logger(CheckCredits.name);

    descriptor.value = async function (...args: unknown[]) {
      try {
        // 1. 获取或查询 spaceId
        const spaceId = await ApplicationContext.getSpaceId();

        // 2. 检查积分余额
        const creditAccountRepository =
          ApplicationContext.getProvider<CreditAccountRepository>(CreditAccountRepository);
        const creditAccount = await creditAccountRepository.getBySpaceId(spaceId);

        // 3. 检查余额是否大于 0
        if (creditAccount.monthlyBalance <= 0) {
          throw new InsufficientCreditsException(
            `Credits exhausted. Available: ${creditAccount.monthlyBalance} (${creditAccount.productTier} tier)`,
          );
        }

        // 4. 执行原方法
        return await originalMethod.apply(this, args);
      } catch (error) {
        // 记录检查失败的情况
        logger.debug(
          `Credits check failed in method ${String(propertyKey)}`,
          error instanceof Error ? error.message : error,
        );
        // 所有异常直接向上抛出
        throw error;
      }
    };

    return descriptor;
  };
}
