import { HttpException, HttpStatus } from '@nestjs/common';
import { LimitType } from '../utils/product-tier.types';

/**
 * 将文件大小格式化为用户友好的显示格式（英文版）
 */
function formatFileSizeForDisplay(bytes: number): string {
  if (bytes >= 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  }
  if (bytes >= 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
  if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  }
  return `${bytes} bytes`;
}

/**
 * 根据限制类型格式化参数显示（英文版）
 */
function formatLimitForDisplay(value: number, limitType: LimitType): string {
  switch (limitType) {
    case LimitType.FILE_SIZE:
      return formatFileSizeForDisplay(value);
    case LimitType.DOCUMENT_PAGES:
      return `${value} pages`;
    case LimitType.AUDIO_DURATION: {
      const minutes = value / 60;
      return `${minutes.toFixed(1)} minutes`;
    }
    default:
      return `${value}`;
  }
}

/**
 * 限制超出异常
 */
export class LimitExceededException extends HttpException {
  constructor(message?: string) {
    super(
      {
        message: message || 'Limit exceeded',
        code: 'LimitExceededException',
        status: HttpStatus.PAYMENT_REQUIRED,
      },
      HttpStatus.PAYMENT_REQUIRED, // 402 状态码表示需要付费
    );
  }
}

/**
 * 参数限制元数据接口
 */
export interface ParameterLimitMetadata {
  limitType: LimitType;
  parameterIndex: number;
  extractStrategy: 'direct' | 'length' | 'byteLength' | 'custom';
  customExtractor?: (value: any) => number;
}

/**
 * 元数据存储键
 */
export const PARAMETER_LIMITS_METADATA = Symbol('parameter-limits');

/**
 * 从参数值中提取数值的策略函数
 */
export function extractValueFromParameter(
  value: any,
  strategy: ParameterLimitMetadata['extractStrategy'],
  customExtractor?: (value: any) => number,
): number {
  switch (strategy) {
    case 'direct':
      return typeof value === 'number' ? value : 0;

    case 'length':
      return value?.length ?? 0;

    case 'byteLength':
      return value?.byteLength ?? 0;

    case 'custom':
      return customExtractor ? customExtractor(value) : 0;

    default:
      return 0;
  }
}

/**
 * 自动推断参数值提取策略
 */
function inferExtractionStrategy(limitType: LimitType): ParameterLimitMetadata['extractStrategy'] {
  switch (limitType) {
    case LimitType.FILE_SIZE:
      return 'direct'; // 直接传入bytes数值（file.size）

    case LimitType.DOCUMENT_PAGES:
      return 'direct'; // 直接传入页数

    case LimitType.AUDIO_DURATION:
      return 'direct'; // 直接传入秒数（ffprobe.duration）

    default:
      return 'direct';
  }
}

/**
 * 参数限制装饰器
 *
 * 用于标记需要进行限制检查的方法参数。
 * 必须与 @CheckLimit() 方法装饰器配合使用。
 *
 * 使用示例：
 * ```typescript
 * @Injectable()
 * export class FileService {
 *   @CheckLimit() // 方法装饰器：执行检查
 *   async uploadFile(
 *     @Limit(LimitType.FILE_SIZE) fileSize: number, // 参数装饰器：标记限制
 *     fileName: string,
 *     content: Buffer
 *   ) {
 *     // FREE用户: 最大10MB, PRO用户: 最大100MB, MAX用户: 无限制
 *     return this.fileRepository.save(content);
 *   }
 *
 *   @CheckLimit()
 *   async batchDeleteItems(
 *     @Limit(LimitType.BATCH_SIZE) itemIds: string[]
 *   ) {
 *     // FREE用户: 最大50个, PRO用户: 最大200个, MAX用户: 无限制
 *     // 装饰器会自动检查 itemIds.length
 *     return this.itemRepository.deleteByIds(itemIds);
 *   }
 * }
 * ```
 *
 * 特性：
 * - 参数级别标记：精确标记需要限制检查的参数
 * - 智能值提取：自动推断如何从参数中提取数值
 * - 等级差异化：FREE/PRO/MAX不同限制
 * - 默认开放：未配置的组合默认无限制
 * - 灵活配置：支持自定义提取策略
 */
export function Limit(
  limitType: LimitType,
  options?: {
    extractStrategy?: ParameterLimitMetadata['extractStrategy'];
    customExtractor?: (value: any) => number;
  },
): ParameterDecorator {
  return (target: any, propertyKey: string | symbol | undefined, parameterIndex: number) => {
    // 获取现有的参数限制元数据
    const existingMetadata: ParameterLimitMetadata[] =
      Reflect.getMetadata(PARAMETER_LIMITS_METADATA, target, propertyKey!) || [];

    // 创建新的参数限制元数据
    const parameterLimit: ParameterLimitMetadata = {
      limitType,
      parameterIndex,
      extractStrategy: options?.extractStrategy || inferExtractionStrategy(limitType),
      customExtractor: options?.customExtractor,
    };

    // 添加到元数据数组
    existingMetadata.push(parameterLimit);

    // 保存到方法的元数据中
    Reflect.defineMetadata(PARAMETER_LIMITS_METADATA, existingMetadata, target, propertyKey!);
  };
}
