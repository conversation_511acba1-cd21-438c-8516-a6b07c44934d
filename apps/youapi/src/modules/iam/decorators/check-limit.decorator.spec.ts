/**
 * CheckLimit 装饰器单元测试
 * 测试参数限制检查功能
 */

import { YouapiClsService } from '@/common/services/cls.service';
import { ApplicationContext } from '@/common/utils/application-context';
import { ProductTier } from '../domain/subscription/models/subscription.types';
import { CreditAccountRepository } from '../repositories/credit-account.repository';
import { SpaceRepository } from '../repositories/space.repository';
import { LimitType } from '../utils/product-tier.types';
import { CheckLimit } from './check-limit.decorator';
import { Limit, LimitExceededException } from './limit.decorator';

describe('CheckLimit Decorator', () => {
  let clsService: jest.Mocked<YouapiClsService>;
  let creditAccountRepository: jest.Mocked<CreditAccountRepository>;
  let spaceRepository: jest.Mocked<SpaceRepository>;

  // 创建测试类来验证 CheckLimit 装饰器的不同限制类型
  class TestHandler {
    @CheckLimit()
    async validateFileSize(@Limit(LimitType.FILE_SIZE) fileSizeInBytes: number): Promise<void> {
      // 空方法，只用于触发装饰器验证
    }

    @CheckLimit()
    async validateDocumentPages(@Limit(LimitType.DOCUMENT_PAGES) pageCount: number): Promise<void> {
      // 空方法，只用于触发装饰器验证
    }

    @CheckLimit()
    async validateAudioDuration(
      @Limit(LimitType.AUDIO_DURATION) durationSeconds: number,
    ): Promise<void> {
      // 空方法，只用于触发装饰器验证
    }
  }

  beforeEach(async () => {
    // Mock CLS Service
    clsService = {
      getSpaceId: jest.fn(),
      getUserId: jest.fn(),
      getProductTier: jest.fn(),
      setSpaceId: jest.fn(),
      setProductTier: jest.fn(),
    } as any;

    // Mock CreditAccountRepository
    creditAccountRepository = {
      getBySpaceId: jest.fn(),
    } as any;

    // Mock SpaceRepository
    spaceRepository = {
      findByCreatorId: jest.fn(),
    } as any;

    // Mock ApplicationContext static methods
    jest.spyOn(ApplicationContext, 'getProductTier').mockImplementation(async () => {
      return clsService.getProductTier() || ProductTier.FREE;
    });

    // Mock ApplicationContext to provide required services (for internal use)
    jest.spyOn(ApplicationContext, 'getProvider').mockImplementation((token: any) => {
      if (token === YouapiClsService) return clsService;
      if (token === CreditAccountRepository) return creditAccountRepository;
      if (token === SpaceRepository) return spaceRepository;
      throw new Error(`Unknown provider: ${token.name}`);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('File Size Limits (FILE_SIZE)', () => {
    describe('FREE tier (limit: 2MB)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.FREE);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
        } as any);
      });

      it('should allow FREE user with file exactly 2MB', async () => {
        const handler = new TestHandler();
        // 2MB in bytes
        await expect(handler.validateFileSize(2 * 1024 * 1024)).resolves.toBeUndefined();
      });

      it('should allow FREE user with file smaller than 2MB', async () => {
        const handler = new TestHandler();
        // 1.5MB in bytes
        await expect(handler.validateFileSize(1.5 * 1024 * 1024)).resolves.toBeUndefined();
      });

      it('should reject FREE user with file larger than 2MB', async () => {
        const handler = new TestHandler();
        // 3MB in bytes
        await expect(handler.validateFileSize(3 * 1024 * 1024)).rejects.toThrow(
          LimitExceededException,
        );
      });
    });

    describe('PRO tier (limit: 100MB)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.PRO);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.PRO,
        } as any);
      });

      it('should allow PRO user with file exactly 100MB', async () => {
        const handler = new TestHandler();
        // 100MB in bytes
        await expect(handler.validateFileSize(100 * 1024 * 1024)).resolves.toBeUndefined();
      });

      it('should allow PRO user with file smaller than 100MB', async () => {
        const handler = new TestHandler();
        // 80MB in bytes
        await expect(handler.validateFileSize(80 * 1024 * 1024)).resolves.toBeUndefined();
      });

      it('should reject PRO user with file larger than 100MB', async () => {
        const handler = new TestHandler();
        // 150MB in bytes
        await expect(handler.validateFileSize(150 * 1024 * 1024)).rejects.toThrow(
          LimitExceededException,
        );
      });
    });

    describe('MAX tier (limit: 500MB)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.MAX);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.MAX,
        } as any);
      });

      it('should allow MAX user with file exactly 500MB', async () => {
        const handler = new TestHandler();
        // 500MB in bytes
        await expect(handler.validateFileSize(500 * 1024 * 1024)).resolves.toBeUndefined();
      });

      it('should allow MAX user with file smaller than 500MB', async () => {
        const handler = new TestHandler();
        // 400MB in bytes
        await expect(handler.validateFileSize(400 * 1024 * 1024)).resolves.toBeUndefined();
      });

      it('should reject MAX user with file larger than 500MB', async () => {
        const handler = new TestHandler();
        // 600MB in bytes
        await expect(handler.validateFileSize(600 * 1024 * 1024)).rejects.toThrow(
          LimitExceededException,
        );
      });
    });
  });

  describe('Document Pages Limits (DOCUMENT_PAGES)', () => {
    describe('FREE tier (limit: 5 pages)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.FREE);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
        } as any);
      });

      it('should allow FREE user with document exactly 5 pages', async () => {
        const handler = new TestHandler();
        await expect(handler.validateDocumentPages(5)).resolves.toBeUndefined();
      });

      it('should allow FREE user with document smaller than 5 pages', async () => {
        const handler = new TestHandler();
        await expect(handler.validateDocumentPages(3)).resolves.toBeUndefined();
      });

      it('should reject FREE user with document larger than 5 pages', async () => {
        const handler = new TestHandler();
        await expect(handler.validateDocumentPages(8)).rejects.toThrow(LimitExceededException);
      });
    });

    describe('PRO tier (no limit)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.PRO);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.PRO,
        } as any);
      });

      it('should allow PRO user with small document', async () => {
        const handler = new TestHandler();
        await expect(handler.validateDocumentPages(10)).resolves.toBeUndefined();
      });

      it('should allow PRO user with large document', async () => {
        const handler = new TestHandler();
        await expect(handler.validateDocumentPages(100)).resolves.toBeUndefined();
      });
    });

    describe('MAX tier (no limit)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.MAX);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.MAX,
        } as any);
      });

      it('should allow MAX user with small document', async () => {
        const handler = new TestHandler();
        await expect(handler.validateDocumentPages(20)).resolves.toBeUndefined();
      });

      it('should allow MAX user with very large document', async () => {
        const handler = new TestHandler();
        await expect(handler.validateDocumentPages(1000)).resolves.toBeUndefined();
      });
    });
  });

  describe('Audio Duration Limits (AUDIO_DURATION)', () => {
    describe('FREE tier (limit: 10 minutes)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.FREE);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
        } as any);
      });

      it('should allow FREE user with audio exactly 10 minutes', async () => {
        const handler = new TestHandler();
        // 10 minutes in seconds
        await expect(handler.validateAudioDuration(10 * 60)).resolves.toBeUndefined();
      });

      it('should allow FREE user with audio shorter than 10 minutes', async () => {
        const handler = new TestHandler();
        // 8 minutes in seconds
        await expect(handler.validateAudioDuration(8 * 60)).resolves.toBeUndefined();
      });

      it('should reject FREE user with audio longer than 10 minutes', async () => {
        const handler = new TestHandler();
        // 15 minutes in seconds
        await expect(handler.validateAudioDuration(15 * 60)).rejects.toThrow(
          LimitExceededException,
        );
      });
    });

    describe('PRO tier (limit: 120 minutes)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.PRO);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.PRO,
        } as any);
      });

      it('should allow PRO user with audio exactly 120 minutes', async () => {
        const handler = new TestHandler();
        // 120 minutes in seconds
        await expect(handler.validateAudioDuration(120 * 60)).resolves.toBeUndefined();
      });

      it('should allow PRO user with audio shorter than 120 minutes', async () => {
        const handler = new TestHandler();
        // 100 minutes in seconds
        await expect(handler.validateAudioDuration(100 * 60)).resolves.toBeUndefined();
      });

      it('should reject PRO user with audio longer than 120 minutes', async () => {
        const handler = new TestHandler();
        // 150 minutes in seconds
        await expect(handler.validateAudioDuration(150 * 60)).rejects.toThrow(
          LimitExceededException,
        );
      });
    });

    describe('MAX tier (limit: 240 minutes)', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.MAX);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.MAX,
        } as any);
      });

      it('should allow MAX user with audio exactly 240 minutes', async () => {
        const handler = new TestHandler();
        // 240 minutes in seconds
        await expect(handler.validateAudioDuration(240 * 60)).resolves.toBeUndefined();
      });

      it('should allow MAX user with audio shorter than 240 minutes', async () => {
        const handler = new TestHandler();
        // 200 minutes in seconds
        await expect(handler.validateAudioDuration(200 * 60)).resolves.toBeUndefined();
      });

      it('should reject MAX user with audio longer than 240 minutes', async () => {
        const handler = new TestHandler();
        // 300 minutes in seconds
        await expect(handler.validateAudioDuration(300 * 60)).rejects.toThrow(
          LimitExceededException,
        );
      });
    });
  });

  describe('Error Details', () => {
    it('should provide detailed error information when limit exceeded', async () => {
      // Setup: FREE user testing file size limit
      clsService.getSpaceId.mockReturnValue('space-1');
      clsService.getUserId.mockReturnValue('user-1');
      clsService.getProductTier.mockReturnValue(ProductTier.FREE);

      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.FREE,
      } as any);

      const handler = new TestHandler();

      try {
        // 5MB in bytes for FREE user (limit: 2MB)
        const fileSizeInBytes = 5 * 1024 * 1024;
        await handler.validateFileSize(fileSizeInBytes);
        fail('Should have thrown LimitExceededException');
      } catch (error) {
        expect(error).toBeInstanceOf(LimitExceededException);

        const limitError = error as LimitExceededException;
        expect(limitError.getStatus()).toBe(402); // Payment Required

        const response = limitError.getResponse() as any;
        expect(response.code).toBe('LimitExceededException');
        expect(response.status).toBe(402);
        expect(response.message).toContain('file_size limit exceeded');
        expect(response.message).toContain('5.0 MB');
        expect(response.message).toContain('2.0 MB');
        expect(response.message).toContain('free tier');
      }
    });
  });
});
