/**
 * CheckCredits 装饰器单元测试
 * 测试积分不足异常处理功能
 */

import { YouapiClsService } from '@/common/services/cls.service';
import { ApplicationContext } from '@/common/utils/application-context';
import { ProductTier } from '../domain/subscription/models/subscription.types';
import { CreditAccountRepository } from '../repositories/credit-account.repository';
import { SpaceRepository } from '../repositories/space.repository';
import { CheckCredits, InsufficientCreditsException } from './check-credits.decorator';

describe('CheckCredits Decorator', () => {
  let clsService: jest.Mocked<YouapiClsService>;
  let creditAccountRepository: jest.Mocked<CreditAccountRepository>;
  let spaceRepository: jest.Mocked<SpaceRepository>;

  // 创建测试类来验证 CheckCredits 装饰器
  class TestHandler {
    @CheckCredits()
    async testOperation(): Promise<void> {
      // 空方法，只用于触发装饰器验证
    }
  }

  beforeEach(async () => {
    // Mock CLS Service
    clsService = {
      getSpaceId: jest.fn(),
      getUserId: jest.fn(),
      getProductTier: jest.fn(),
      setSpaceId: jest.fn(),
      setProductTier: jest.fn(),
    } as any;

    // Mock CreditAccountRepository
    creditAccountRepository = {
      getBySpaceId: jest.fn(),
      updateBalance: jest.fn(),
    } as any;

    // Mock SpaceRepository
    spaceRepository = {
      findByCreatorId: jest.fn(),
    } as any;

    // Mock ApplicationContext static methods
    jest.spyOn(ApplicationContext, 'getSpaceId').mockImplementation(async () => {
      return clsService.getSpaceId() || 'space-1';
    });

    jest.spyOn(ApplicationContext, 'getProductTier').mockImplementation(async () => {
      return clsService.getProductTier() || ProductTier.FREE;
    });

    // Mock ApplicationContext to provide required services (for internal use)
    jest.spyOn(ApplicationContext, 'getProvider').mockImplementation((token: any) => {
      if (token === YouapiClsService) return clsService;
      if (token === CreditAccountRepository) return creditAccountRepository;
      if (token === SpaceRepository) return spaceRepository;
      throw new Error(`Unknown provider: ${token.name}`);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('积分充足的情况', () => {
    describe('FREE tier', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.FREE);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
          monthlyBalance: 20,
        } as any);
      });

      it('should allow FREE user with sufficient credits', async () => {
        const handler = new TestHandler();
        await expect(handler.testOperation()).resolves.toBeUndefined();
      });

      it('should allow FREE user with positive balance', async () => {
        // 设置有积分的账户
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
          monthlyBalance: 5,
        } as any);

        const handler = new TestHandler();
        await expect(handler.testOperation()).resolves.toBeUndefined();
      });
    });

    describe('PRO tier', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.PRO);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.PRO,
          monthlyBalance: 100,
        } as any);
      });

      it('should allow PRO user with sufficient credits', async () => {
        const handler = new TestHandler();
        await expect(handler.testOperation()).resolves.toBeUndefined();
      });
    });

    describe('MAX tier', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.MAX);
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.MAX,
          monthlyBalance: 200,
        } as any);
      });

      it('should allow MAX user with sufficient credits', async () => {
        const handler = new TestHandler();
        await expect(handler.testOperation()).resolves.toBeUndefined();
      });
    });
  });

  describe('积分不足的情况', () => {
    describe('FREE tier', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.FREE);
      });

      it('should reject FREE user with no credits', async () => {
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
          monthlyBalance: 0,
        } as any);

        const handler = new TestHandler();
        await expect(handler.testOperation()).rejects.toThrow(InsufficientCreditsException);
      });

      it('should reject FREE user with negative balance', async () => {
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.FREE,
          monthlyBalance: -5,
        } as any);

        const handler = new TestHandler();
        await expect(handler.testOperation()).rejects.toThrow(InsufficientCreditsException);
      });
    });

    describe('PRO tier', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.PRO);
      });

      it('should reject PRO user with zero balance', async () => {
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.PRO,
          monthlyBalance: 0,
        } as any);

        const handler = new TestHandler();
        await expect(handler.testOperation()).rejects.toThrow(InsufficientCreditsException);
      });
    });

    describe('MAX tier', () => {
      beforeEach(() => {
        clsService.getSpaceId.mockReturnValue('space-1');
        clsService.getUserId.mockReturnValue('user-1');
        clsService.getProductTier.mockReturnValue(ProductTier.MAX);
      });

      it('should reject MAX user with negative balance', async () => {
        creditAccountRepository.getBySpaceId.mockResolvedValue({
          productTier: ProductTier.MAX,
          monthlyBalance: -10,
        } as any);

        const handler = new TestHandler();
        await expect(handler.testOperation()).rejects.toThrow(InsufficientCreditsException);
      });
    });
  });

  describe('异常详情验证', () => {
    it('should provide correct error details when credits insufficient', async () => {
      // Setup: FREE user testing with insufficient credits
      clsService.getSpaceId.mockReturnValue('space-1');
      clsService.getUserId.mockReturnValue('user-1');
      clsService.getProductTier.mockReturnValue(ProductTier.FREE);

      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.FREE,
        monthlyBalance: 0,
      } as any);

      const handler = new TestHandler();

      try {
        await handler.testOperation();
        fail('Should have thrown InsufficientCreditsException');
      } catch (error) {
        expect(error).toBeInstanceOf(InsufficientCreditsException);

        const creditsError = error as InsufficientCreditsException;
        expect(creditsError.getStatus()).toBe(402); // Payment Required

        const response = creditsError.getResponse() as any;
        expect(response.code).toBe('InsufficientCreditsException');
        expect(response.status).toBe(402);
        expect(response.message).toContain('Credits exhausted');
        expect(response.message).toContain('0');
        expect(response.message).toContain('free tier');
      }
    });
  });

  describe('服务依赖验证', () => {
    it('should cache and query spaceId correctly', async () => {
      // Test case: spaceId not in cache, needs query
      const mockGetSpaceId = jest.spyOn(ApplicationContext, 'getSpaceId');
      mockGetSpaceId.mockResolvedValue('space-from-db');

      clsService.getProductTier.mockReturnValue(ProductTier.FREE);
      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.FREE,
        monthlyBalance: 10,
      } as any);

      const handler = new TestHandler();
      await handler.testOperation();

      // Verify spaceId method was called
      expect(mockGetSpaceId).toHaveBeenCalled();
    });

    it('should get productTier from credit account data', async () => {
      // Test case: productTier comes from database, not from ApplicationContext
      const mockGetSpaceId = jest.spyOn(ApplicationContext, 'getSpaceId');
      mockGetSpaceId.mockResolvedValue('space-1');

      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.PRO,
        monthlyBalance: 100,
      } as any);

      const handler = new TestHandler();
      await handler.testOperation();

      // Verify credit account was queried and productTier comes from there
      expect(creditAccountRepository.getBySpaceId).toHaveBeenCalledWith('space-1');
      expect(mockGetSpaceId).toHaveBeenCalled();
    });

    it('should call CreditAccountRepository.getBySpaceId with correct parameters', async () => {
      clsService.getSpaceId.mockReturnValue('space-1');
      clsService.getProductTier.mockReturnValue(ProductTier.FREE);
      creditAccountRepository.getBySpaceId.mockResolvedValue({
        productTier: ProductTier.FREE,
        monthlyBalance: 10,
      } as any);

      const handler = new TestHandler();
      await handler.testOperation();

      // Verify credit account query was called with correct parameters
      expect(creditAccountRepository.getBySpaceId).toHaveBeenCalledWith('space-1');
    });
  });
});
