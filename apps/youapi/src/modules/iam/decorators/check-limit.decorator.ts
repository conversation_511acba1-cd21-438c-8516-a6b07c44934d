import { Logger } from '@nestjs/common';
import { ApplicationContext } from '@/common/utils/application-context';
import { ProductTier } from '../domain/subscription/models/subscription.types';
import { LimitType } from '../utils/product-tier.types';
import { getTierLimit } from '../utils/product-tier.utils';
import {
  extractValueFromParameter,
  LimitExceededException,
  PARAMETER_LIMITS_METADATA,
  ParameterLimitMetadata,
} from './limit.decorator';

/**
 * 将文件大小格式化为用户友好的显示格式（英文版）
 */
function formatFileSizeForDisplay(bytes: number): string {
  if (bytes >= 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  }
  if (bytes >= 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
  if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(1)} KB`;
  }
  return `${bytes} bytes`;
}

/**
 * 根据限制类型格式化参数显示（英文版）
 */
function formatLimitForDisplay(value: number, limitType: LimitType): string {
  switch (limitType) {
    case LimitType.FILE_SIZE:
      return formatFileSizeForDisplay(value);
    case LimitType.DOCUMENT_PAGES:
      return `${value} pages`;
    case LimitType.AUDIO_DURATION: {
      const minutes = value / 60;
      return `${minutes.toFixed(1)} minutes`;
    }
    default:
      return `${value}`;
  }
}

/**
 * 参数限制检查装饰器
 *
 * 该装饰器会在方法执行前检查所有标记了 @Limit 的参数，
 * 确保参数值符合用户当前产品等级的限制。
 *
 * 使用示例：
 * ```typescript
 * @Injectable()
 * export class FileService {
 *   @CheckLimit() // 方法装饰器：执行检查
 *   async uploadFile(
 *     @Limit(LimitType.FILE_SIZE) fileSize: number, // 参数装饰器：标记限制
 *     fileName: string,
 *     content: Buffer
 *   ) {
 *     // FREE用户: 最大10MB, PRO用户: 最大100MB, MAX用户: 无限制
 *     return this.fileRepository.save(content);
 *   }
 *
 *   @CheckLimit()
 *   async batchOperation(
 *     @Limit(LimitType.BATCH_SIZE) itemIds: string[],
 *     @Limit(LimitType.TEXT_LENGTH) description: string
 *   ) {
 *     // 支持同一方法中多个参数限制
 *     return this.processItems(itemIds, description);
 *   }
 * }
 * ```
 *
 * 特性：
 * - 方法级别检查：在方法执行前进行所有参数限制检查
 * - 自动获取等级：复用现有CLS缓存机制获取用户产品等级
 * - 智能值提取：根据参数类型自动提取需要检查的数值
 * - 配置驱动：通过配置控制不同等级的限制规则
 * - 异常处理：提供详细的限制超出异常信息
 */
export function CheckLimit(): MethodDecorator {
  return (_target: unknown, propertyKey: string | symbol, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;

    if (typeof originalMethod !== 'function') {
      throw new Error(`@CheckLimit can only be applied to methods`);
    }

    // 创建 Logger 实例
    const logger = new Logger(CheckLimit.name);

    descriptor.value = async function (...args: unknown[]) {
      try {
        // 获取该方法的参数限制元数据
        const parameterLimits: ParameterLimitMetadata[] =
          Reflect.getMetadata(PARAMETER_LIMITS_METADATA, _target, propertyKey!) || [];

        // 如果没有参数限制，直接执行原方法
        if (parameterLimits.length === 0) {
          return await originalMethod.apply(this, args);
        }

        // 获取用户等级信息
        const productTier = await ApplicationContext.getProductTier();

        // 检查每个标记的参数
        for (const parameterLimit of parameterLimits) {
          await checkParameterLimit(parameterLimit, args, productTier, String(propertyKey), logger);
        }

        // 所有检查通过，执行原方法
        return await originalMethod.apply(this, args);
      } catch (error) {
        // 记录检查失败的情况
        logger.debug(
          `Parameter limit check failed in method ${String(propertyKey)}`,
          error instanceof Error ? error.message : error,
        );
        // 所有异常直接向上抛出
        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * 检查单个参数的限制
 */
async function checkParameterLimit(
  parameterLimit: ParameterLimitMetadata,
  methodArgs: unknown[],
  productTier: string,
  methodName: string,
  logger: Logger,
): Promise<void> {
  const { limitType, parameterIndex, extractStrategy, customExtractor } = parameterLimit;

  // 获取参数值
  const parameterValue = methodArgs[parameterIndex];

  // 提取数值
  const value = extractValueFromParameter(parameterValue, extractStrategy, customExtractor);

  // 获取限制配置
  const limitConfig = getTierLimit(productTier as ProductTier, limitType);

  if (!limitConfig) {
    // 没有配置限制，直接放行
    logger.debug(`No limit for ${limitType} (${productTier}), access granted`);
    return;
  }

  // 检查是否超出限制
  if (value > limitConfig) {
    const currentDisplay = formatLimitForDisplay(value, limitType);
    const limitDisplay = formatLimitForDisplay(limitConfig, limitType);
    throw new LimitExceededException(
      `${limitType} limit exceeded. Used ${currentDisplay}, limit ${limitDisplay} (${productTier} tier)`,
    );
  }

  // 记录检查通过
  logger.debug(
    `Parameter limit check passed for ${limitType}: ${value}/${limitConfig} in method ${methodName}`,
  );
}
