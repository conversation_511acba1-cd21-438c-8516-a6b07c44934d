import { omit } from 'lodash-es';
import { concatMap, endWith, map, Observable } from 'rxjs';
import {
  CompletionBlockStatusEnum,
  CompletionBlockTypeEnum,
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  InternetSearchResult,
  MessageEvent,
  MessageEventTypeEnum,
  MessageStatusEnum,
  SearchResultTypeEnum,
  StreamDataTypeEnum,
  StreamMessage,
  ToolNames,
} from '@/common/types';
import { camelToSnakeKey, SafeParse } from '@/common/utils';

/**
 * Converts CompletionStreamChunk to StreamMessage format
 * Migrated from youapp src/lib/domain/chat/index.ts
 */

// createChat Example
// data: {"type":"content_start"}
// data: {"type":"data","data":{"id":"0198a897-74e5-7e8d-a084-d2dd25403fa8","creator_id":"d7a9bac3-18e8-4a04-a9d0-fd9d8a6d9f60","created_at":{},"updated_at":{},"title":"Hi there","origin":{"type":"webpage","id":"","url":"https://medium.com/@michelleglauser/how-the-ilooklikeanengineer-ad-campaign-happened-ten-years-ago-762e1a443e77"},"board_id":"","mode":"chat","show_new_board_suggestion":false,"messages":[{"id":"0198a897-74ed-71e0-840b-7e7409aa6929","chat_id":"0198a897-74e5-7e8d-a084-d2dd25403fa8","created_at":{},"updated_at":{},"status":"success","role":"user","origin":{"id":"","url":"https://medium.com/@michelleglauser/how-the-ilooklikeanengineer-ad-campaign-happened-ten-years-ago-762e1a443e77","type":"webpage"},"content":"Hi there","selection":"","at_references":[],"board_id":"","tools":{},"mode":"ask"},{"id":"0198a897-74f1-73e0-a7c9-376b3b7774d9","chat_id":"0198a897-74e5-7e8d-a084-d2dd25403fa8","created_at":{},"updated_at":{},"status":"queued","role":"assistant","model":"gpt-4o","error":null,"trace_id":"","blocks":[],"events":[],"content":"","reasoning":""}],"board_ids":[]},"dataType":"ChatDetail","id":"0198a897-74e5-7e8d-a084-d2dd25403fa8"}
// data: {"type":"content_start"}
// data: {"type":"chunked_data","parentDataType":"Message","parentId":"0198a897-74f1-73e0-a7c9-376b3b7774d9","chunkPath":"model","chunkData":"gpt-4o"}
// data: {"type":"trace_id","trace_id":"795db554-c771-4e9a-9a1a-300e367964a3"}
// data: {"data":"Hello","type":"content","contentType":"content"}
// data: {"type":"content_stop"}

// sendMessage with tool call Example
// data: {"type":"data","data":{"id":"0198a897-74e5-7e8d-a084-d2dd25403fa8","creator_id":"d7a9bac3-18e8-4a04-a9d0-fd9d8a6d9f60","created_at":{},"updated_at":{},"title":"Hi there","origin":{"type":"webpage","id":"","url":"https://medium.com/@michelleglauser/how-the-ilooklikeanengineer-ad-campaign-happened-ten-years-ago-762e1a443e77"},"board_id":"","mode":"chat","show_new_board_suggestion":false,"board_ids":[]},"dataType":"Chat","id":"0198a897-74e5-7e8d-a084-d2dd25403fa8"}
// data: {"type":"data","data":{"id":"0198a89a-154e-7348-95dd-79d79be08f1e","chat_id":"0198a897-74e5-7e8d-a084-d2dd25403fa8","created_at":{},"updated_at":{},"status":"success","role":"user","origin":{"id":"","url":"https://medium.com/@michelleglauser/how-the-ilooklikeanengineer-ad-campaign-happened-ten-years-ago-762e1a443e77","type":"webpage"},"content":"帮我搜索类似文章","selection":"","at_references":[],"board_id":"","tools":{},"mode":"ask"},"dataType":"Message","id":"0198a89a-154e-7348-95dd-79d79be08f1e"}
// data: {"type":"data","data":{"id":"0198a89a-1551-77b7-920b-2e1550424fda","chat_id":"0198a897-74e5-7e8d-a084-d2dd25403fa8","created_at":{},"updated_at":{},"status":"queued","role":"assistant","model":"gpt-4o","error":null,"trace_id":"","blocks":[],"events":[],"content":"","reasoning":""},"dataType":"Message","id":"0198a89a-1551-77b7-920b-2e1550424fda"}
// data: {"type":"content_start"}
// data: {"type":"chunked_data","parentDataType":"Message","parentId":"0198a89a-1551-77b7-920b-2e1550424fda","chunkPath":"model","chunkData":"gpt-4o"}
// data: {"type":"trace_id","trace_id":"27f8a594-ad10-4f32-9ca0-30092a2b6c36"}
// data: {"type":"status_update","status":"generating","message":"Searching #ILookLikeAnEngineer 相关文章"}
// data: {"type":"chunked_data","parentDataType":"Message","parentId":"0198a89a-1551-77b7-920b-2e1550424fda","chunkPath":"events.0","chunkData":{"type":"search","query":"#ILookLikeAnEngineer 相关文章","results":[],"time_of_action":"2025-08-14T12:42:08.069Z"}}
// data: {"data":"你好","type":"content","contentType":"content"}
// data: {"type":"content_stop"}

// Overview Example
// data: {"type":"data","data":{"id":"0195f1a2-2c60-781d-96be-c638bd84a423","updated_at":{},"type":"overview","snip_id":"0195f1a2-2b61-750b-ab9b-9741d8c77e75","current_content_id":"0195f1a2-2c72-752b-8dc1-40b58bb39206","contents":[{"id":"0195f1a2-2c72-752b-8dc1-40b58bb39206","block_id":"0195f1a2-2c60-781d-96be-c638bd84a423","status":"completed","format":"llm-output","raw":"","plain":"","language":"en-US","trace_id":"0d5ecc44-2452-430b-b63e-065de599383f"}],"status":"completed"},"dataType":"SnipOverview","id":"0195f1a2-2c60-781d-96be-c638bd84a423"}
// data: {"type":"content_start"}
// data: {"type":"content","data":"..."}
// data: {"type":"trace_id","trace_id":"a6adc2bd-28a7-4533-9a6a-ebadeee3d8f9"}
// data: {"type":"content_stop"}
// data: {"type":"data","dataType":"Content","data":{"id":"0195f1a2-2c72-752b-8dc1-40b58bb39206","block_id":"0195f1a2-2c60-781d-96be-c638bd84a423","status":"completed","format":"llm-output","raw":"...","plain":"...","language":"en-US","trace_id":"a6adc2bd-28a7-4533-9a6a-ebadeee3d8f9"},"id":"0195f1a2-2c72-752b-8dc1-40b58bb39206"}

export function toV1CompatibleObservable(
  observable: Observable<CompletionStreamChunk<any>>,
  isCreateChat: boolean = false,
): Observable<StreamMessage<any>> {
  let emitStart = false;
  let chat;
  const toolArgStrMap = {};
  const blocks: Record<string, object> = {};
  const toolBlockIds = [];

  const mappedObservable = observable.pipe(
    map((chunk: CompletionStreamChunk<any>): StreamMessage<any>[] => {
      const messages: StreamMessage<any>[] = [];

      if (chunk.mode === CompletionStreamModeEnum.INSERT) {
        if (chunk.dataType === 'Chat') {
          if (isCreateChat) {
            // create chat 时，一次性发送 Chat + 消息2条
            chat = chunk.data;
            chat.messages = [];
          } else {
            messages.push({
              id: chunk.data.id,
              type: StreamDataTypeEnum.DATA,
              data: chunk.data,
              dataType: 'Chat',
            } as StreamMessage<any>);
          }
        } else if (chunk.dataType === 'Message') {
          if (isCreateChat) {
            chat.messages.push(chunk.data);
            if (chat.messages.length === 2) {
              messages.push({
                id: chunk.data.id,
                type: StreamDataTypeEnum.DATA,
                data: chat,
                dataType: 'ChatDetail',
              } as StreamMessage<any>);
            }
          } else {
            messages.push({
              id: chunk.data.id,
              type: StreamDataTypeEnum.DATA,
              data: chunk.data,
              dataType: 'Message',
            } as StreamMessage<any>);
          }

          // data: {"type":"chunked_data","parentDataType":"Message","parentId":"0198a897-74f1-73e0-a7c9-376b3b7774d9","chunkPath":"model","chunkData":"gpt-4o"}
          if (chunk.data.model) {
            messages.push({
              type: StreamDataTypeEnum.CHUNKED_DATA,
              parentDataType: 'Message',
              parentId: chunk.data.id,
              chunkPath: 'model',
              chunkData: chunk.data.model,
            } as StreamMessage<any>);
          }
          // data: {"type":"trace_id","trace_id":"795db554-c771-4e9a-9a1a-300e367964a3"}
          if (chunk.data.trace_id) {
            messages.push({
              type: StreamDataTypeEnum.TRACE_ID,
              trace_id: chunk.data.trace_id,
            } as StreamMessage<any>);
          }
        } else if (chunk.dataType === 'CompletionBlock') {
          const block = omit(chunk.data, ['tool_id', 'tool_response']);
          blocks[chunk.data.id] = block;

          if (block.id && block.type === CompletionBlockTypeEnum.TOOL) {
            toolBlockIds.push(chunk.data.id);
          }
          // Handle reasoning blocks
          if (block.type === CompletionBlockTypeEnum.REASONING) {
            messages.push({
              type: StreamDataTypeEnum.CHUNKED_DATA,
              parentDataType: 'Message',
              parentId: block.message_id,
              chunkPath: 'reasoning_begin_at',
              chunkData: new Date(block.created_at).getTime().toString(),
            } as StreamMessage<any>);
          }
        } else {
          // 其他类型进行透传
          // {"type":"data","data":{},"dataType":"Content","id":"0195f1a2-2c60-781d-96be-c638bd84a423"}
          messages.push({
            type: StreamDataTypeEnum.DATA,
            dataType: chunk.dataType,
            data: chunk.data,
            id: chunk.data.id,
          });
        }
      }

      // Handle REPLACE mode - when existing data is updated
      if (chunk.mode === CompletionStreamModeEnum.REPLACE) {
        if (chunk.targetType === 'Message') {
          if (chunk.path === 'error') {
            messages.push({
              type: StreamDataTypeEnum.ERROR,
              error: chunk.data,
            });
          }
        }

        if (chunk.targetType === 'CompletionBlock') {
          if (chunk.path === 'tool_result') {
            // data: {"type":"chunked_data","parentDataType":"Message","parentId":"0198a89a-1551-77b7-920b-2e1550424fda","chunkPath":"events.0","chunkData":{"type":"search","query":"#ILookLikeAnEngineer 相关文章","results":[],"time_of_action":"2025-08-14T12:42:08.069Z"}}
            const eventIndex = toolBlockIds.findIndex((id) => id === chunk.targetId);
            const block = blocks[chunk.targetId];
            if (block) {
              messages.push({
                type: StreamDataTypeEnum.CHUNKED_DATA,
                parentDataType: 'Message',
                parentId: chunk.targetId, // Approximate
                chunkPath: 'events.' + eventIndex,
                chunkData: convertToolCallResultToMessageEvent({
                  updated_at: new Date((block as any).update_at || Date.now()),
                  tool_name: (block as any).tool_name || '',
                  tool_arguments: SafeParse(toolArgStrMap[chunk.targetId], true, { query: '' }),
                  tool_result: chunk.data,
                }),
              });
            }
          }

          if (chunk.path === 'updated_at') {
            const block = blocks[chunk.targetId] as any;
            if (block && block.type === CompletionBlockTypeEnum.REASONING) {
              messages.push({
                type: StreamDataTypeEnum.CHUNKED_DATA,
                parentDataType: 'Message',
                parentId: chunk.targetId,
                chunkPath: 'reasoning_end_at',
                chunkData: new Date(chunk.data).getTime().toString(),
              });
            }
          }

          if (chunk.path === 'extra.error') {
            messages.push({
              type: StreamDataTypeEnum.ERROR,
              error: chunk.data,
            });
          }

          if (chunk.path === 'status') {
            if (chunk.data === CompletionBlockStatusEnum.EXECUTING) {
              if (blocks[chunk.targetId]) {
                messages.push({
                  type: StreamDataTypeEnum.STATUS_UPDATE,
                  status: MessageStatusEnum.ING,
                  message: getToolDisplayMessage({
                    tool_name: (blocks[chunk.targetId] as any).tool_name,
                    tool_arguments: SafeParse(toolArgStrMap[chunk.targetId], true, { query: '' }),
                  }),
                });
              }
            }
          }
        }
      }

      // Handle APPEND_STRING mode - when text content is streamed
      if (chunk.mode === CompletionStreamModeEnum.APPEND_STRING) {
        if (!emitStart) {
          emitStart = true;
          messages.push({
            type: StreamDataTypeEnum.CONTENT_START,
          } as StreamMessage<any>);
        }

        if (chunk.targetType === 'CompletionBlock' && chunk.path === 'data') {
          const block = blocks[chunk.targetId] as any;
          // Need block type to determine content vs reasoning
          // For content blocks
          messages.push({
            data: chunk.data,
            type: StreamDataTypeEnum.CONTENT,
            contentType: block?.type || 'content',
          });
        }
      }

      // Handle APPEND_JSON mode - when structured data is streamed
      if (chunk.mode === CompletionStreamModeEnum.APPEND_JSON) {
        if (chunk.targetType === 'CompletionBlock' && chunk.path === 'tool_arguments') {
          if (!toolArgStrMap[chunk.targetId]) {
            toolArgStrMap[chunk.targetId] = '';
          }
          toolArgStrMap[chunk.targetId] += chunk.data;
        }
      }

      // Handle ERROR mode - when an error occurs
      if (chunk.mode === CompletionStreamModeEnum.ERROR) {
        console.log(
          '[toV1CompatibleObservable] Error mode - full chunk:',
          JSON.stringify(chunk, null, 2),
        );
        console.log(
          '[toV1CompatibleObservable] Error mode - chunk.error type:',
          typeof chunk.error,
        );
        console.log(
          '[toV1CompatibleObservable] Error mode - chunk.error keys:',
          chunk.error ? Object.keys(chunk.error) : 'null/undefined',
        );
        console.log(
          '[toV1CompatibleObservable] Error mode - chunk properties:',
          Object.keys(chunk),
        );
        console.log(
          '[toV1CompatibleObservable] Error mode - chunk.error message:',
          (chunk.error as any)?.message,
        );
        console.log(
          '[toV1CompatibleObservable] Error mode - chunk.error stack:',
          (chunk.error as any)?.stack,
        );

        // Extract error information - handle both Error objects and plain objects
        let errorData = chunk.error;

        // If error is empty object, try to create a meaningful error
        if (errorData && typeof errorData === 'object' && Object.keys(errorData).length === 0) {
          console.log(
            '[toV1CompatibleObservable] Empty error object detected, creating fallback error',
          );
          errorData = {
            message: 'An unknown error occurred during message processing',
            name: 'UnknownError',
          };
        }

        console.log(
          '[toV1CompatibleObservable] Error data being sent:',
          JSON.stringify(errorData, null, 2),
        );

        messages.push({
          type: StreamDataTypeEnum.ERROR,
          error: errorData,
        });
      }

      return messages;
    }),
    concatMap((messages) => messages),
  );

  // Add CONTENT_STOP message when the observable completes
  // Use endWith to append CONTENT_STOP when the source completes
  return mappedObservable.pipe(
    endWith({ type: StreamDataTypeEnum.CONTENT_STOP } as StreamMessage<any>),
  );
}

/**
 * Get status message for tool blocks
 */
const getToolDisplayMessage = (block: {
  tool_name: string;
  tool_arguments?: { query: string };
}) => {
  if (['image_edit', 'image_generate', 'diagram_generate'].includes(block.tool_name)) {
    return 'Creating image';
  }
  if (block.tool_name === 'google_search' && block.tool_arguments?.query) {
    return 'Searching ' + (block.tool_arguments?.query || '');
  }
  if (block.tool_name === 'library_search') {
    return 'Searching in board ' + (block.tool_arguments?.query || '');
  }
  if (block.tool_name === 'create_snip_by_url') {
    return 'Creating Snip';
  }
  return '';
};

const convertToolCallResultToMessageEvent = (block: {
  updated_at: Date;
  tool_name: string;
  tool_arguments: { query: string };
  tool_result: object;
}): MessageEvent => {
  switch (block.tool_name) {
    case camelToSnakeKey(ToolNames.GOOGLE_SEARCH):
      return {
        type: MessageEventTypeEnum.SEARCH,
        query: block.tool_arguments?.query || '',
        results:
          (block.tool_result as any)?.results?.map((r: InternetSearchResult) => ({
            entity_type: SearchResultTypeEnum.INTERNET_SEARCH_RESULT,
            entity_vo: r,
          })) || [],
        time_of_action: new Date(block.updated_at).toISOString(),
      };
    case camelToSnakeKey(ToolNames.BOARD_SEARCH):
    case camelToSnakeKey(ToolNames.LIBRARY_SEARCH):
    case 'resolve_library':
      return {
        type: MessageEventTypeEnum.RESOLVE_LIBRARY,
        query: block.tool_arguments?.query || '',
        results: (block.tool_result as any)?.results || [],
        time_of_action: new Date(block.updated_at).toISOString(),
      };
    case camelToSnakeKey(ToolNames.IMAGE_GENERATE):
      return {
        type: MessageEventTypeEnum.GENERATE_IMAGE,
        results: [],
        image_urls: (block.tool_result as any)?.image_urls,
        time_of_action: new Date(block.updated_at).toISOString(),
      };
    case camelToSnakeKey(ToolNames.DIAGRAM_GENERATE):
      return {
        type: MessageEventTypeEnum.GENERATE_SVG,
        results: [],
        svg: (block.tool_result as any)?.svg,
        image_url: (block.tool_result as any)?.image_url,
        time_of_action: new Date(block.updated_at).toISOString(),
      };
    case camelToSnakeKey(ToolNames.CREATE_SNIP_BY_URL):
      return {
        type: MessageEventTypeEnum.CREATE_SNIP_BY_URL,
        snipsResults: (block.tool_result as any)?.snipsResults || [],
        summary_message: (block.tool_result as any)?.summary_message || '',
        time_of_action: new Date(block.updated_at).toISOString(),
      };
    default:
      return { results: [] } as unknown as MessageEvent;
  }
};
