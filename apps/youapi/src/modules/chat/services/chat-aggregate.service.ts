/**
 * Chat Aggregate Service - 聊天聚合服务
 * 处理聊天聚合的复杂业务逻辑，包括数据聚合和业务规则
 *
 * 位于Chat模块内，遵循DDD原则：
 * - 包含复杂的业务逻辑，不属于单个聚合
 * - 协调多个聚合之间的操作
 * - 提供模块级别的服务能力
 *
 * Migrated from:
 * - ChatRepository.aggregateJoinResult() - 数据聚合逻辑
 */

import { Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { CompletionBlockDO } from '@/modules/ai/domain/models/completion-block.entity';
import { Chat, ChatDO } from '@/modules/chat/domain/chat/models/chat.entity';
import { MessageDO } from '@/modules/chat/domain/message/models/message.entity';
import { ChatDetailV1Dto } from '../dto/v1/chat-v1.dto';
import { ChatDetailV2Dto } from '../dto/v2/chat-v2.dto';
import { LegacyCompatibilityService } from './legacy-compatibility.service';

@Injectable()
export class ChatAggregateService {
  constructor(
    private readonly legacyCompatibilityService: LegacyCompatibilityService,
    private readonly commandBus: CommandBus,
  ) {}

  /**
   * 聚合JOIN查询结果为完整的Chat聚合
   * 将扁平的JOIN结果转换为嵌套的领域对象结构
   * Chat -> Messages[] -> CompletionBlocks[]
   */
  aggregateChatFromJoinResult(
    rows: Array<{
      chats: ChatDO;
      messages: MessageDO | null;
      blocks: CompletionBlockDO | null;
    }>,
  ): Chat {
    if (!rows || rows.length === 0) {
      throw new Error('Cannot aggregate empty join result');
    }

    // 1. 处理第一行以获取Chat基本信息
    const firstRow = rows[0];
    const chatData: ChatDO = {
      ...firstRow.chats,
      messages: [],
    };

    // 2. 聚合消息和块，使用Map避免重复
    const processedMessageIds = new Map<string, number>();
    const processedBlockIds = new Set<string>();

    // 遍历所有行来聚合消息和块
    for (const row of rows) {
      // 处理消息（如果存在且未处理过）
      if (row.messages && !processedMessageIds.has(row.messages.id)) {
        const messageData: MessageDO = {
          ...row.messages,
          blocks: [],
        };
        chatData.messages.push(messageData);
        processedMessageIds.set(row.messages.id, chatData.messages.length - 1);
      }

      // 处理完成块（如果存在且未处理过）
      if (row.blocks && row.messages && !processedBlockIds.has(row.blocks.id)) {
        const blockData: CompletionBlockDO = {
          ...row.blocks,
          extra: row.blocks.extra || {},
        };

        // 将块添加到对应的消息中
        chatData.messages[processedMessageIds.get(row.messages.id)!].blocks.push(blockData);
        processedBlockIds.add(row.blocks.id);
      }
    }

    // 3. 创建Chat聚合根并设置消息实体
    const chat = this.legacyCompatibilityService.createChatFromDO(chatData);
    return chat;
  }

  aggregateChatDtoFromJoinResult(
    rows: Array<{
      chats: ChatDO;
      messages: MessageDO | null;
      blocks: CompletionBlockDO | null;
    }>,
    version: 'v1' | 'v2' = 'v2',
  ): ChatDetailV1Dto | ChatDetailV2Dto {
    const chat = this.aggregateChatFromJoinResult(rows);
    return this.toChatDetailDto(chat, version);
  }

  /**
   * 聚合多个Chat的JOIN查询结果
   * 处理按最新消息活动倒序、chat创建时间倒序、message正序排列的查询结果
   * 返回按最新消息活动倒序排列的Chat聚合数组，每个Chat内的messages按正序排列
   *
   * @param rows JOIN查询结果
   * @param reverseMessages 是否反转消息顺序（对应youapp的selectDetailsForChatAssistant行为）
   */
  aggregateMultipleChatsFromJoinResult(
    rows: Array<{
      chats: ChatDO;
      messages: MessageDO | null;
      blocks: CompletionBlockDO | null;
    }>,
  ): Chat[] {
    if (!rows || rows.length === 0) {
      return [];
    }

    // 2. 聚合消息和块，使用Map避免重复
    const chatDOs: ChatDO[] = [];
    const processedChatIds = new Map<string, number>();
    const processedMessageIds = new Map<string, number>();
    const processedBlockIds = new Set<string>();

    // 遍历所有行来聚合消息和块
    for (const row of rows) {
      if (!processedChatIds.has(row.chats.id)) {
        const index =
          chatDOs.push({
            ...row.chats,
            messages: [],
          }) - 1;
        processedChatIds.set(row.chats.id, index);
      }

      // 处理消息（如果存在且未处理过）
      if (row.messages && !processedMessageIds.has(row.messages.id)) {
        const messageData: MessageDO = {
          ...row.messages,
          blocks: [],
        };
        if (!processedChatIds.has(row.chats.id)) {
          throw new Error(`Chat ${row.chats.id} was not processed`);
        }
        const index = chatDOs[processedChatIds.get(row.chats.id)!].messages.push(messageData) - 1;
        processedMessageIds.set(row.messages.id, index);
      }

      // 处理完成块（如果存在且未处理过）
      if (row.blocks && row.messages && !processedBlockIds.has(row.blocks.id)) {
        const blockData: CompletionBlockDO = {
          ...row.blocks,
          extra: row.blocks.extra || {},
        };
        if (!processedMessageIds.has(row.messages.id)) {
          throw new Error(`Message ${row.messages.id} was not processed`);
        }
        // 将块添加到对应的消息中
        chatDOs[processedChatIds.get(row.chats.id)!].messages[
          processedMessageIds.get(row.messages.id)!
        ].blocks.push(blockData);
        processedBlockIds.add(row.blocks.id);
      }
    }

    // 4. 创建Chat聚合根并设置消息实体
    return chatDOs.map((chatData) => this.legacyCompatibilityService.createChatFromDO(chatData));
  }

  aggregateMultipleChatDtosFromJoinResult(
    rows: Array<{
      chats: ChatDO;
      messages: MessageDO | null;
      blocks: CompletionBlockDO | null;
    }>,
    version: 'v1' | 'v2' = 'v2',
  ): Array<ChatDetailV1Dto | ChatDetailV2Dto> {
    const chats = this.aggregateMultipleChatsFromJoinResult(rows);
    return chats.map((chat) => this.toChatDetailDto(chat, version));
  }

  /**
   * 根据消息内容自动选择合适的组装器并组装聊天详情
   * DDD原则：领域服务封装业务决策逻辑
   *
   * 决策规则：
   * - 如果消息包含reasoning或event上下文（tool blocks），使用V1格式
   * - 否则使用V2格式
   *
   * @param chat 聊天聚合根
   * @param messages 消息列表（可选，如果不提供则使用chat.messages）
   * @returns 组装后的聊天详情DTO
   */
  toChatDetailDto(chat: Chat, version: 'v1' | 'v2' = 'v2'): ChatDetailV1Dto | ChatDetailV2Dto {
    console.log('has messages', chat.messages?.length);
    if (version === 'v1') {
      const chatDto = this.legacyCompatibilityService.convertChatV1DtoFromChat(chat);

      return chatDto;
    }

    const chatDto = this.legacyCompatibilityService.convertChatV2DtoFromChat(chat);

    return chatDto;
  }
}
