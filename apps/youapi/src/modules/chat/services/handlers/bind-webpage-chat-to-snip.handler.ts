/**
 * 绑定网页聊天到片段命令处理器
 * 处理将网页类型的聊天转换为片段类型的聊天的业务逻辑
 *
 * 业务逻辑：
 * 1. 获取聊天信息，验证是否为网页类型
 * 2. 更新聊天的 origin 为片段类型
 * 3. 如果提供了 boardId，同时更新 boardId
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { ChatOriginTypeEnum, ChatSnipOrigin } from '@/common/types';
import { ChatRepository } from '../../repositories/chat.repository';
import { BindWebpageChatToSnipCommand } from '../commands/bind-webpage-chat-to-snip.command';

@Injectable()
@CommandHandler(BindWebpageChatToSnipCommand)
export class BindWebpageChatToSnipHandler implements ICommandHandler<BindWebpageChatToSnipCommand> {
  private readonly logger = new Logger(BindWebpageChatToSnipHandler.name);

  constructor(private readonly chatRepository: ChatRepository) {}

  async execute(command: BindWebpageChatToSnipCommand): Promise<void> {
    const { chatId, snipId, boardId } = command;

    // 获取聊天信息
    const chat = await this.chatRepository.getById(chatId);

    // 只处理网页类型的聊天
    if (chat.origin.type !== ChatOriginTypeEnum.WEBPAGE) {
      this.logger.debug(`Chat ${chatId} is not a webpage chat, skipping binding`);
      return;
    }

    // 创建片段类型的 origin
    const snipOrigin: ChatSnipOrigin = {
      type: ChatOriginTypeEnum.SNIP,
      id: snipId,
    };

    // 更新聊天的 origin 和 boardId（如果提供）
    chat.updateOrigin(snipOrigin, boardId);

    // 持久化更改
    await this.chatRepository.save(chat);
    chat.commit();

    this.logger.log(
      `Successfully bound chat ${chatId} to snip ${snipId}${boardId ? ` with board ${boardId}` : ''}`,
    );
  }
}
