/**
 * Send Message Handler - 发送消息命令处理器
 * 处理发送消息的业务逻辑，验证权限，保存消息
 *
 * Migrated from:
 * - /Users/<USER>/Projects/github.com/YouMindInc/youapp/src/lib/app/chat/index.ts (chatWithMessage function)
 */

import { Injectable, Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { type Observable } from 'rxjs';
import { NotFound, ResourceEnum } from '@/common/errors';
import { YouapiClsService } from '@/common/services/cls.service';
import {
  CompletionStreamChunk,
  DEFAULT_AI_AGENT_MODEL,
  DEFAULT_AI_CHAT_MODEL,
  MessageModeEnum,
  StreamChunkUnion,
} from '@/common/types/chat.types';
import { AssistantMessage, UserMessage } from '../../domain/message/models/message.entity';
import { ChatRepository } from '../../repositories/chat.repository';
import { ChatAggregateService } from '../chat-aggregate.service';
import { SendMessageCommand } from '../commands/send-message.command';
import { getChatRunner } from '../runners';

@CommandHandler(SendMessageCommand)
@Injectable()
export class SendMessageHandler implements ICommandHandler<SendMessageCommand> {
  private readonly logger = new Logger(SendMessageHandler.name);

  constructor(
    private readonly chatRepository: ChatRepository,
    private readonly youapiClsService: YouapiClsService,
    private readonly chatAggregateService: ChatAggregateService,
  ) {}

  async execute(
    command: SendMessageCommand,
  ): Promise<Observable<CompletionStreamChunk<StreamChunkUnion>>> {
    const {
      userId,
      chatId,
      boardId,
      message,
      selection,
      atReferences,
      origin,
      messageMode = MessageModeEnum.ASK,
      tools,
      shortcut,
    } = command.param;

    // 1. 验证聊天是否存在且用户有权限访问
    const rawData = await this.chatRepository.findDetailRawDataById(chatId);
    const chat = await this.chatAggregateService.aggregateChatFromJoinResult(rawData);
    if (!chat) {
      throw new NotFound({
        resource: ResourceEnum.CHAT,
        id: chatId,
      });
    }

    // 2. 创建用户消息
    let chatModel = command.param.chatModel || DEFAULT_AI_CHAT_MODEL;
    if (messageMode === MessageModeEnum.AGENT) {
      chatModel = DEFAULT_AI_AGENT_MODEL;
      this.logger.debug(`Upgrading to ${DEFAULT_AI_AGENT_MODEL} for AGENT mode message`);
    }
    const userMessage = UserMessage.createNew({
      chatId: chat.id,
      message: message,
      origin: origin || chat.origin,
      selection: selection || '',
      atReferences: atReferences || [],
      tools: tools || {},
      mode: messageMode || MessageModeEnum.ASK,
      model: chatModel,
      command: command.param.command,
      shortcut,
      boardId,
    });
    const assistantMessage = AssistantMessage.createNewFromUserMessage(userMessage);
    assistantMessage.setTraceId(this.youapiClsService.getTraceId());

    chat.addMessage(userMessage);
    chat.addMessage(assistantMessage);

    const runner = getChatRunner(chat, userId);
    const observable = await runner.generate(command);
    return observable;
  }
}
