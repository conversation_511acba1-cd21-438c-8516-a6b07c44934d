import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { getNormalizedUrl } from '@repo/common/utilities/url';
import { YouapiSse } from '@/common/sse';
import { toV1CompatibleObservable } from '@/modules/chat/utils/toObservable';
import { BaseController } from '@/shared/base.controller';
// 领域对象
import { ReaderHTMLContent } from '../domain/snip/value-objects/content.vo';
import { WebpageMeta } from '../domain/snip/value-objects/webpage-meta.vo';
// Content DTOs
import { HtmlSelectionDto } from '../dto/shared/html-selection.dto';
import { AddPunctuationDto } from '../dto/snip/add-punctuation.dto';
import { ArticleDto } from '../dto/snip/article.dto';
// Snip DTOs
import { CreateArticleDto } from '../dto/snip/create-article.dto';
import { CreateImageDto } from '../dto/snip/create-image.dto';
import { CreateOfficeDto } from '../dto/snip/create-office.dto';
import { CreateOtherWebpageDto } from '../dto/snip/create-other-webpage.dto';
import { CreatePdfDto } from '../dto/snip/create-pdf.dto';
import { CreatePdfByUrlDto } from '../dto/snip/create-pdf-by-url.dto';
import { CreateSnippetDto } from '../dto/snip/create-snippet.dto';
import { CreateTextFileDto } from '../dto/snip/create-text-file.dto';
import { CreateVideoDto } from '../dto/snip/create-video.dto';
import { CreateVoiceDto } from '../dto/snip/create-voice.dto';
import { DeleteFormattedSubtitlesDto } from '../dto/snip/delete-formatted-subtitles.dto';
import { DeleteSnipDto } from '../dto/snip/delete-snip.dto';
import { DetectSpeakersDto, DetectSpeakersResponseDto } from '../dto/snip/detect-speakers.dto';
import { CreateUploadFileDto, UntransferredFileMetaDto } from '../dto/snip/file.dto';
import { GenerateImageInfoDto } from '../dto/snip/generate-image-info.dto';
import { GenerateOverviewDto } from '../dto/snip/generate-overview.dto';
import { GenerateTranscriptDto } from '../dto/snip/generate-transcript.dto';
import { ImageDto } from '../dto/snip/image.dto';
import {
  FormattedSubtitleContentDto,
  ListFormattedSubtitlesDto,
} from '../dto/snip/list-formatted-subtitles.dto';
import { ListSnipsByUrlDto } from '../dto/snip/list-snips-by-url.dto';
import { ListWebpagesByUrlsDto } from '../dto/snip/list-webpages-by-urls.dto';
import { ListWebpagesInBoardByUrlsDto } from '../dto/snip/list-webpages-in-board-by-urls.dto';
import { OfficeDto } from '../dto/snip/office.dto';
import { OtherWebpageDto } from '../dto/snip/other-webpage.dto';
import { PatchVideoTranscriptDto } from '../dto/snip/patch-video-transcript.dto';
import { PatchVoiceTranscriptDto } from '../dto/snip/patch-voice-transcript.dto';
import { PdfDto } from '../dto/snip/pdf.dto';
import { getSnipDtoArraySchema, getSnipDtoSchema, SnipDto } from '../dto/snip/snip.dto';
import {
  GetSnipDto,
  GetSnipsDto,
  GetWebpageByNormalizedUrlDto,
  ListSnipsDto,
} from '../dto/snip/snip-query.dto';
import {
  ArticleDtoWithBlocks,
  ArticleDtoWithBlocksAndBoards,
  getSnipWithBlocksAndBoardsSchema,
  getSnipWithBlocksArraySchema,
  getSnipWithBlocksSchema,
  ImageDtoWithBlocks,
  ImageDtoWithBlocksAndBoards,
  OfficeDtoWithBlocks,
  OfficeDtoWithBlocksAndBoards,
  OtherWebpageDtoWithBlocks,
  OtherWebpageDtoWithBlocksAndBoards,
  PdfDtoWithBlocks,
  PdfDtoWithBlocksAndBoards,
  SnipDtoWithBlocks,
  SnipDtoWithBlocksAndBoards,
  SnippetDtoWithBlocks,
  SnippetDtoWithBlocksAndBoards,
  TextDtoWithBlocks,
  TextDtoWithBlocksAndBoards,
  UnknownWebpageDtoWithBlocks,
  UnknownWebpageDtoWithBlocksAndBoards,
  VideoDtoWithBlocks,
  VideoDtoWithBlocksAndBoards,
  VoiceDtoWithBlocks,
  VoiceDtoWithBlocksAndBoards,
} from '../dto/snip/snip-with-blocks.dto';
import { SnippetDto } from '../dto/snip/snippet.dto';
import { TextDto } from '../dto/snip/text.dto';
import { TryCreateSnipByUrlDto } from '../dto/snip/try-create-snip-by-url.dto';
import { UnknownWebpageDto } from '../dto/snip/unknown-webpage.dto';
import { UpdateImageDto } from '../dto/snip/update-image.dto';
import { UpdateSnipPlayUrlDto } from '../dto/snip/update-snip-play-url.dto';
import { UpdateSnipTitleDto } from '../dto/snip/update-snip-title.dto';
import {
  UpdateTranscriptSpeakerDto,
  UpdateTranscriptSpeakerResponseDto,
} from '../dto/snip/update-transcript-speaker.dto';
import { VideoDto } from '../dto/snip/video.dto';
import { VoiceDto } from '../dto/snip/voice.dto';
import { WebpageMetaDto } from '../dto/snip/webpage-meta.dto';
import { SnipContentDto } from '../dto/snip-content.dto';
import { AddPunctuationCommand } from '../services/commands/snip/add-punctuation.command';
import { CallOverviewCommand } from '../services/commands/snip/call-overview.command';
// Snip Commands
import { CreateArticleCommand } from '../services/commands/snip/create-article.command';
import { CreateImageCommand } from '../services/commands/snip/create-image.command';
import { CreateOfficeCommand } from '../services/commands/snip/create-office.command';
import { CreateOtherWebpageCommand } from '../services/commands/snip/create-other-webpage.command';
import { CreatePdfCommand } from '../services/commands/snip/create-pdf.command';
import { CreatePdfByUrlCommand } from '../services/commands/snip/create-pdf-by-url.command';
import { CreateBlockContentParam } from '../services/commands/snip/create-snip-blocks.command';
import { CreateSnippetCommand } from '../services/commands/snip/create-snippet.command';
import { CreateTextFileCommand } from '../services/commands/snip/create-text-file.command';
import { CreateVideoCommand } from '../services/commands/snip/create-video.command';
import { CreateVoiceCommand } from '../services/commands/snip/create-voice.command';
import { DeleteFormattedSubtitlesCommand } from '../services/commands/snip/delete-formatted-subtitles.command';
import { DeleteSnipCommand } from '../services/commands/snip/delete-snip.command';
import { DetectSpeakersCommand } from '../services/commands/snip/detect-speakers.command';
import { GenerateImageInfoCommand } from '../services/commands/snip/generate-image-info.command';
import { GenerateTranscriptCommand } from '../services/commands/snip/generate-transcript.command';
import { PatchVideoTranscriptCommand } from '../services/commands/snip/patch-video-transcript.command';
import { PatchVoiceTranscriptCommand } from '../services/commands/snip/patch-voice-transcript.command';
import { TryCreateSnipByUrlCommand } from '../services/commands/snip/try-create-snip-by-url.command';
import { UpdateImageCommand } from '../services/commands/snip/update-image.command';
import { UpdateSnipPlayUrlCommand } from '../services/commands/snip/update-snip-play-url.command';
import { UpdateSnipTitleCommand } from '../services/commands/snip/update-snip-title.command';
import { UpdateTranscriptSpeakerCommand } from '../services/commands/snip/update-transcript-speaker.command';
// Services
import { SnipDtoService } from '../services/dto-services/snip-dto.service';
// Snip Queries
import { GetSnipQuery } from '../services/queries/snip/get-snip.query';
import { GetSnipsQuery } from '../services/queries/snip/get-snips.query';
import { GetWebpageByNormalizedUrlQuery } from '../services/queries/snip/get-webpage-by-normalized-url.query';
import { ListFormattedSubtitlesQuery } from '../services/queries/snip/list-formatted-subtitles.query';
import { ListSnipsQuery } from '../services/queries/snip/list-snips.query';
import { ListSnipsByUrlQuery } from '../services/queries/snip/list-snips-by-url.query';
import { ListWebpagesByUrlsQuery } from '../services/queries/snip/list-webpages-by-urls.query';
import { ListWebpagesInBoardByUrlsQuery } from '../services/queries/snip/list-webpages-in-board-by-urls.query';

/**
 * Snip Controller - 处理所有 snip 相关的 API 操作
 * 迁移自 youapp 的 snip API，包括内容创建、管理和查询功能
 *
 * 支持的内容类型：
 * - 基础内容：网页片段、文章、图片、视频、语音
 * - 文档类：Office文档、PDF、文本文件
 * - 特殊类型：在线视频、播客
 */
@ApiTags('Snip')
@Controller('api/v1')
@ApiExtraModels(
  CreateUploadFileDto,
  UntransferredFileMetaDto,
  CreateBlockContentParam,
  HtmlSelectionDto,
  ArticleDto,
  ImageDto,
  VoiceDto,
  VideoDto,
  OfficeDto,
  PdfDto,
  TextDto,
  SnippetDto,
  OtherWebpageDto,
  UnknownWebpageDto,
  // WithBlocks 版本的类 - 确保 Swagger 能发现这些动态创建的类
  ArticleDtoWithBlocks,
  SnippetDtoWithBlocks,
  ImageDtoWithBlocks,
  VoiceDtoWithBlocks,
  VideoDtoWithBlocks,
  PdfDtoWithBlocks,
  OfficeDtoWithBlocks,
  TextDtoWithBlocks,
  OtherWebpageDtoWithBlocks,
  UnknownWebpageDtoWithBlocks,
  // WithBlocksAndBoards 版本的类 - 用于包含 boards 信息的响应
  ArticleDtoWithBlocksAndBoards,
  SnippetDtoWithBlocksAndBoards,
  ImageDtoWithBlocksAndBoards,
  VoiceDtoWithBlocksAndBoards,
  VideoDtoWithBlocksAndBoards,
  PdfDtoWithBlocksAndBoards,
  OfficeDtoWithBlocksAndBoards,
  TextDtoWithBlocksAndBoards,
  OtherWebpageDtoWithBlocksAndBoards,
  UnknownWebpageDtoWithBlocksAndBoards,
)
export class SnipController extends BaseController {
  constructor(private readonly snipDtoService: SnipDtoService) {
    super();
  }

  // =============================================================================
  // 基础内容创建接口 (Create APIs)
  // =============================================================================

  @Post(['createSnippet', 'createTextSnippet'])
  @HttpCode(200)
  @ApiOperation({
    summary: '创建网页片段',
    description: '创建新的网页片段，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: SnippetDto })
  async createSnippet(@Body() dto: CreateSnippetDto): Promise<SnippetDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CreateSnippetCommand(
      spaceId,
      userId,
      dto.webpage,
      dto.content,
      dto.selection,
      dto.annotation,
      dto.parentId,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    const snippet = await this.commandBus.execute(command);
    return snippet;
  }

  @Post('createArticle')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建文章',
    description: '创建新的文章内容，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: ArticleDto })
  async createArticle(@Body() dto: CreateArticleDto): Promise<ArticleDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    // // 将 DTO 转换为领域对象
    const webpage = this.convertWebpageMetaDto(dto.webpage);
    const content = this.convertSnipContentDto(dto.content);

    const command = new CreateArticleCommand(
      spaceId,
      userId,
      dto.title,
      webpage,
      content,
      dto.authors,
      dto.heroImageUrl,
      dto.publishedAt,
      dto.playUrl,
      dto.extra,
      dto.overviewContents,
      dto.transcriptContents,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return await this.commandBus.execute(command);
  }

  @Post(['createImage', 'createImageSnippet'])
  @HttpCode(200)
  @ApiOperation({
    summary: '创建图片',
    description: '创建新的图片内容，支持上传文件或网络图片，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: ImageDto })
  async createImage(@Body() dto: CreateImageDto): Promise<ImageDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CreateImageCommand(
      spaceId,
      userId,
      dto.title,
      dto.extra,
      dto.file,
      dto.webpage,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return this.commandBus.execute(command);
  }

  @Post(['createVideo', 'createOnlineVideo'])
  @HttpCode(200)
  @ApiOperation({
    summary: '创建视频',
    description: '创建新的视频内容，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: VideoDto })
  async createVideo(@Body() dto: CreateVideoDto): Promise<VideoDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    // 将 DTO 转换为领域对象
    const webpage = this.convertWebpageMetaDto(dto.webpage);
    const description = this.convertSnipContentDto(dto.description);

    const command = new CreateVideoCommand(
      spaceId,
      userId,
      dto.title,
      webpage,
      description,
      dto.authors,
      dto.heroImageUrl,
      dto.publishedAt,
      dto.playUrl,
      dto.extra,
      dto.overviewContents,
      dto.transcriptContents,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return await this.commandBus.execute(command);
  }

  @Post(['createVoice', 'createPodcast'])
  @HttpCode(200)
  @ApiOperation({
    summary: '创建语音',
    description: '创建新的语音内容，支持本地文件和在线播客，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: VoiceDto })
  async createVoice(@Body() dto: CreateVoiceDto): Promise<VoiceDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    // 将 DTO 转换为领域对象
    const webpage = dto.webpage ? this.convertWebpageMetaDto(dto.webpage) : undefined;
    const showNotes = dto.showNotes ? this.convertSnipContentDto(dto.showNotes) : undefined;

    const command = new CreateVoiceCommand(
      spaceId,
      userId,
      dto.title,
      webpage,
      showNotes,
      dto.authors,
      dto.heroImageUrl,
      dto.publishedAt,
      dto.playUrl,
      dto.extra,
      dto.overviewContents,
      dto.transcriptContents,
      dto.file,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return await this.commandBus.execute(command);
  }

  // =============================================================================
  // 文档类创建接口
  // =============================================================================

  @Post('createOffice')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建Office文档',
    description: '创建Office文档（Word, PowerPoint, Excel），支持Azure文档解析和绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: OfficeDto })
  async createOffice(@Body() dto: CreateOfficeDto): Promise<OfficeDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CreateOfficeCommand(
      spaceId,
      userId,
      dto.file,
      dto.title,
      dto.syncTranscribe,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return await this.commandBus.execute(command);
  }

  @Post('createTextFile')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建文本文档',
    description: '创建文本文档 (TXT, Markdown, SRT)，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: TextDto })
  async createTextFile(@Body() dto: CreateTextFileDto): Promise<TextDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CreateTextFileCommand(
      spaceId,
      userId,
      dto.file,
      dto.content,
      dto.title,
      dto.fileType,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return await this.commandBus.execute(command);
  }

  @Post(['createPDF', 'snip/createPDF'])
  @HttpCode(200)
  @ApiOperation({
    summary: '创建PDF文档',
    description: '创建PDF文档，支持文件上传和URL两种方式，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: PdfDto })
  async createPDF(@Body() dto: CreatePdfDto): Promise<PdfDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CreatePdfCommand(
      spaceId,
      userId,
      dto.file,
      dto.title,
      dto.authors,
      dto.publishedAt,
      dto.heroImageUrl,
      dto.extra,
      dto.syncTranscribe,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return await this.commandBus.execute(command);
  }

  @Post('createPDFbyUrl')
  @HttpCode(200)
  @ApiOperation({
    summary: '通过URL创建PDF',
    description: '通过URL创建PDF文档，支持从网络URL下载PDF文件并创建，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: PdfDto })
  async createPDFbyUrl(@Body() dto: CreatePdfByUrlDto): Promise<PdfDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CreatePdfByUrlCommand(
      spaceId,
      userId,
      dto.webpage.url,
      dto.extra,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return await this.commandBus.execute(command);
  }

  // =============================================================================
  // 网页类创建接口
  // =============================================================================

  @Post('createOtherWebpage')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建其他网页',
    description: '创建其他类型网页，支持绑定聊天会话',
  })
  @ApiResponse({ status: 200, description: '创建成功', type: OtherWebpageDto })
  async createOtherWebpage(@Body() dto: CreateOtherWebpageDto): Promise<OtherWebpageDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    // 将 DTO 转换为领域对象
    const webpage = this.convertWebpageMetaDto(dto.webpage);

    const command = new CreateOtherWebpageCommand(
      spaceId,
      userId,
      webpage,
      dto.extra,
      dto.overviewContents,
      dto.transcriptContents,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.chatId,
    );

    return await this.commandBus.execute(command);
  }

  // =============================================================================
  // 特殊类型创建接口
  // =============================================================================
  @Post('tryCreateSnipByUrl')
  @HttpCode(200)
  @ApiOperation({
    summary: '通过URL创建资源',
    description: '智能解析URL并创建对应类型的资源（图片、音频、网页、PDF等），支持YouMind分享链接',
  })
  @ApiResponse({
    status: 200,
    description: '创建成功，返回创建的资源信息',
    type: Object,
  })
  async tryCreateSnipByUrl(
    @Body() dto: TryCreateSnipByUrlDto,
  ): Promise<ImageDto | VoiceDto | PdfDto | UnknownWebpageDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new TryCreateSnipByUrlCommand(
      spaceId,
      userId,
      dto.url,
      dto.boardId,
      dto.parentBoardGroupId,
      dto.title,
      dto.albumUrlForVoiceSnip,
    );

    return this.commandBus.execute(command);
  }

  // =============================================================================
  // Snip管理接口 - 查询接口
  // =============================================================================

  @Post('snip/getSnip')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取单个资源',
    description: '根据ID获取指定的snip资源详情',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功，返回包含 blocks 信息的 snip 详情',
    schema: getSnipWithBlocksSchema(),
  })
  async getSnip(@Body() dto: GetSnipDto): Promise<SnipDtoWithBlocks> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new GetSnipQuery(dto.id, spaceId, userId);
    return this.queryBus.execute(query);
  }

  @Post('listSnips')
  @HttpCode(200)
  @ApiOperation({
    summary: '列出资源列表',
    description: '获取当前用户空间内的所有snip资源列表',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: getSnipDtoArraySchema(),
  })
  async listSnips(@Body() dto: ListSnipsDto): Promise<SnipDto[]> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new ListSnipsQuery(
      spaceId,
      userId,
      dto.ids,
      dto.type,
      dto.from,
      dto.site,
      dto.createdBefore,
      dto.createdAfter,
      dto.normalizedUrl,
    );

    return this.queryBus.execute(query);
  }

  @Post('listSnipsByUrl')
  @HttpCode(200)
  @ApiOperation({
    summary: '通过 URL 查询用户的 Snips',
    description: '通过 URL 查询用户的 Snips',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: getSnipDtoArraySchema(),
  })
  async listSnipsByUrl(@Body() dto: ListSnipsByUrlDto): Promise<SnipDto[]> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new ListSnipsByUrlQuery(dto.url, spaceId, userId);
    const snips = await this.queryBus.execute(query);

    return this.snipDtoService.toDtoList(snips);
  }

  // =============================================================================
  // Snip管理接口 - 删除接口
  // =============================================================================

  @Post('deleteSnip')
  @HttpCode(200)
  @ApiOperation({
    summary: '删除资源',
    description: '软删除指定的snip资源',
  })
  @ApiResponse({ status: 200, description: '删除成功' })
  async deleteSnip(@Body() dto: DeleteSnipDto): Promise<void> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new DeleteSnipCommand(dto.id, spaceId, userId);
    await this.commandBus.execute(command);
  }

  // =============================================================================
  // Snip管理接口 - 更新接口
  // =============================================================================

  @Post('snip/updateSnipTitle')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新资源标题',
    description: '更新指定snip资源的标题',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    schema: getSnipDtoSchema(),
  })
  async updateSnipTitle(@Body() dto: UpdateSnipTitleDto): Promise<SnipDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new UpdateSnipTitleCommand(userId, spaceId, dto.id, dto.title);
    return this.commandBus.execute(command);
  }

  @Post('snip/updateSnipPlayUrl')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新播放URL',
    description: '更新指定snip资源的播放URL',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    schema: getSnipDtoSchema(),
  })
  async updateSnipPlayUrl(@Body() dto: UpdateSnipPlayUrlDto): Promise<SnipDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new UpdateSnipPlayUrlCommand(userId, spaceId, dto.id, dto.playUrl, dto.extra);
    return this.commandBus.execute(command);
  }

  // =============================================================================
  // 转录相关接口
  // =============================================================================

  @Post('snip/listFormattedSubtitles')
  @HttpCode(200)
  @ApiOperation({
    summary: '列出格式化字幕',
    description: '列出指定资源的格式化字幕，支持按语言过滤',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [FormattedSubtitleContentDto],
  })
  async listFormattedSubtitles(
    @Body() dto: ListFormattedSubtitlesDto,
  ): Promise<FormattedSubtitleContentDto[]> {
    const query = new ListFormattedSubtitlesQuery(dto.snipId, dto.language);
    return this.queryBus.execute(query);
  }

  @Post('snip/deleteFormattedSubtitles')
  @HttpCode(200)
  @ApiOperation({
    summary: '删除格式化字幕',
    description: '删除指定资源的格式化字幕，支持按语言过滤',
  })
  @ApiResponse({
    status: 200,
    description: '删除成功',
    type: Number,
  })
  async deleteFormattedSubtitles(@Body() dto: DeleteFormattedSubtitlesDto): Promise<number> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new DeleteFormattedSubtitlesCommand(dto.snipId, spaceId, userId, dto.language);

    return await this.commandBus.execute(command);
  }

  @Post('snip/updateTranscriptSpeaker')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新转录说话人',
    description: '更新转录文本中的说话人信息，支持批量替换说话人标识',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: UpdateTranscriptSpeakerResponseDto,
  })
  async updateTranscriptSpeaker(
    @Body() dto: UpdateTranscriptSpeakerDto,
  ): Promise<UpdateTranscriptSpeakerResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new UpdateTranscriptSpeakerCommand(dto.id, dto.speakerUpdates, userId, spaceId);

    await this.commandBus.execute(command);

    return {
      success: true,
      message: 'Transcript speakers updated successfully',
    };
  }

  @Post('snip/updateImage')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新图片信息',
    description: '更新图片的标题、描述和提取文本等信息',
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: ImageDto,
  })
  async updateImage(@Body() dto: UpdateImageDto): Promise<ImageDto> {
    const userId = this.getUserId();

    const command = new UpdateImageCommand(
      userId,
      dto.id,
      dto.title,
      dto.description,
      dto.extractedText,
    );

    return this.commandBus.execute(command);
  }

  @Post('snip/detectSpeakers')
  @HttpCode(200)
  @ApiOperation({
    summary: '检测说话人',
    description: '检测音视频中的说话人，通过AI分析转录文本识别不同的说话人',
  })
  @ApiResponse({
    status: 200,
    description: '检测成功',
    type: DetectSpeakersResponseDto,
  })
  async detectSpeakers(@Body() dto: DetectSpeakersDto): Promise<DetectSpeakersResponseDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new DetectSpeakersCommand(dto.transcript, dto.snipId, userId, spaceId);

    const speakers = await this.commandBus.execute(command);
    return { speakers };
  }

  @Post('snip/addPunctuation')
  @HttpCode(200)
  @ApiOperation({
    summary: '添加标点符号',
    description: '为转录文本添加标点符号，使用AI处理字幕内容',
  })
  @ApiResponse({ status: 200, description: '添加成功' })
  async addPunctuation(@Body() dto: AddPunctuationDto): Promise<void> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new AddPunctuationCommand(
      dto.snipId,
      dto.blockId,
      dto.language,
      dto.regenerate || false,
      userId,
      spaceId,
    );

    await this.commandBus.execute(command);
  }

  // =============================================================================
  // 内容处理接口
  // =============================================================================

  @Post('snip/generateOverview')
  @HttpCode(200)
  @ApiOperation({
    summary: '生成概览',
    description: '为指定资源生成概览信息',
  })
  @ApiResponse({ status: 200, description: '生成成功' })
  @YouapiSse()
  async generateOverview(@Body() dto: GenerateOverviewDto) {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CallOverviewCommand(
      dto.snipId,
      userId,
      spaceId,
      dto.responseLanguage,
      dto.secondResponseLanguage, // 支持双语
      dto.regenerate || false,
    );

    // 获取生成器实例
    const observable = await this.commandBus.execute(command);
    return toV1CompatibleObservable(observable);
  }

  @Post('snip/generateTranscript')
  @HttpCode(200)
  @ApiOperation({
    summary: '生成转录',
    description: '为指定音频/视频资源生成转录内容',
  })
  @ApiResponse({ status: 200, description: '生成成功' })
  @YouapiSse()
  async generateTranscript(@Body() dto: GenerateTranscriptDto) {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new GenerateTranscriptCommand(
      userId,
      spaceId,
      dto.snipId,
      dto.responseLanguage,
      dto.speakerLabels,
      dto.regenerate,
    );

    const observable = await this.commandBus.execute(command);
    return toV1CompatibleObservable(observable);
  }

  @Post('snip/generateImageInfo')
  @HttpCode(200)
  @ApiOperation({
    summary: '生成图片信息',
    description: '使用 AI 为图片资源生成标题和描述信息',
  })
  @ApiResponse({
    status: 200,
    description: '生成成功',
    schema: getSnipDtoSchema(),
  })
  async generateImageInfo(@Body() dto: GenerateImageInfoDto): Promise<SnipDto> {
    const userId = this.getUserId();

    const command = new GenerateImageInfoCommand(dto.id, userId);
    return this.commandBus.execute(command);
  }

  @Post('snip/getSnips')
  @HttpCode(200)
  @ApiOperation({
    summary: '批量获取Snip详情',
    description: '根据ID列表批量获取Snip详细信息，包含overview和transcript blocks',
  })
  @ApiResponse({
    status: 200,
    description: '返回Snip详情列表（包含overview和transcript blocks）',
    schema: getSnipWithBlocksArraySchema(),
  })
  async getSnips(@Body() dto: GetSnipsDto): Promise<SnipDtoWithBlocks[]> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new GetSnipsQuery(dto.ids, spaceId, userId);
    return this.queryBus.execute(query);
  }

  @Post('snip/listWebpagesByUrls')
  @HttpCode(200)
  @ApiOperation({
    summary: '根据URL列表获取网页',
    description: '根据URL列表获取对应的网页资源',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: getSnipDtoArraySchema(),
  })
  async listWebpagesByUrls(@Body() dto: ListWebpagesByUrlsDto): Promise<SnipDto[]> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();
    const query = new ListWebpagesByUrlsQuery(dto.urls, spaceId, userId);
    return this.queryBus.execute(query);
  }

  @Post('snip/listWebpagesInBoardByUrls')
  @HttpCode(200)
  @ApiOperation({
    summary: '根据URL列表获取看板中的网页',
    description: '根据URL列表获取指定看板中的网页资源',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: getSnipDtoArraySchema(),
  })
  async listWebpagesInBoardByUrls(@Body() dto: ListWebpagesInBoardByUrlsDto): Promise<SnipDto[]> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();
    const query = new ListWebpagesInBoardByUrlsQuery(dto.boardId, dto.urls, spaceId, userId);
    return this.queryBus.execute(query);
  }

  @Post('tryGetWebpageByNormalizedUrl')
  @HttpCode(200)
  @ApiOperation({
    summary: '通过规范化URL获取网页',
    description:
      '尝试通过规范化URL查询用户的网页片段，包含overview、transcript blocks和关联的boards',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功，返回网页信息（包含blocks和boards）或null',
    schema: {
      oneOf: [getSnipWithBlocksAndBoardsSchema(), { type: 'null' }],
    },
  })
  async tryGetWebpageByNormalizedUrl(
    @Body() dto: GetWebpageByNormalizedUrlDto,
  ): Promise<SnipDtoWithBlocksAndBoards | null> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new GetWebpageByNormalizedUrlQuery(dto.normalizedUrl, spaceId, userId);
    return this.queryBus.execute(query);
  }

  // =============================================================================
  // 其他相关接口
  // =============================================================================

  @Post('patchVoiceTranscript')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新语音字幕',
    description: '更新语音字幕，支持通过其他途径转录出来字幕并挂到某个 snip 下',
  })
  @ApiResponse({ status: 200, description: 'OK' })
  async patchVoiceTranscript(@Body() dto: PatchVoiceTranscriptDto): Promise<void> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new PatchVoiceTranscriptCommand(
      userId,
      spaceId,
      dto.snipId,
      dto.subtitleFileUrl,
      dto.transcript,
    );

    await this.commandBus.execute(command);
  }

  @Post('patchVideoTranscript')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新视频字幕',
    description: '更新视频字幕，支持 SRT 格式的字幕文件并挂到某个 snip 下',
  })
  @ApiResponse({ status: 200, description: 'OK' })
  async patchVideoTranscript(@Body() dto: PatchVideoTranscriptDto): Promise<void> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new PatchVideoTranscriptCommand(
      userId,
      spaceId,
      dto.snipId,
      dto.transcript,
      dto.language,
    );

    await this.commandBus.execute(command);
  }

  // =============================================================================
  // 私有辅助方法 - DTO 到领域对象转换
  // =============================================================================

  /**
   * 将 WebpageMetaDto 转换为 WebpageMeta 领域对象
   */
  private convertWebpageMetaDto(dto: WebpageMetaDto): WebpageMeta {
    return new WebpageMeta(
      dto.url,
      dto.normalizedUrl || getNormalizedUrl(dto.url), // 如果没有提供 normalizedUrl，使用原始 url
      dto.title,
      dto.description,
      {
        name: dto.site.name,
        host: dto.site.host,
        faviconUrl: dto.site.faviconUrl,
      },
    );
  }

  /**
   * 将 SnipContentDto 转换为 ReaderHTMLContent 领域对象
   */
  private convertSnipContentDto(dto: SnipContentDto): ReaderHTMLContent {
    return new ReaderHTMLContent(dto.raw, dto.plain, dto.language);
  }
}
