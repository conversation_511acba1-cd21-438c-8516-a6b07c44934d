/**
 * Webhook Controller - 处理外部服务回调
 * 对应 youapp 中的 webhook 路由，包括抓取、解析、转存等事件
 */

import { Body, Controller, HttpCode, Logger, Post, Put, Query, Req } from '@nestjs/common';
import {
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import type { Request } from 'express';
import { PublicRoute } from '@/common/decorators/public.decorator';
import { runInBackground } from '@/common/errors/error-handler';
import { FindSpaceByUserIdQuery } from '@/modules/iam/services/queries/space/find-space-by-user-id.query';
import { BaseController } from '@/shared/base.controller';
import type { WebhookFetchedRequestDto } from '../dto/webhook/webhook-fetched.dto';
import {
  WebhookFetchedResponseDto,
  YougetArticleFetchedEventDto,
  YougetErrorEventDto,
  YougetOtherWebpageFetchedEventDto,
  YougetVideoFetchedEventDto,
  YougetVoiceFetchedEventDto,
} from '../dto/webhook/webhook-fetched.dto';
import type { WebhookImagesTransferedRequestDto } from '../dto/webhook/webhook-images-transfered.dto';
import {
  WebhookImagesTransferedResponseDto,
  YougetTransferedFileMetaDto,
} from '../dto/webhook/webhook-images-transfered.dto';
import type { WebhookOfficeParsedRequestDto } from '../dto/webhook/webhook-office-parsed.dto';
import {
  WebhookOfficeParsedResponseDto,
  YougetOfficeParsedEventDto,
} from '../dto/webhook/webhook-office-parsed.dto';
import type { WebhookPdfParsedRequestDto } from '../dto/webhook/webhook-pdf-parsed.dto';
import {
  WebhookPdfParsedResponseDto,
  YougetPdfParsedEventDto,
} from '../dto/webhook/webhook-pdf-parsed.dto';
import { WebhookResendRequestDto } from '../dto/webhook/webhook-resend.dto';
import type { WebhookSubtitleTranscribedRequestDto } from '../dto/webhook/webhook-subtitle-transcribed.dto';
import {
  SubtitleTranscribedQueryParamsDto,
  YougetSubtitleTranscribedEventDto,
} from '../dto/webhook/webhook-subtitle-transcribed.dto';
import { WebhookFetchedCommand } from '../services/commands/webhook/webhook-fetched.command';
import { WebhookImagesTransferedCommand } from '../services/commands/webhook/webhook-images-transfered.command';
import { WebhookOfficeParsedCommand } from '../services/commands/webhook/webhook-office-parsed.command';
import { WebhookPdfParsedCommand } from '../services/commands/webhook/webhook-pdf-parsed.command';
import { WebhookSubtitleTranscribedCommand } from '../services/commands/webhook/webhook-subtitle-transcribed.command';
import { GetUserIdFromSnipQuery } from '../services/queries/snip/get-user-id-from-snip.query';

@ApiTags('Webhook')
@Controller('webhook/v1')
@ApiExtraModels(
  YougetArticleFetchedEventDto,
  YougetVoiceFetchedEventDto,
  YougetVideoFetchedEventDto,
  YougetOtherWebpageFetchedEventDto,
  YougetErrorEventDto,
  YougetTransferedFileMetaDto,
  YougetOfficeParsedEventDto,
  YougetPdfParsedEventDto,
  YougetSubtitleTranscribedEventDto,
  SubtitleTranscribedQueryParamsDto,
)
export class WebhookController extends BaseController {
  private static readonly logger = new Logger(WebhookController.name);

  /**
   * 从 snip 塞入 userId 到 clsService
   * @param snipId
   * @returns
   */
  async buildContext(snipId: string) {
    if (super.findUserId()) {
      return;
    }

    const userId = await this.queryBus.execute(new GetUserIdFromSnipQuery(snipId));
    if (!userId) {
      throw new Error('UserId not found in snip');
    }

    const space = await this.queryBus.execute(new FindSpaceByUserIdQuery(userId));
    if (!space) {
      throw new Error('Space not found for user');
    }

    this.clsService.setUserId(userId);
    this.clsService.setSpaceId(space.id);
  }

  @PublicRoute()
  @Put('images-transfered')
  @HttpCode(200)
  @ApiOperation({
    summary: '处理图片转存任务完成事件',
    description: '处理图片转存任务完成事件',
  })
  @ApiQuery({ name: 'snip_id', required: true, description: 'Snip ID' })
  @ApiResponse({ status: 200, description: 'OK', type: WebhookImagesTransferedResponseDto })
  @ApiBody({
    description: '图片转存结果数据',
    schema: {
      type: 'object',
    },
  })
  async handleImagesTransferedEvent(
    @Query('snip_id') snipId: string,
    @Body() requestBody: WebhookImagesTransferedRequestDto,
    @Req() _request: Request,
  ): Promise<void> {
    if (snipId) {
      await this.buildContext(snipId);
      WebhookController.logger.debug('handleImagesTransferedEvent', snipId, requestBody);
      runInBackground(
        this.commandBus.execute(new WebhookImagesTransferedCommand(snipId, requestBody)),
      );
    }
  }

  @PublicRoute()
  @Put('office-parsed')
  @HttpCode(200)
  @ApiOperation({
    summary: '处理 Office 文档解析完成事件',
    description: '处理 Office 文档解析完成事件',
  })
  @ApiQuery({ name: 'snip_id', required: true, description: 'Snip ID' })
  @ApiResponse({ status: 200, description: 'OK', type: WebhookOfficeParsedResponseDto })
  @ApiBody({
    description: 'Office 文档解析结果数据',
    schema: {
      type: 'object',
    },
  })
  async handleOfficeParsedEvent(
    @Query('snip_id') snipId: string,
    @Body() requestBody: WebhookOfficeParsedRequestDto,
    @Req() _request: Request,
  ): Promise<void> {
    if (snipId) {
      await this.buildContext(snipId);
      WebhookController.logger.debug('handleOfficeParsedEvent', snipId, requestBody);
      runInBackground(this.commandBus.execute(new WebhookOfficeParsedCommand(snipId, requestBody)));
    }
  }

  @PublicRoute()
  @Put('pdf-parsed')
  @HttpCode(200)
  @ApiOperation({
    summary: '处理 PDF 文档解析完成事件',
    description: '处理 PDF 文档解析完成事件',
  })
  @ApiQuery({ name: 'snip_id', required: true, description: 'Snip ID' })
  @ApiResponse({ status: 200, description: 'OK', type: WebhookPdfParsedResponseDto })
  @ApiBody({
    description: 'PDF 解析结果数据',
    schema: {
      type: 'object',
    },
  })
  async handlePdfParsedEvent(
    @Query('snip_id') snipId: string,
    @Body() requestBody: WebhookPdfParsedRequestDto,
    @Req() _request: Request,
  ): Promise<void> {
    if (snipId) {
      await this.buildContext(snipId);
      WebhookController.logger.debug('handlePdfParsedEvent', snipId, requestBody);
      runInBackground(this.commandBus.execute(new WebhookPdfParsedCommand(snipId, requestBody)));
    }
  }

  @PublicRoute()
  @Put('subtitle-transcribed')
  @HttpCode(200)
  @ApiOperation({
    summary: '处理字幕转录完成事件',
    description: '处理字幕转录完成事件',
  })
  @ApiResponse({ status: 200, description: 'OK' })
  @ApiBody({
    description: '字幕转录结果数据',
    schema: {
      type: 'object',
    },
  })
  async handleSubtitleTranscribedEvent(
    @Query() queryParams: SubtitleTranscribedQueryParamsDto,
    @Body() requestBody?: WebhookSubtitleTranscribedRequestDto,
    @Req() request?: Request,
  ): Promise<void> {
    const { snipId } = queryParams;
    if (!snipId) {
      WebhookController.logger.error('snipId is required', queryParams);
      return;
    }

    await this.buildContext(snipId);

    WebhookController.logger.debug(
      'handleSubtitleTranscribedEvent',
      snipId,
      queryParams,
      requestBody,
    );

    runInBackground(
      this.commandBus.execute(
        new WebhookSubtitleTranscribedCommand(
          snipId,
          queryParams,
          requestBody,
          request?.headers['x-youget-partial-result'] !== '1', // 考虑到 null 的情况，就不用 !== 来判断了
        ),
      ),
    );
  }

  @PublicRoute()
  @Put('fetched')
  @HttpCode(200)
  @ApiOperation({
    summary: '处理内容抓取完成事件',
    description: '处理内容抓取完成事件，将 UnknownWebpage 转换为具体的内容类型',
  })
  @ApiQuery({ name: 'snip_id', required: true, description: 'Snip ID' })
  @ApiResponse({ status: 200, description: 'OK', type: WebhookFetchedResponseDto })
  // 前端传 any 就行了，不折腾，原因见下
  @ApiBody({
    description: '内容抓取结果数据',
    schema: {
      type: 'object',
    },
  })
  // 不能加这个，因为 requestBody 没法区分，除非前端也塞入一个 $class 字段，但这样就太麻烦了，前端传个 any 就行
  // @ApiBody({
  //   schema: {
  //     title: 'WebhookFetchedRequestDto',
  //     oneOf: [
  //       { $ref: getSchemaPath(YougetArticleFetchedEventDto) },
  //       { $ref: getSchemaPath(YougetVoiceFetchedEventDto) },
  //       { $ref: getSchemaPath(YougetVideoFetchedEventDto) },
  //       { $ref: getSchemaPath(YougetOtherWebpageFetchedEventDto) },
  //       { $ref: getSchemaPath(YougetErrorEventDto) },
  //     ],
  //   },
  // })
  async handleFetchedEvent(
    @Query('snip_id') snipId: string,
    @Body() requestBody: WebhookFetchedRequestDto,
    @Req() _request: Request,
  ): Promise<void> {
    if (snipId) {
      await this.buildContext(snipId);
      WebhookController.logger.debug('handleFetchedEvent', snipId, requestBody);
      runInBackground(this.commandBus.execute(new WebhookFetchedCommand(snipId, requestBody)));
    }
  }

  @PublicRoute()
  @Post('resend')
  @HttpCode(200)
  @ApiOperation({
    summary: 'Resend 邮件服务回调',
    description: 'Resend 邮件服务回调',
  })
  @ApiResponse({ status: 200, description: 'OK' })
  async handleResendEvent(
    @Body() body: WebhookResendRequestDto,
    @Req() _request: Request,
  ): Promise<void> {
    console.log('handleResendEvent', body);
    const emails = body?.data?.to?.join(',');
    const subject = body?.data?.subject;

    if (body.type === 'email.delivery_delayed') {
      WebhookController.logger.error(`email.delivery_delayed/${subject}/${emails}`, 'warning');
    } else if (body.type === 'email.complained') {
      WebhookController.logger.error(`email.marked_as_spam/${subject}/${emails}`, 'error');
    } else if (body.type === 'email.rejected') {
      WebhookController.logger.error(`email.rejected/${subject}/${emails}`, 'error');
    }
    return;
  }
}
