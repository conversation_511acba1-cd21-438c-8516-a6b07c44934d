import { ApiProperty } from '@nestjs/swagger';
import { BoardItemDto } from './board.dto';
import { BoardGroupDto } from './board-group.dto';

export class BoardGroupDetailDto {
  @ApiProperty({
    description: 'Board group 详细信息',
    type: BoardGroupDto,
  })
  boardGroup: BoardGroupDto;

  @ApiProperty({
    description: 'Board group 下的所有 items',
    type: [BoardItemDto],
  })
  boardItems: BoardItemDto[];

  @ApiProperty({
    description: 'Snip 数量',
  })
  snipsCount: number;

  @ApiProperty({
    description: 'Thought 数量',
  })
  thoughtsCount: number;

  @ApiProperty({
    description: '子 Board group 数量',
  })
  boardGroupsCount: number;
}
