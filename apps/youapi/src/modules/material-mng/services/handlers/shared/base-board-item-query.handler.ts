import { Inject } from '@nestjs/common';
import {
  BoardItem,
  BoardItemType,
} from '@/modules/material-mng/domain/board-item/models/board-item.entity';
import { BoardItemDto } from '@/modules/material-mng/dto/board.dto';
import { BoardGroupDto } from '@/modules/material-mng/dto/board-group.dto';
import { SnipDto } from '@/modules/material-mng/dto/snip/snip.dto';
import { ThoughtDto } from '@/modules/material-mng/dto/thought.dto';
import { BoardGroupRepository } from '@/modules/material-mng/repositories/board-group.repository';
import { BoardItemRepository } from '@/modules/material-mng/repositories/board-item.repository';
import { SnipRepository } from '@/modules/material-mng/repositories/snip.repository';
import { ThoughtRepository } from '@/modules/material-mng/repositories/thought.repository';
import { BoardGroupDtoService } from '../../dto-services/board-group-dto.service';
import { SnipDtoService } from '../../dto-services/snip-dto.service';
import { ThoughtDtoService } from '../../dto-services/thought-dto.service';

/**
 * BoardItem 查询选项
 */
export interface BoardItemQueryOptions {
  /** 是否包含 Snip 的完整内容 */
  includeSnipContent?: boolean;
}

/**
 * BoardItem 查询结果
 */
export interface BoardItemQueryResult {
  /** 排序后的 board items */
  boardItems: BoardItemDto[];
  /** Snip 数量 */
  snipsCount: number;
  /** Thought 数量 */
  thoughtsCount: number;
  /** Board group 数量 */
  boardGroupsCount: number;
}

export abstract class BaseBoardItemQueryHandler {
  @Inject()
  protected readonly boardItemRepository: BoardItemRepository;

  @Inject()
  protected readonly boardGroupRepository: BoardGroupRepository;

  @Inject()
  protected readonly snipRepository: SnipRepository;

  @Inject()
  protected readonly thoughtRepository: ThoughtRepository;

  @Inject()
  protected readonly boardGroupDtoService: BoardGroupDtoService;

  @Inject()
  protected readonly snipDtoService: SnipDtoService;

  @Inject()
  protected readonly thoughtDtoService: ThoughtDtoService;

  /**
   * 处理 board items：获取实体数据、排序、转换为 DTO
   */
  protected async processBoardItems(
    boardItems: BoardItem[],
    spaceId: string,
    options: BoardItemQueryOptions,
  ): Promise<BoardItemQueryResult> {
    const { includeSnipContent = false } = options;

    // 1. 收集所有需要查询的实体 ID
    const snipIds: string[] = [];
    const thoughtIds: string[] = [];
    const boardGroupIds: string[] = [];

    boardItems.forEach((item) => {
      if (item.entityType === BoardItemType.SNIP) {
        snipIds.push(item.entityId);
      } else if (item.entityType === BoardItemType.THOUGHT) {
        thoughtIds.push(item.entityId);
      } else if (item.entityType === BoardItemType.BOARD_GROUP) {
        boardGroupIds.push(item.entityId);
      }
    });

    // 2. 并行获取所有实体数据
    const [snipDtos, thoughtDtos, childBoardGroups] = await Promise.all([
      this.getSnipDtos(snipIds, includeSnipContent, spaceId),
      this.getThoughtDtos(thoughtIds, spaceId),
      boardGroupIds.length > 0 ? this.boardGroupRepository.findByIds(boardGroupIds) : [],
    ]);

    const boardGroupDtos = this.boardGroupDtoService.toDtoList(childBoardGroups);

    // 3. 创建实体映射
    const snipMap = new Map(snipDtos.map((snip) => [snip.id!, snip]));
    const thoughtMap = new Map(thoughtDtos.map((thought) => [thought.id, thought]));
    const boardGroupMap = new Map(boardGroupDtos.map((group) => [group.id, group]));

    // 4. 排序 board items
    const sortedBoardItems = this.sortBoardItems(boardItems);

    // 5. 转换为 BoardItemDto
    const boardItemDtos = sortedBoardItems
      .map((item) => {
        let entity: SnipDto | ThoughtDto | BoardGroupDto | null = null;

        if (item.entityType === BoardItemType.SNIP) {
          entity = snipMap.get(item.entityId) || null;
        } else if (item.entityType === BoardItemType.THOUGHT) {
          entity = thoughtMap.get(item.entityId) || null;
        } else if (item.entityType === BoardItemType.BOARD_GROUP) {
          entity = boardGroupMap.get(item.entityId) || null;
        }

        if (!entity) {
          return null;
        }

        return <BoardItemDto>{
          id: item.id,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          boardId: item.boardId,
          parentBoardGroupId: item.parentBoardGroupId || null,
          rank: item.rank,
          snipId: item.entityType === BoardItemType.SNIP ? item.entityId : null,
          thoughtId: item.entityType === BoardItemType.THOUGHT ? item.entityId : null,
          chatId: item.entityType === BoardItemType.CHAT ? item.entityId : null,
          boardGroupId: item.entityType === BoardItemType.BOARD_GROUP ? item.entityId : null,
          entityType: item.entityType,
          entity,
        };
      })
      .filter((item): item is BoardItemDto => item !== null);

    return {
      boardItems: boardItemDtos,
      snipsCount: snipIds.length,
      thoughtsCount: thoughtIds.length,
      boardGroupsCount: boardGroupIds.length,
    };
  }

  private async getSnipDtos(
    snipIds: string[],
    includeSnipContent: boolean,
    spaceId: string,
  ): Promise<SnipDto[]> {
    if (snipIds.length === 0) {
      return [];
    }

    const snips = includeSnipContent
      ? await this.snipRepository.findByIds({
          ids: snipIds,
          spaceId,
          includeDeleted: false,
        })
      : await this.snipRepository.findByIdsWithContentTruncated({
          ids: snipIds,
          spaceId,
          includeDeleted: false,
        });

    return this.snipDtoService.toDtoList(snips);
  }

  private async getThoughtDtos(thoughtIds: string[], spaceId: string): Promise<ThoughtDto[]> {
    if (thoughtIds.length === 0) {
      return [];
    }

    const thoughts = await this.thoughtRepository.findByIds({
      spaceId,
      ids: thoughtIds,
    });

    return this.thoughtDtoService.toDtoList(thoughts);
  }

  /**
   * 对 board items 进行排序，并构建 boardGroupRankMap
   * @param boardItems 要排序的 board items
   * @returns 排序后的 board items
   */
  protected sortBoardItems(boardItems: BoardItem[]): BoardItem[] {
    // 构建 boardGroupRankMap
    const boardGroupRankMap = new Map<string, string>();
    boardItems
      .filter((item) => item.entityType === BoardItemType.BOARD_GROUP)
      .forEach((item) => {
        boardGroupRankMap.set(item.entityId, item.rank);
      });

    // 复制数组并排序
    const sortedItems = [...boardItems];
    sortedItems.sort((a, b) => this.compareBoardItems(a, b, boardGroupRankMap));

    return sortedItems;
  }

  /**
   * 按照 youapp 的排序逻辑进行 board items 排序
   * 实现层级排序：先按根级 rank 排序，再按子级 rank 排序
   */
  private compareBoardItems(
    a: BoardItem,
    b: BoardItem,
    boardGroupRankMap: Map<string, string>,
  ): number {
    // 如果在同一个父级分组内，直接比较 rank
    if (a.parentBoardGroupId === b.parentBoardGroupId) {
      return this.rankCompare(a.rank, b.rank);
    }

    // 不同层级的情况：需要找到各自的根级 rank 进行比较
    const aRootRank = this.findRootRank(a, boardGroupRankMap);
    const bRootRank = this.findRootRank(b, boardGroupRankMap);

    return this.rankCompare(aRootRank, bRootRank);
  }

  /**
   * 查找元素的根级 rank（递归查找到最顶层）
   * 使用 boardGroupRankMap 来高效查找父分组的 rank
   */
  private findRootRank(item: BoardItem, boardGroupRankMap: Map<string, string>): string {
    // 如果没有父分组，则自己就是根级
    if (!item.parentBoardGroupId) {
      return item.rank;
    }

    // 如果有父分组，返回父分组的 rank（这样会按父分组排序）
    const parentRank = boardGroupRankMap.get(item.parentBoardGroupId);
    return parentRank || item.rank; // 如果找不到父分组，使用自己的 rank
  }

  /**
   * 按照 youapp 的排序逻辑进行 rank 比较
   * 使用字符串的字典序比较
   */
  private rankCompare(a: string, b: string): number {
    return a < b ? -1 : a > b ? 1 : 0;
  }
}
