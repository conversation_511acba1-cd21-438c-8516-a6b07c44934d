import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { AuthorizationService } from '@/modules/iam/services/authorization.service';
import { BoardGroupDetailDto } from '@/modules/material-mng/dto/board-group-detail.dto';
import { GetBoardGroupDetailQuery } from '../../queries/board/get-board-group-detail.query';
import { BaseBoardItemQueryHandler } from '../shared/base-board-item-query.handler';

@QueryHandler(GetBoardGroupDetailQuery)
export class GetBoardGroupDetailHandler
  extends BaseBoardItemQueryHandler
  implements IQueryHandler<GetBoardGroupDetailQuery>
{
  @Inject()
  private readonly authorizationService: AuthorizationService;

  async execute(query: GetBoardGroupDetailQuery): Promise<BoardGroupDetailDto> {
    const { boardGroupId, options, spaceId } = query;

    // 1. 鉴权
    this.authorizationService.validateSpacePermission(spaceId);

    // 2. 获取 BoardGroup 信息
    const boardGroup = await this.boardGroupRepository.getById(boardGroupId);

    // 3. 获取 board items 并处理
    const boardItems = await this.boardItemRepository.listByParentBoardGroupId(boardGroupId);
    const result = await this.processBoardItems(boardItems, spaceId, options);

    // 4. 构建返回对象
    const boardGroupDto = this.boardGroupDtoService.toDto(boardGroup);

    return {
      boardGroup: boardGroupDto,
      boardItems: result.boardItems,
      snipsCount: result.snipsCount,
      thoughtsCount: result.thoughtsCount,
      boardGroupsCount: result.boardGroupsCount,
    };
  }
}
