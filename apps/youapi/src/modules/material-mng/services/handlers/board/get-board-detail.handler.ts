import { Inject } from '@nestjs/common';
import { I<PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { AuthorizationService } from '@/modules/iam/services/authorization.service';
import { BoardWithItemsDto } from '../../../dto/board.dto';
import { BoardRepository } from '../../../repositories/board.repository';
import { BoardDtoService } from '../../dto-services/board-dto.service';
import { GetBoardDetailQuery } from '../../queries/board/get-board-detail.query';
import { BaseBoardItemQueryHandler } from '../shared/base-board-item-query.handler';

// TODO: 需要添加 ChatRepository 来处理 chat 类型的 board items
// import { ChatRepository } from '../../../repositories/chat.repository';
// import { ChatDtoService } from '../../dto-services/chat-dto.service';

@QueryHandler(GetBoardDetailQuery)
export class GetBoardDetailHandler
  extends BaseBoardItemQueryHandler
  implements IQueryHandler<GetBoardDetailQuery>
{
  @Inject()
  private readonly boardRepository: BoardRepository;

  @Inject()
  private readonly boardDtoService: BoardDtoService;

  @Inject()
  private readonly authorizationService: AuthorizationService;

  async execute(query: GetBoardDetailQuery): Promise<BoardWithItemsDto> {
    const { boardId, options, spaceId } = query;

    // 1. 鉴权
    this.authorizationService.validateSpacePermission(spaceId);

    // 2. 获取 Board 信息
    const board = await this.boardRepository.getById(boardId);

    // 3. 获取 board items 并处理
    const boardItems = await this.boardItemRepository.listByBoardId(boardId);
    const result = await this.processBoardItems(boardItems, board.spaceId, options);

    // 4. 构建返回对象
    const boardDto = this.boardDtoService.toDto(board);

    return {
      ...boardDto,
      snipsCount: result.snipsCount,
      thoughtsCount: result.thoughtsCount,
      boardItems: result.boardItems,
    };
  }
}
