import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CommandBus, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { ContentFormatEnum } from '@repo/common';
import { of, Subject } from 'rxjs';
import { CompletionStreamChunk, CompletionStreamModeEnum, StreamChunkUnion } from '@/common/types';
import { TextRunnerService } from '@/modules/ai/runners';
import { BlockContent } from '@/modules/material-mng/domain/block/models';
import { Snip } from '@/modules/material-mng/domain/snip/models/snip.entity';
import { OnWebhookCompletionAction } from '@/modules/material-mng/dto/webhook/webhook-subtitle-transcribed.dto';
import { RunSnipActionCommand } from '../../../handlers/action/run-snip-action.handler';
import { BlockRepository } from '../../../repositories/block.repository';
import { SnipRepository } from '../../../repositories/snip.repository';
import { ExtractTranscriptCommand } from '../../commands/snip/extract-transcript.command';
import { GenerateTranscriptCommand } from '../../commands/snip/generate-transcript.command';
import { ProcessingException } from './call-overview.handler';

@CommandHandler(GenerateTranscriptCommand)
@Injectable()
export class GenerateTranscriptHandler implements ICommandHandler<GenerateTranscriptCommand> {
  private readonly logger = new Logger(GenerateTranscriptHandler.name);

  constructor(
    private readonly snipRepository: SnipRepository,
    private readonly blockRepository: BlockRepository,
    private readonly commandBus: CommandBus,
    private readonly textRunnerService: TextRunnerService,
  ) {}

  async execute(command: GenerateTranscriptCommand): Promise<any> {
    const { userId, spaceId, snipId, responseLanguage, speakerLabels, regenerate } = command;

    this.logger.log(`Generating transcript for snip ${snipId} by user ${userId}`);

    // 1. 验证 snip 存在且用户有权限
    const snip = await this.snipRepository.findById(snipId);
    if (!snip) {
      throw new NotFoundException(`Snip with id ${snipId} not found`);
    }

    // 验证 snip 属于用户的 space
    if (snip.spaceId !== spaceId) {
      throw new NotFoundException(`Snip with id ${snipId} not found in user's space`);
    }

    // 临时 handler 调用 RunSnipActionHandler 执行 transcript action
    const result = await this.commandBus.execute(
      new RunSnipActionCommand({
        action_name: 'transcript',
        snip_id: snipId,
        user_id: userId,
        language: responseLanguage,
        extra: {
          speaker_labels: speakerLabels,
          regenerate,
        },
      }),
    );

    const transcript = await this.getTranscriptContent(snip, userId);

    if (!transcript) {
      // content 应该为空
      await this.commandBus.execute(
        new ExtractTranscriptCommand(
          snipId,
          userId,
          spaceId,
          OnWebhookCompletionAction.CALL_OVERVIEW,
          '',
          responseLanguage,
          speakerLabels,
        ),
      );
      return of(null);
    }

    const subject = new Subject<CompletionStreamChunk<StreamChunkUnion>>();
    subject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Content',
      data: {
        id: '',
        block_id: '',
        status: '',
        format: '',
        raw: '',
        plain: '',
        language: '',
        trace_id: '',
      },
    });

    const runner = this.textRunnerService.getTranslateTranscriptRunner({
      transcript: transcript.raw,
      language: responseLanguage,
    });
    const observable = await runner.generateStream({ streamFormat: 'completion-stream' });

    let finalContent = '';
    observable.subscribe({
      next: (chunk) => {
        subject.next(chunk);
        if (chunk.mode === CompletionStreamModeEnum.APPEND_STRING) {
          finalContent += chunk.data;
        }
      },
      error: (error) => {
        subject.error(error);
      },
      complete: async () => {
        subject.next({
          mode: CompletionStreamModeEnum.INSERT,
          dataType: 'Content',
          data: {
            id: '',
            block_id: '',
            status: '',
            format: '',
            raw: '',
            plain: '',
            language: '',
            trace_id: '',
          },
        });
        subject.complete();
        // TODO: @muke 存一下新 content
      },
    });

    return subject.asObservable();
  }

  private async getTranscriptContent(snip: Snip, userId: string): Promise<BlockContent> {
    // 查询转录内容
    const transcripts = await this.blockRepository.queryTranscriptContentBySnip(snip.id);

    if (!transcripts.length) {
      // 触发转录提取
      const extractCommand = new ExtractTranscriptCommand(
        snip.id,
        userId,
        snip.spaceId,
        OnWebhookCompletionAction.CALL_OVERVIEW,
      );
      await this.commandBus.execute(extractCommand);
      throw new ProcessingException('Transcript is being generated, please try again later');
    }

    // 选择合适的转录内容：优先 SUBTITLE 格式，否则使用第一个
    return transcripts.find((t) => t.format === ContentFormatEnum.SUBTITLE) ?? transcripts[0];
  }
}
