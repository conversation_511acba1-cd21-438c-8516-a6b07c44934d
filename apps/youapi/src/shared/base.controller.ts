import { Inject, Logger, UseInterceptors } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { SpaceIdInterceptor } from '@/common/interceptors/space-id.interceptor';
import { YouapiClsService } from '@/common/services/cls.service';
import { ApplicationContext } from '@/common/utils/application-context';
import { SerializeInterceptor } from './serialize.interceptor';

@ApiBearerAuth()
@UseInterceptors(SerializeInterceptor, SpaceIdInterceptor)
@ApiResponse({
  status: 200,
  description: 'Success',
})
@ApiResponse({
  status: 400,
  description: 'Bad Request',
})
@ApiResponse({
  status: 401,
  description: 'Unauthorized',
})
@ApiResponse({
  status: 403,
  description: 'Forbidden',
})
@ApiResponse({
  status: 404,
  description: 'Not Found',
})
@ApiResponse({
  status: 500,
  description: 'Internal Server Error',
})
export abstract class BaseController {
  @Inject()
  protected readonly clsService: YouapiClsService;
  @Inject()
  protected readonly queryBus: QueryBus;
  @Inject()
  protected readonly commandBus: CommandBus;

  protected readonly logger = new Logger(this.constructor.name);

  protected findUserId(): string | undefined {
    return ApplicationContext.findUserId();
  }

  protected getUserId(): string {
    return ApplicationContext.getUserId();
  }

  protected async getSpaceId(): Promise<string> {
    return ApplicationContext.getSpaceId();
  }
}
