/** biome-ignore-all lint/complexity/noStaticOnlyClass: <工具类> */
import type { ModuleRef } from '@nestjs/core';
import { TestingModule } from '@nestjs/testing';
import * as otel from '@opentelemetry/api';
import { ProductTier } from '../../modules/iam/domain/subscription/models/subscription.types';
import { CreditAccountRepository } from '../../modules/iam/repositories/credit-account.repository';
import { SpaceRepository } from '../../modules/iam/repositories/space.repository';
import { YouapiClsService } from '../services/cls.service';

/**
 * 用于获取应用上下文中的服务实例
 */
export class ApplicationContext {
  private static moduleRef: ModuleRef | TestingModule;

  static register(ref: ModuleRef | TestingModule) {
    ApplicationContext.moduleRef = ref;
  }

  // biome-ignore lint/suspicious/noExplicitAny: <所有 provider 都可能被注入>
  static getProvider<T>(type: any): T {
    // Avoid circular JSON serialization by not logging the moduleRef object
    return ApplicationContext.moduleRef.get(type, { strict: false });
  }

  static findUserId(): string {
    const clsService = ApplicationContext.getProvider<YouapiClsService>(YouapiClsService);
    return clsService.getUserId();
  }

  static getUserId(): string {
    const userId = ApplicationContext.findUserId();
    if (!userId) {
      throw new Error('User ID not found in CLS context');
    }
    return userId;
  }

  /**
   * 获取当前用户的 spaceId
   * 复用现有的 CLS 缓存机制，避免重复查询
   */
  static async getSpaceId(): Promise<string> {
    const clsService = ApplicationContext.getProvider<YouapiClsService>(YouapiClsService);

    // 1. 尝试从 CLS 缓存获取
    let spaceId = clsService.getSpaceId();
    if (!spaceId) {
      // 2. 从 CLS 获取 userId (如果不存在会抛出异常)
      const userId = clsService.getUserId();
      if (!userId) {
        throw new Error('User ID not found in CLS context');
      }

      // 3. 通过 userId 查询 Space
      const spaceRepository = ApplicationContext.getProvider<SpaceRepository>(SpaceRepository);
      const space = await spaceRepository.getByCreatorId(userId);
      spaceId = space.id;
      // 4. 缓存到 CLS 中供后续使用
      clsService.setSpaceId(spaceId);
      clsService.setSpace(space);
    }

    return spaceId;
  }

  /**
   * 获取当前用户的 productTier
   * 复用现有的 CLS 缓存机制，避免重复查询
   */
  static async getProductTier(): Promise<ProductTier> {
    const clsService = ApplicationContext.getProvider<YouapiClsService>(YouapiClsService);

    // 1. 尝试从 CLS 缓存获取
    let productTier = clsService.getProductTier();
    if (!productTier) {
      // 2. 获取 spaceId（可能从缓存或数据库查询）
      const spaceId = await ApplicationContext.getSpaceId();

      // 3. 通过 spaceId 查询 CreditAccount
      const creditAccountRepository =
        ApplicationContext.getProvider<CreditAccountRepository>(CreditAccountRepository);
      const creditAccount = await creditAccountRepository.getBySpaceId(spaceId);
      productTier = creditAccount.productTier;
      // 4. 缓存到 CLS 中供后续使用
      clsService.setProductTier(productTier);
    }

    return productTier;
  }

  static getTraceId(): string | undefined {
    const clsService = ApplicationContext.getProvider<YouapiClsService>(YouapiClsService);
    let traceId = clsService.getTraceId();
    if (!traceId) {
      traceId = otel.trace.getActiveSpan()?.spanContext().traceId;
      clsService.setTraceId(traceId);
    }
    return traceId;
  }
}
