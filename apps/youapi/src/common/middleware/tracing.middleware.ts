/**
 * NestJS 追踪中间件
 * 从 youapp/src/middleware.ts 迁移而来的追踪逻辑
 * 适配到 NestJS Middleware 模式
 */

import { Injectable, type NestMiddleware } from '@nestjs/common';
import * as otel from '@opentelemetry/api';
import type { NextFunction, Request, Response } from 'express';
import { YouapiClsService } from '../services/cls.service';
import { type RichSpan } from '../tracing';

@Injectable()
export class TracingMiddleware implements NestMiddleware {
  constructor(private readonly clsService: YouapiClsService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const span = otel.trace.getActiveSpan() as RichSpan | undefined;
    const traceId = span?.spanContext().traceId;
    if (traceId) {
      // 将 traceId 存储到 CLS 中，供后续服务使用
      this.clsService.setTraceId(traceId);
      next();
    } else {
      // 如果没有活动 span，创建一个新的根 span 并在其 context 中执行后续操作
      const tracer = otel.trace.getTracer('youapi');
      const newSpan = tracer.startSpan('http.request', {
        attributes: {
          'http.url': req.url,
          'http.target': req.path,
          'http.method': req.method,
        },
      });
      const newTraceId = newSpan.spanContext().traceId;
      this.clsService.setTraceId(newTraceId);

      // 使用 context API 在新 span 的上下文中执行后续操作
      otel.context.with(otel.trace.setSpan(otel.context.active(), newSpan), () => {
        // 在响应结束时才结束 span
        res.on('finish', () => {
          newSpan.setAttributes({
            'http.status_code': res.statusCode,
          });
          newSpan.end();
        });

        next();
      });
    }
  }
}
